import mysql.connector
import sys

def try_connection(description, config):
    print(f"\n=== Testing {description} ===")
    print(f"Config: {config}")

    try:
        print("Connecting...")
        conn = mysql.connector.connect(**config)

        if conn.is_connected():
            print("✅ SUCCESS!")
            conn.close()
            return True
    except Exception as e:
        print(f"❌ FAILED: {e}")

    return False

# Try different configurations
configs = [
    {
        "description": "Connection with no database specified",
        "config": {
            "host": "localhost",
            "user": "giguser",
            "password": "Happiness1524!"
        }
    },
    {
        "description": "Connection with different auth plugin",
        "config": {
            "host": "localhost",
            "user": "giguser",
            "password": "Happiness1524!",
            "auth_plugin": "mysql_native_password"
        }
    },
    {
        "description": "Connection with different port",
        "config": {
            "host": "localhost",
            "port": 3306,
            "user": "giguser",
            "password": "Happiness1524!"
        }
    },
    {
        "description": "Connection with 127.0.0.1 instead of localhost",
        "config": {
            "host": "127.0.0.1",
            "user": "giguser",
            "password": "Happiness1524!"
        }
    },
    {
        "description": "Connection with root user",
        "config": {
            "host": "localhost",
            "user": "root",
            "password": "Happiness1524!"
        }
    },
    {
        "description": "Connection with empty password",
        "config": {
            "host": "localhost",
            "user": "giguser",
            "password": ""
        }
    },
    {
        "description": "Connection with root and empty password",
        "config": {
            "host": "localhost",
            "user": "root",
            "password": ""
        }
    }
]

success = False
for config in configs:
    if try_connection(config["description"], config["config"]):
        success = True
        print(f"\n✅ SUCCESS with {config['description']}!")
        break

if not success:
    print("\n❌ All connection attempts failed.")

sys.exit(0 if success else 1)
