import os
import subprocess
import sys
import webbrowser

def check_xampp():
    print("=== Checking for XAMPP installation ===")
    
    # Common XAMPP paths
    xampp_paths = [
        "C:\\xampp",
        "D:\\xampp",
        "E:\\xampp"
    ]
    
    xampp_path = None
    for path in xampp_paths:
        if os.path.exists(path):
            xampp_path = path
            print(f"✅ Found XAMPP installation at: {xampp_path}")
            break
    
    if not xampp_path:
        print("❌ XAMPP installation not found")
        print("Please install XAMPP or check if it's installed in a non-standard location")
        return False
    
    # Check if XAMPP control panel exists
    control_panel = os.path.join(xampp_path, "xampp-control.exe")
    if os.path.exists(control_panel):
        print(f"✅ Found XAMPP control panel at: {control_panel}")
        print("You can use this to start/stop MySQL")
    else:
        print("❌ XAMPP control panel not found")
    
    # Check if MySQL is installed with XAMPP
    mysql_path = os.path.join(xampp_path, "mysql")
    if os.path.exists(mysql_path):
        print(f"✅ Found MySQL installation at: {mysql_path}")
        
        # Check for MySQL client
        mysql_client = os.path.join(mysql_path, "bin", "mysql.exe")
        if os.path.exists(mysql_client):
            print(f"✅ Found MySQL client at: {mysql_client}")
        else:
            print("❌ MySQL client not found")
    else:
        print("❌ MySQL not found in XAMPP installation")
        return False
    
    # Check if phpMyAdmin is installed
    phpmyadmin_path = os.path.join(xampp_path, "phpMyAdmin")
    if os.path.exists(phpmyadmin_path):
        print(f"✅ Found phpMyAdmin at: {phpmyadmin_path}")
        
        # Check if we can open phpMyAdmin
        phpmyadmin_url = "http://localhost/phpmyadmin/"
        print(f"\nYou can access phpMyAdmin at: {phpmyadmin_url}")
        
        open_browser = input("Would you like to open phpMyAdmin in your browser? (y/n): ")
        if open_browser.lower() == 'y':
            webbrowser.open(phpmyadmin_url)
    else:
        print("❌ phpMyAdmin not found in XAMPP installation")
    
    # Try to connect to MySQL using the XAMPP MySQL client
    print("\n=== Trying to connect to MySQL using XAMPP's MySQL client ===")
    
    # Default XAMPP MySQL credentials
    credentials = [
        {"user": "root", "password": "", "description": "XAMPP default (root with no password)"},
        {"user": "giguser", "password": "Happiness1524!", "description": "giguser with password"},
        {"user": "root", "password": "Happiness1524!", "description": "root with password"}
    ]
    
    for cred in credentials:
        print(f"\nTrying to connect as {cred['description']}...")
        
        cmd = [mysql_client, f"-u{cred['user']}"]
        if cred['password']:
            cmd.append(f"-p{cred['password']}")
        
        cmd.extend(["-e", "SELECT 'Connection successful!' AS Result;"])
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Connection successful!")
                print(result.stdout)
                
                # Try to show databases
                print("\nListing databases...")
                cmd = [mysql_client, f"-u{cred['user']}"]
                if cred['password']:
                    cmd.append(f"-p{cred['password']}")
                
                cmd.extend(["-e", "SHOW DATABASES;"])
                
                db_result = subprocess.run(cmd, capture_output=True, text=True)
                if db_result.returncode == 0:
                    print(db_result.stdout)
                    
                    # Check if giggenius database exists
                    if "giggenius" in db_result.stdout.lower():
                        print("✅ Found giggenius database!")
                        
                        # Try to create giguser if it doesn't exist
                        print("\nTrying to create giguser if it doesn't exist...")
                        create_user_cmd = [
                            mysql_client, 
                            f"-u{cred['user']}"
                        ]
                        
                        if cred['password']:
                            create_user_cmd.append(f"-p{cred['password']}")
                        
                        create_user_sql = """
                        CREATE USER IF NOT EXISTS 'giguser'@'localhost' IDENTIFIED BY 'Happiness1524!';
                        CREATE USER IF NOT EXISTS 'giguser'@'%' IDENTIFIED BY 'Happiness1524!';
                        GRANT ALL PRIVILEGES ON giggenius.* TO 'giguser'@'localhost';
                        GRANT ALL PRIVILEGES ON giggenius.* TO 'giguser'@'%';
                        FLUSH PRIVILEGES;
                        SELECT User, Host FROM mysql.user WHERE User = 'giguser';
                        """
                        
                        create_user_cmd.extend(["-e", create_user_sql])
                        
                        user_result = subprocess.run(create_user_cmd, capture_output=True, text=True)
                        if user_result.returncode == 0:
                            print("✅ User creation/update successful!")
                            print(user_result.stdout)
                        else:
                            print(f"❌ Error creating/updating user: {user_result.stderr}")
                    else:
                        print("❌ giggenius database not found")
                        
                        # Ask if we should create the database
                        create_db = input("Would you like to create the giggenius database? (y/n): ")
                        if create_db.lower() == 'y':
                            print("\nCreating giggenius database...")
                            create_db_cmd = [
                                mysql_client, 
                                f"-u{cred['user']}"
                            ]
                            
                            if cred['password']:
                                create_db_cmd.append(f"-p{cred['password']}")
                            
                            create_db_cmd.extend(["-e", "CREATE DATABASE giggenius;"])
                            
                            db_create_result = subprocess.run(create_db_cmd, capture_output=True, text=True)
                            if db_create_result.returncode == 0:
                                print("✅ Database created successfully!")
                            else:
                                print(f"❌ Error creating database: {db_create_result.stderr}")
                else:
                    print(f"❌ Error listing databases: {db_result.stderr}")
                
                return True
            else:
                print(f"❌ Connection failed: {result.stderr}")
        except Exception as e:
            print(f"Error running MySQL client: {e}")
    
    print("\n❌ All connection attempts failed")
    
    print("\n=== Suggestions ===")
    print("1. Make sure XAMPP's MySQL service is running:")
    print("   - Open XAMPP Control Panel")
    print("   - Click 'Start' next to MySQL")
    print("2. Try accessing phpMyAdmin to manage your database")
    print("3. The default XAMPP MySQL credentials are:")
    print("   - Username: root")
    print("   - Password: (empty)")
    print("4. Check XAMPP's MySQL logs for errors:")
    print(f"   - {os.path.join(xampp_path, 'mysql', 'data', 'mysql_error.log')}")
    
    return False

if __name__ == "__main__":
    success = check_xampp()
    sys.exit(0 if success else 1)
