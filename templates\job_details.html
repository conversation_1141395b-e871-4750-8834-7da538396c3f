<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Details - GigGenius</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --accent-color: #3b82f6;
            --light-color: #f3f4f6;
            --dark-color: #1f2937;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
        }

        body {
            font-family: 'Poppins', sans-serif;
            color: var(--dark-color);
            background-color: #f9fafb;
        }

        .navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 15px 0;
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color);
            font-size: 1.5rem;
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background-color: white;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
            z-index: 1000;
            padding-top: 80px;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--dark-color);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .sidebar-menu a:hover, .sidebar-menu a.active {
            background-color: var(--light-color);
            color: var(--primary-color);
            border-left-color: var(--primary-color);
        }

        .sidebar-menu i {
            margin-right: 10px;
            font-size: 1.2rem;
            width: 20px;
            text-align: center;
        }

        .main-content {
            margin-left: 250px;
            padding: 80px 20px 20px;
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 15px 20px;
            font-weight: 600;
        }

        .card-body {
            padding: 20px;
        }

        .job-header {
            background-color: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            margin-bottom: 30px;
        }

        .job-title {
            font-weight: 700;
            margin-bottom: 10px;
        }

        .job-meta {
            color: #6b7280;
            margin-bottom: 20px;
        }

        .job-meta span {
            margin-right: 15px;
        }

        .job-meta i {
            margin-right: 5px;
        }

        .job-description {
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .job-details-section {
            margin-bottom: 30px;
        }

        .job-details-section h3 {
            font-weight: 600;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .detail-item {
            display: flex;
            margin-bottom: 10px;
        }

        .detail-label {
            width: 150px;
            font-weight: 500;
        }

        .detail-value {
            flex: 1;
        }

        .client-info {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .client-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 15px;
        }

        .client-name {
            font-weight: 600;
            margin-bottom: 0;
        }

        .client-company {
            color: #6b7280;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand ms-4" href="#">
                <i class="fas fa-bolt me-2"></i>
                GigGenius
            </a>
            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <a href="#" class="d-flex align-items-center text-dark text-decoration-none dropdown-toggle" id="userDropdown" data-bs-toggle="dropdown">
                        <img src="https://ui-avatars.com/api/?name={{ session.get('first_name', ' ')[0] }}{{ session.get('last_name', ' ')[0] }}&background=2563eb&color=fff" alt="User" class="rounded-circle me-2" style="width: 40px; height: 40px;">
                        <span>{{ session.get('first_name', '') }}</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i> My Profile</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i> Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="sidebar">
        <ul class="sidebar-menu">
            <li>
                <a href="{{ url_for('genius_page') }}">
                    <i class="fas fa-home"></i> Dashboard
                </a>
            </li>
            <li>
                <a href="#" class="active">
                    <i class="fas fa-briefcase"></i> Jobs
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-project-diagram"></i> Projects
                </a>
            </li>
            <li>
                <a href="{{ url_for('chat') }}">
                    <i class="fas fa-comments"></i> Messages
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-wallet"></i> Payments
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-user"></i> Profile
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-cog"></i> Settings
                </a>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <a href="{{ url_for('genius_page') }}" class="text-decoration-none text-dark">
                    <i class="fas fa-arrow-left me-2"></i> Back to Jobs
                </a>
            </div>

            <div class="job-header">
                <h1 class="job-title">{{ job.title }}</h1>
                <div class="job-meta">
                    <span><i class="far fa-calendar-alt"></i> Posted {{ job.created_at }}</span>
                    <span><i class="fas fa-tag"></i> {{ job.category }}</span>
                    {% if job.specialty %}<span><i class="fas fa-star"></i> {{ job.specialty }}</span>{% endif %}
                </div>

                <div class="client-info">
                    <img src="https://ui-avatars.com/api/?name={{ job.first_name[0] }}{{ job.last_name[0] }}&background=2563eb&color=fff" alt="Client" class="client-avatar">
                    <div>
                        <p class="client-name">{{ job.first_name }} {{ job.last_name }}</p>
                        <p class="client-company">{{ job.business_name }}</p>
                    </div>
                </div>

                <div class="d-flex justify-content-end">
                    {% if has_applied %}
                        <div class="btn-group">
                            <button class="btn btn-success" disabled>
                                <i class="fas fa-check me-2"></i> Applied
                            </button>
                            <button class="btn btn-danger" onclick="deleteApplication({{ job.id }})">
                                <i class="fas fa-trash me-2"></i> Delete Application
                            </button>
                        </div>
                    {% else %}
                        <button class="btn btn-primary" onclick="applyForJob({{ job.id }})">
                            <i class="fas fa-paper-plane me-2"></i> Apply Now
                        </button>
                    {% endif %}
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            Job Description
                        </div>
                        <div class="card-body">
                            <div class="job-description">
                                {{ job.description|safe }}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            Job Details
                        </div>
                        <div class="card-body">
                            <div class="job-details-section">
                                <div class="detail-item">
                                    <div class="detail-label">Project Size</div>
                                    <div class="detail-value">{{ job.project_size }}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Duration</div>
                                    <div class="detail-value">{{ job.duration }}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Budget</div>
                                    <div class="detail-value">
                                        {{ job.budget_amount }}
                                        {% if job.budget_type %}({{ job.budget_type }}){% endif %}
                                    </div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Job Type</div>
                                    <div class="detail-value">{{ job.job_type }}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Posted</div>
                                    <div class="detail-value">
                                        {{ job.created_at }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deleteApplication(jobId) {
            // Show confirmation dialog
            if (confirm('Are you sure you want to delete your application for this job?')) {
                // Make an AJAX request to delete the application
                fetch('/delete_application', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        job_id: jobId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Delete response:', data);
                    if (data.success) {
                        // Update the UI to show the apply button again
                        const btnGroup = document.querySelector('.btn-group');
                        btnGroup.innerHTML = `
                            <button class="btn btn-primary" onclick="applyForJob(${jobId})">
                                <i class="fas fa-paper-plane me-2"></i> Apply Now
                            </button>
                        `;

                        // Create and show a success toast
                        const successToast = document.createElement('div');
                        successToast.className = 'position-fixed bottom-0 end-0 p-3';
                        successToast.style.zIndex = '5';
                        successToast.innerHTML = `
                            <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
                                <div class="toast-header bg-success text-white">
                                    <strong class="me-auto">Success</strong>
                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                                </div>
                                <div class="toast-body">
                                    Application deleted successfully!
                                </div>
                            </div>
                        `;
                        document.body.appendChild(successToast);

                        // Remove toast after 3 seconds
                        setTimeout(() => {
                            successToast.remove();
                        }, 3000);

                        // Refresh the page after a short delay
                        setTimeout(() => {
                            window.location.reload();
                        }, 3500);
                    } else {
                        // Show error message
                        console.error('Delete error:', data.error);
                        alert('Error: ' + (data.error || 'An error occurred'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                });
            }
        }

        function applyForJob(jobId) {
            // Show confirmation dialog
            if (confirm('Are you sure you want to apply for this job?')) {
                // Make an AJAX request to submit the application
                fetch('/apply_for_job', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        job_id: jobId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Application response:', data);
                    if (data.success) {
                        // Update the apply button
                        const applyButton = document.querySelector('button[onclick="applyForJob(' + jobId + ')"]');
                        applyButton.innerHTML = '<i class="fas fa-check me-2"></i> Applied';
                        applyButton.disabled = true;
                        applyButton.classList.remove('btn-primary');
                        applyButton.classList.add('btn-success');

                        // Create and show a success toast
                        const successToast = document.createElement('div');
                        successToast.className = 'position-fixed bottom-0 end-0 p-3';
                        successToast.style.zIndex = '5';
                        successToast.innerHTML = `
                            <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
                                <div class="toast-header bg-success text-white">
                                    <strong class="me-auto">Success</strong>
                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                                </div>
                                <div class="toast-body">
                                    Application submitted successfully! ${data.application_id ? `(ID: ${data.application_id})` : ''}
                                </div>
                            </div>
                        `;
                        document.body.appendChild(successToast);

                        // Remove toast after 3 seconds
                        setTimeout(() => {
                            successToast.remove();
                        }, 3000);

                        // Show auto message from client if available
                        if (data.auto_message) {
                            setTimeout(() => {
                                const messageToast = document.createElement('div');
                                messageToast.className = 'position-fixed bottom-0 end-0 p-3';
                                messageToast.style.zIndex = '5';
                                messageToast.innerHTML = `
                                    <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
                                        <div class="toast-header bg-primary text-white">
                                            <strong class="me-auto">Message from ${data.client_name || 'Client'}</strong>
                                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                                        </div>
                                        <div class="toast-body">
                                            ${data.auto_message}
                                            <div class="mt-2 pt-2 border-top">
                                                <a href="/chat" class="btn btn-sm btn-primary">Reply</a>
                                            </div>
                                        </div>
                                    </div>
                                `;
                                document.body.appendChild(messageToast);

                                // Remove message toast after 6 seconds
                                setTimeout(() => {
                                    messageToast.remove();
                                }, 6000);
                            }, 3500); // Show after the success toast is gone
                        }
                    } else {
                        // Show error message
                        console.error('Application error:', data.error);
                        alert('Error: ' + (data.error || 'An error occurred'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                });
            }
        }
    </script>
</body>
</html>
