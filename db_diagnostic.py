import mysql.connector
import sys
import socket
import platform
import os

def print_system_info():
    print("=== System Information ===")
    print(f"Platform: {platform.platform()}")
    print(f"Python version: {platform.python_version()}")
    print(f"Hostname: {socket.gethostname()}")
    try:
        print(f"IP Address: {socket.gethostbyname(socket.gethostname())}")
    except:
        print("IP Address: Unable to determine")
    print()

def test_mysql_connection(config, description):
    print(f"=== Testing {description} ===")
    print(f"Connection parameters: {config}")
    
    try:
        # Try to connect
        print("Attempting to connect...")
        conn = mysql.connector.connect(**config)
        
        if conn.is_connected():
            print("✅ CONNECTION SUCCESSFUL!")
            
            # Get server information
            db_info = conn.get_server_info()
            print(f"MySQL Server version: {db_info}")
            
            # Get cursor
            cursor = conn.cursor()
            
            # Check current database
            if 'database' in config:
                cursor.execute("SELECT DATABASE();")
                db_name = cursor.fetchone()[0]
                print(f"Connected to database: {db_name}")
            
            # List available databases
            print("\nAvailable databases:")
            cursor.execute("SHOW DATABASES;")
            dbs = cursor.fetchall()
            for db in dbs:
                print(f"- {db[0]}")
            
            # If connected to a specific database, list tables
            if 'database' in config:
                print(f"\nTables in {config['database']}:")
                try:
                    cursor.execute("SHOW TABLES;")
                    tables = cursor.fetchall()
                    if tables:
                        for table in tables:
                            print(f"- {table[0]}")
                    else:
                        print("No tables found")
                except mysql.connector.Error as e:
                    print(f"Error listing tables: {e}")
            
            # Close connections
            cursor.close()
            conn.close()
            print("\nConnection closed properly")
            return True
            
    except mysql.connector.Error as e:
        print(f"❌ CONNECTION FAILED: {e}")
        
        # Provide more specific error information
        if "Access denied" in str(e):
            print("\nPossible causes:")
            print("1. Incorrect username or password")
            print("2. User doesn't have permission to access from your current host")
            print("3. User exists but doesn't have the necessary privileges")
            
        elif "Unknown database" in str(e):
            print("\nPossible causes:")
            print("1. The database doesn't exist")
            print("2. The database name is misspelled or case is incorrect")
            print("3. The user doesn't have access to this database")
            
        elif "Can't connect to MySQL server" in str(e):
            print("\nPossible causes:")
            print("1. MySQL server is not running")
            print("2. MySQL server is not accepting connections on the specified host/port")
            print("3. Firewall is blocking the connection")
            
        return False

def main():
    print_system_info()
    
    # Test connection configurations
    configs = [
        {
            "description": "Connection with giguser",
            "config": {
                'host': 'localhost',
                'user': 'giguser',
                'password': 'Happiness1524!',
                'database': 'giggenius'
            }
        },
        {
            "description": "Connection with giguser without specifying database",
            "config": {
                'host': 'localhost',
                'user': 'giguser',
                'password': 'Happiness1524!'
            }
        },
        {
            "description": "Connection with root user",
            "config": {
                'host': 'localhost',
                'user': 'root',
                'password': 'Happiness1524!',
                'database': 'giggenius'
            }
        },
        {
            "description": "Connection with root user without specifying database",
            "config": {
                'host': 'localhost',
                'user': 'root',
                'password': 'Happiness1524!'
            }
        },
        {
            "description": "Connection using 127.0.0.1 instead of localhost",
            "config": {
                'host': '127.0.0.1',
                'user': 'giguser',
                'password': 'Happiness1524!',
                'database': 'giggenius'
            }
        }
    ]
    
    success_count = 0
    
    for test in configs:
        print("\n" + "="*50 + "\n")
        if test_mysql_connection(test["config"], test["description"]):
            success_count += 1
    
    print("\n" + "="*50)
    print(f"\nResults: {success_count} successful connections out of {len(configs)} attempts.")
    
    if success_count > 0:
        return 0
    else:
        return 1

if __name__ == "__main__":
    sys.exit(main())
