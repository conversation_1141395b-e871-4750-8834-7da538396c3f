"""
This script tries to connect to MySQL using PyMySQL instead of mysql-connector.
You'll need to install PyMySQL first:

pip install pymysql

"""

try:
    import pymysql
    print("PyMySQL is installed.")
except ImportError:
    print("PyMySQL is not installed. Please run: pip install pymysql")
    import sys
    sys.exit(1)

def try_connection():
    print("\n=== Testing connection with PyMySQL ===")
    
    # Configuration
    config = {
        "host": "localhost",
        "user": "giguser",
        "password": "Happiness1524!",
        "database": "giggenius"
    }
    
    print(f"Config: {config}")
    
    try:
        print("Connecting...")
        conn = pymysql.connect(**config)
        
        if conn:
            print("✅ SUCCESS!")
            
            # Get cursor
            cursor = conn.cursor()
            
            # Check database
            cursor.execute("SELECT DATABASE();")
            db_name = cursor.fetchone()[0]
            print(f"Connected to database: {db_name}")
            
            # List tables
            cursor.execute("SHOW TABLES;")
            tables = cursor.fetchall()
            
            if tables:
                print("\nTables in the database:")
                for table in tables:
                    print(f"- {table[0]}")
            else:
                print("\nNo tables found in the database.")
            
            cursor.close()
            conn.close()
            return True
    except Exception as e:
        print(f"❌ FAILED: {e}")
    
    return False

if __name__ == "__main__":
    success = try_connection()
    import sys
    sys.exit(0 if success else 1)
