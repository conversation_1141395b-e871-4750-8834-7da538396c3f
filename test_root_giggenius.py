import mysql.connector
import sys

# Try to connect to GigGenius database with root user
def test_connection():
    # Try different possible root passwords
    passwords = [
        "",  # Empty password
        "root",  # Common default
        "admin",  # Common default
        "mysql",  # Common default
        "password",  # Common default
        "Happiness1524!",  # Your specified password
    ]
    
    for password in passwords:
        try:
            print(f"\nTrying to connect with root user and password: '{password}'")
            conn = mysql.connector.connect(
                host="localhost",
                user="root",
                password=password,
                database="GigGenius"  # Note the capitalization
            )
            
            if conn.is_connected():
                print("✅ SUCCESS! Connected to GigGenius database with root user")
                db_info = conn.get_server_info()
                print(f"MySQL Server version: {db_info}")
                
                cursor = conn.cursor()
                cursor.execute("SELECT DATABASE();")
                db_name = cursor.fetchone()[0]
                print(f"Connected to database: {db_name}")
                
                # Check for tables
                cursor.execute("SHOW TABLES;")
                tables = cursor.fetchall()
                
                if tables:
                    print("\nTables in the database:")
                    for table in tables:
                        print(f"- {table[0]}")
                else:
                    print("\nNo tables found in the database yet.")
                
                cursor.close()
                conn.close()
                
                print("\nSUCCESS! Use these connection parameters in your app.py:")
                print("    'host': 'localhost',")
                print("    'user': 'root',")
                print(f"    'password': '{password}',")
                print("    'database': 'GigGenius'")
                
                return True
                
        except mysql.connector.Error as e:
            print(f"❌ Failed: {e}")
    
    print("\n❌ All connection attempts failed.")
    return False

if __name__ == "__main__":
    success = test_connection()
    sys.exit(0 if success else 1)
