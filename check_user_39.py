#!/usr/bin/env python3
import mysql.connector
from mysql.connector import <PERSON><PERSON><PERSON>

def check_user_39():
    try:
        # Database connection
        connection = mysql.connector.connect(
            host='localhost',
            database='giggenius',
            user='root',
            password='Happiness1524!'
        )
        
        if connection.is_connected():
            cursor = connection.cursor(dictionary=True)
            
            print("=== Checking User ID 39 ===")
            
            # Check in register_genius table
            print("\n1. Checking register_genius table:")
            cursor.execute("""
                SELECT id, first_name, last_name, email, is_pwd, pwd_approval_status, 
                       commission_rate, pwd_condition, 
                       CASE WHEN pwd_proof IS NOT NULL THEN 'Has proof' ELSE 'No proof' END as proof_status
                FROM register_genius 
                WHERE id = 39
            """)
            register_result = cursor.fetchone()
            
            if register_result:
                print(f"   Found in register_genius:")
                for key, value in register_result.items():
                    print(f"   {key}: {value}")
            else:
                print("   Not found in register_genius")
            
            # Check in approve_genius table
            print("\n2. Checking approve_genius table:")
            cursor.execute("""
                SELECT id, first_name, last_name, email, is_pwd, pwd_approval_status, 
                       commission_rate
                FROM approve_genius 
                WHERE id = 39
            """)
            approve_result = cursor.fetchone()
            
            if approve_result:
                print(f"   Found in approve_genius:")
                for key, value in approve_result.items():
                    print(f"   {key}: {value}")
            else:
                print("   Not found in approve_genius")
            
            # Check all users with name containing 'carla'
            print("\n3. Checking all users with name 'carla':")
            cursor.execute("""
                SELECT 'register_genius' as table_name, id, first_name, last_name, email, is_pwd, pwd_approval_status
                FROM register_genius 
                WHERE first_name LIKE '%carla%' OR last_name LIKE '%carla%'
                UNION ALL
                SELECT 'approve_genius' as table_name, id, first_name, last_name, email, is_pwd, pwd_approval_status
                FROM approve_genius 
                WHERE first_name LIKE '%carla%' OR last_name LIKE '%carla%'
            """)
            carla_results = cursor.fetchall()
            
            if carla_results:
                for result in carla_results:
                    print(f"   {result['table_name']}: ID {result['id']} - {result['first_name']} {result['last_name']} - PWD: {result['is_pwd']} - Status: {result['pwd_approval_status']}")
            else:
                print("   No users named 'carla' found")
                
    except Error as e:
        print(f"Error: {e}")
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == "__main__":
    check_user_39()
