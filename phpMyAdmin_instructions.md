# Setting Up GigGenius User in phpMyAdmin

Follow these steps to properly set up the GigGenius user with the correct privileges:

1. Open phpMyAdmin in your browser
2. Click on the "SQL" tab at the top of the page
3. Copy and paste the following SQL commands into the SQL query box:

```sql
-- Drop the user if it exists to start fresh
DROP USER IF EXISTS 'GigGenius'@'localhost';

-- Create the user with the correct password
CREATE USER 'GigGenius'@'localhost' IDENTIFIED BY 'Happiness1524!';

-- Grant all privileges on the giggenius database to the user
GRANT ALL PRIVILEGES ON giggenius.* TO 'GigGenius'@'localhost';

-- Flush privileges to apply changes
FLUSH PRIVILEGES;

-- Show the user to verify
SELECT User, Host FROM mysql.user WHERE User = 'GigGenius';

-- Show the grants for the user to verify
SHOW GRANTS FOR 'GigGenius'@'localhost';
```

4. Click the "Go" button to execute the SQL commands
5. Verify that the user was created and has the correct privileges

## Alternative Method: Using MySQL Command Line

If you prefer to use the MySQL command line, you can run these commands:

1. Open a command prompt or terminal
2. Log in to MySQL as the root user:
   ```
   mysql -u root -p
   ```
3. Enter your root password when prompted
4. Run the same SQL commands as above
5. Exit MySQL by typing `exit`

## Testing the Connection

After setting up the user, run the test_db_connection.py script to verify that the connection works:

```
python test_db_connection.py
```

You should see a successful connection message if everything is set up correctly.
