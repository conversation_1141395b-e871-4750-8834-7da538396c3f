<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-store, no-cache, must-revalidate, post-check=0, pre-check=0, max-age=0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="-1">
    <title>Genius Page</title>
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/logo.png') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/optimized-job-cards.css') }}" rel="stylesheet">
    <style>

        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            /* Removed transition for better performance */
        }

        body {
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        /* Container Styles */
        .container {
            max-width: 2000px;
            margin: auto;
            padding: auto;
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0rem 1rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 5.5rem;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.7rem;
            font-weight: bold;
            color: var(--primary-pink);
            margin-right: 1rem;
        }

        .logo img {
            width: 5rem;
            height: 5rem;
        }

        .logo h1 {
            font-size: 1.7rem;
            font-weight: bold;
            margin-left: -0.5rem;
        }

        .nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            padding: 0.5rem 1rem;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .nav-links a:hover, .nav-links a.active {
            color: var(--primary-pink);
        }

        .nav-dropdown {
            position: relative;
            display: inline-block;
        }

        .nav-dropbtn {
            font-weight: 500;
            font-size: 1.2rem;
            color: var(--primary-blue);
            background: none;
            border: none;
            padding: 0.5rem 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-dropbtn:hover {
            color: var(--primary-pink);
        }

        .nav-dropdown-content {
            display: none;
            position: absolute;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            top: 100%;
            left: 0;
        }

        .nav-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            font-size: 1rem;
        }

        .nav-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: var(--primary-pink);
        }

        .nav-dropdown:hover .nav-dropdown-content {
            display: block;
        }

        /* Right section container */
        .right-section {
            display: flex;
            align-items: center;
            gap: 2rem; /* Increased gap between search and auth buttons */
        }

        /* Search container */
        .search-container {
            display: flex;
            align-items: center;
            margin-right: 1rem; /* Added margin after search container */
        }

        .search-type-select {
            position: relative;
            height: 100%;
        }

        .search-type-button {
            height: 50px;
            background: white;
            border: 2px solid var(--primary-blue);
            border-right: none;
            border-radius: 8px 0 0 8px;
            padding: 0 1rem;
            color: var(--primary-blue);
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
        }

        .search-type-button:hover {
            color: var(--primary-pink);
            border-color: var(--primary-pink);
        }

        .search-type-dropdown {
            position: absolute;
            top: 55px;
            left: 0;
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 8px;
            margin-top: 0.5rem;
            min-width: 120px;
            display: none;
            z-index: 1000;
            width: max-content;
        }

        .search-type-dropdown.active {
            display: block;
        }

        .search-type-option {
            padding: 1rem 1.5rem;
            cursor: pointer;
            color: var(--primary-blue);
        }

        .search-type-option:hover {
            background: #f5f5f5;
            color: var(--primary-pink);
        }

        .search-bar {
            height: 50px;
            display: flex;
            align-items: center;
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 0 8px 8px 0;
            width: 350px;
        }

        .search-bar:hover {
            border-color: var(--primary-pink);
        }

        .search-bar input {
            border: none;
            outline: none;
            padding: 0 1rem;
            width: 100%;
            height: 100%;
            font-size: 1rem;
        }

        .search-bar .icon {
            color: var(--primary-blue);
            padding: 0 0.5rem;
            font-size: 1rem;
        }

        .search-bar:hover .icon {
            color: var(--primary-pink);
        }


        /* Auth buttons container */
        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 1.5rem; /* Increased gap between notification and profile */
        }

        /* Notification icon */
        .notification-icon {
            position: relative;
            cursor: pointer;
            padding: 0.5rem; /* Added padding for better click area */
        }

        .notification-icon i {
            font-size: 1.8rem; /* Slightly larger icon */
            color: var(--primary-blue);
            /* Removed transition */
        }

        /* Removed hover effect */

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: var(--primary-pink);
            color: white;
            border-radius: 50%;
            padding: 0.2rem 0.5rem;
            font-size: 0.8rem;
            min-width: 20px; /* Slightly larger badge */
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Profile button */
        .profile-button {
            width: 52px;
            height: 52px;
            border-radius: 50%;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid var(--primary-blue);
            transition: all 0.3s ease;
            position: relative;
        }

        .profile-button:hover {
            transform: scale(1.05);
            border-color: var(--primary-pink);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .profile-button:active {
            transform: scale(0.95);
        }

        /* Removed hover effect */

        .profile-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            max-width: 52px;
            max-height: 52px;
        }

        /* Profile dropdown */
        .profile-dropdown {
            position: relative;
            display: inline-block;
        }

        .profile-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 60px;
            background-color: #fff;
            min-width: 220px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            border: 1px solid rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .profile-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1rem;
            /* Removed transition */
        }

        .profile-dropdown-content a i {
            width: 20px;
            text-align: center;
        }

        .profile-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: var(--primary-pink);
        }

        .dropdown-divider {
            height: 1px;
            background-color: #eee;
            margin: 8px 0;
        }

        .logout-option {
            color: #dc3545 !important;
        }

        .logout-option:hover {
            background-color: #fff5f5 !important;
            color: #dc3545 !important;
        }

        /* Show dropdown on click */
        .profile-dropdown.active .profile-dropdown-content {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }


        /* Footer Styles */
        footer {
            background: #0047AB; /* Primary blue color */
            padding: 2rem 5%;
            color: white;
        }

        footer .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        footer h3 {
            color: white;
            margin-bottom: 1.5rem;
            font-size: 1.1rem;
            font-weight: 600;
        }

        footer a {
            color: white;
            text-decoration: none;
            display: block;
            margin-bottom: 0.75rem;
            font-size: 0.9rem;
            /* Removed transition */
        }

        /* Removed hover effect */

        .footer-content {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 2rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 2rem;
        }

        .footer-bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .social-links {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .social-links span {
            margin-right: 0.5rem;
        }

        .footer-links {
            display: flex;
            gap: 1.5rem;
        }

        @media (max-width: 768px) {
            .footer-content {
                grid-template-columns: repeat(2, 1fr);
            }

            .footer-bottom {
                flex-direction: column;
                text-align: center;
            }

            .footer-links {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        .mobile-menu-btn {
            background: none;
            border: none;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .mobile-menu-btn:hover {
            background-color: #f3f4f6;
        }

        .mobile-menu {
            display: none;
            position: absolute;
            top: 60px;
            right: 1rem;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            width: 200px;
            z-index: 20;
            border: 1px solid #e5e7eb;
        }

        .mobile-menu.active {
            display: block;
        }

        .mobile-menu a {
            display: block;
            padding: 0.75rem 1rem;
            color: #333;
            font-size: 0.875rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .mobile-menu a:last-child {
            border-bottom: none;
        }

        .mobile-menu a:hover {
            background-color: #f9fafb;
        }

        /* Main Content */
        .main-content {
            padding: 1.5rem 1rem 3rem;
        }

        /* Profile Section */
        .profile-section {
            padding: 2rem 0;
            position: relative;
        }

        .profile-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB4PSIwIiB5PSIwIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSgzMCkiPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIyIiBoZWlnaHQ9IjIiIGZpbGw9IiMwMDRBQUQiIG9wYWNpdHk9IjAuMDMiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjcGF0dGVybikiLz48L3N2Zz4=');
            opacity: 0.5;
            z-index: -1;
        }

        .profile-card {
            background: linear-gradient(to bottom right, #ffffff, #f8f9ff);
            border-radius: 16px;
            border: 1px solid rgba(0, 74, 173, 0.1);
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.03);
            position: relative;
            /* Removed transition */
        }

        .profile-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #004AAD, #CD208B);
            opacity: 0.7;
        }

        /* Removed hover effect */

        .profile-content {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 1.5rem;
            padding: 1.5rem;
            position: relative;
        }

        .profile-info {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 1.5rem;
            width: 100%;
        }

        .profile-avatar-container {
            position: relative;
            flex-shrink: 0;
        }

        .profile-avatar {
            width: 110px;
            height: 110px;
            border-radius: 50%;
            border: 2px solid rgba(0, 74, 173, 0.1);
            overflow: hidden;
            flex-shrink: 0;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
            /* Removed transition */
            position: relative;
            z-index: 1;
            max-width: 110px;
            max-height: 110px;
        }

        .profile-avatar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            box-shadow: inset 0 0 0 3px rgba(205, 32, 139, 0.1);
            z-index: 1;
        }

        /* Removed hover effect */

        .profile-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            /* Removed transition */
            max-width: 110px;
            max-height: 110px;
        }

        .status-indicator {
            position: absolute;
            bottom: 8px;
            right: 8px;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            border: 2px solid white;
            z-index: 2;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .status-indicator.online {
            background-color: #2ecc71;
        }

        .status-indicator.away {
            background-color: #f39c12;
        }

        .status-indicator.offline {
            background-color: #e74c3c;
        }

        .availability-badge {
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(90deg, #2ecc71, #27ae60);
            color: white;
            padding: 0.3rem 0.9rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 600;
            box-shadow: 0 3px 8px rgba(46, 204, 113, 0.3);
            z-index: 2;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 0.3rem;
            /* Removed transition */
        }

        /* Removed hover effect */

        .profile-details {
            flex-grow: 1;
            position: relative;
        }

        .profile-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .profile-name {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            position: relative;
            display: inline-block;
        }

        .profile-name::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 40px;
            height: 3px;
            background: linear-gradient(90deg, #004AAD, #CD208B);
            border-radius: 3px;
            opacity: 0.7;
        }

        .profile-name span {
            background: linear-gradient(90deg, #CD208B, #e83a9c);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 700;
        }

        .profile-rating {
            display: flex;
            align-items: center;
            gap: 0.2rem;
            background-color: rgba(0, 74, 173, 0.05);
            padding: 0.5rem 0.8rem;
            border-radius: 50px;
            /* Removed transition */
        }

        /* Removed hover effect */

        .profile-rating i {
            color: #f1c40f;
            font-size: 0.9rem;
        }

        .profile-rating span {
            margin-left: 0.3rem;
            font-weight: 600;
            color: #555;
            font-size: 0.9rem;
        }

        .profile-meta {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 1rem;
            color: #555;
            font-size: 0.95rem;
        }

        .profile-meta-item {
            display: flex;
            align-items: center;
            gap: 0.4rem;
            padding: 0.35rem 0.75rem;
            background-color: rgba(0, 74, 173, 0.05);
            border-radius: 50px;
            /* Removed transition */
        }

        /* Removed hover effect */

        .profile-meta-item i {
            color: #004AAD;
            font-size: 1rem;
        }

        .profile-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            margin-top: 1.5rem;
        }

        .profile-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.4rem;
            padding: 0.4rem 0.9rem;
            font-size: 0.85rem;
            font-weight: 500;
            border-radius: 50px;
            cursor: pointer;
            /* Removed transition */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .profile-btn i {
            font-size: 0.9rem;
            /* Removed transition */
        }

        .btn-outline {
            background-color: white;
            border: 1px solid #e0e0e0;
            color: #555;
        }

        /* Removed hover effects */

        .btn-outline.active {
            background: linear-gradient(90deg, #004AAD, #0066e8);
            color: white;
            border: none;
            box-shadow: 0 5px 15px rgba(0, 74, 173, 0.2);
        }

        /* Removed hover effect */

        /* Stats Grid */
        .stats-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 1.25rem;
            margin-top: 1.5rem;
            position: relative;
        }

        .stat-card {
            flex: 1;
            min-width: calc(33.333% - 1rem);
            max-width: calc(33.333% - 1rem);
        }

        @media (max-width: 768px) {
            .stat-card {
                min-width: calc(50% - 0.625rem);
                max-width: calc(50% - 0.625rem);
            }
        }

        .stat-card {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.75rem 1.5rem;
            text-align: center;
            background: linear-gradient(to bottom right, #ffffff, #f8f9ff);
            border-radius: 16px;
            border: 1px solid rgba(0, 74, 173, 0.1);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.03);
            /* Removed transition */
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #004AAD, #CD208B);
            opacity: 0.7;
            /* Removed transform and transition */
        }

        /* Removed hover effects */

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(90deg, #004AAD, #CD208B);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
            margin-bottom: 0.5rem;
            position: relative;
            display: inline-block;
        }

        .stat-value::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 2px;
            background: linear-gradient(90deg, #004AAD, #CD208B);
            border-radius: 2px;
            opacity: 0.5;
        }

        .stat-label {
            font-size: 0.95rem;
            color: #555;
            font-weight: 500;
            margin-top: 0.5rem;
            /* Removed transition */
        }

        /* Removed hover effect */

        .stat-icon {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 1.2rem;
            color: rgba(0, 74, 173, 0.15);
            /* Removed transition */
        }

        /* Removed hover effect */

        /* Introduction Section */
        .introduction-section {
            margin-top: 2rem;
            padding: 2rem;
            background: linear-gradient(to bottom right, #ffffff, #f8f9ff);
            border-radius: 16px;
            border: 1px solid rgba(0, 74, 173, 0.1);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.03);
            position: relative;
            overflow: hidden;
            /* Removed transition */
        }

        .introduction-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(to bottom, #004AAD, #CD208B);
            opacity: 0.7;
        }

        /* Removed hover effect */

        .introduction-section h3 {
            font-size: 1.35rem;
            font-weight: 600;
            margin-bottom: 1.25rem;
            color: #333;
            position: relative;
            padding-left: 15px;
        }

        .introduction-section h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 20px;
            background: linear-gradient(to bottom, #004AAD, #CD208B);
            border-radius: 3px;
        }

        .text-wrapper {
            display: flex;
            align-items: flex-end;
            position: relative;
        }

        .truncated-text {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            margin: 0;
            line-height: 1.7;
            color: #555;
            font-size: 1rem;
            background-clip: text;
            -webkit-background-clip: text;
        }

        .show-more-btn {
            color: #004AAD;
            font-weight: 600;
            white-space: nowrap;
            margin-left: 0.5rem;
            padding: 0.25rem 0.75rem;
            background-color: rgba(0, 74, 173, 0.08);
            border-radius: 50px;
            transition: all 0.2s ease;
        }

        .show-more-btn:hover {
            background-color: rgba(0, 74, 173, 0.15);
            transform: translateY(-2px);
        }

        /* Quote Slider */
        .quote-slider {
            margin: 2.5rem 0;
            padding: 3rem 2rem;
            background: linear-gradient(135deg, rgba(0, 74, 173, 0.03) 0%, rgba(205, 32, 139, 0.03) 100%);
            border-radius: 16px;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 74, 173, 0.08);
        }

        .quote-slider::before {
            content: '"';
            position: absolute;
            top: 20px;
            left: 30px;
            font-size: 120px;
            font-family: Georgia, serif;
            color: rgba(0, 74, 173, 0.07);
            line-height: 1;
        }

        .quote-slider::after {
            content: '"';
            position: absolute;
            bottom: 20px;
            right: 30px;
            font-size: 120px;
            font-family: Georgia, serif;
            color: rgba(205, 32, 139, 0.07);
            line-height: 1;
        }

        .quote-navigation {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 3rem;
            margin-top: 2rem;
        }

        .quote-arrow {
            font-size: 1.2rem;
            color: #004AAD;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: white;
            border-radius: 50%;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 74, 173, 0.1);
        }

        .quote-arrow:hover {
            transform: scale(1.1) translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
            color: #CD208B;
        }

        .quote-arrow:active {
            transform: scale(0.95);
        }

        .quote {
            font-size: 1.8rem;
            font-weight: 600;
            line-height: 1.5;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            padding: 0 2rem;
        }

        .main-text {
            display: block;
            background: linear-gradient(90deg, #004AAD, #0066e8);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }

        .sub-text {
            display: block;
            background: linear-gradient(90deg, #CD208B, #e83a9c);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-top: 1rem;
            font-size: 1.5rem;
        }

        .quote.fade {
            opacity: 0;
            transform: translateY(20px);
        }

        .quote-dots {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }

        .quote-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: rgba(0, 74, 173, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .quote-dot.active {
            background-color: #004AAD;
            transform: scale(1.3);
        }

        /* Dashboard Layout */
        .dashboard-content {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        /* Filters Sidebar */
        .filters {
            background-color: white;
            border-radius: 16px;
            border: 1px solid #eaedf2;
            padding: 1.75rem;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }

        .filters::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #004AAD, #CD208B);
            opacity: 0.7;
        }

        .filters-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            position: relative;
        }

        .filters-header h2 {
            font-size: 1.35rem;
            font-weight: 600;
            color: #333;
            position: relative;
            padding-left: 12px;
        }

        .filters-header h2::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 20px;
            background: linear-gradient(to bottom, #004AAD, #CD208B);
            border-radius: 4px;
        }

        .clear-filters-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            background-color: #f5f5f5;
            border: none;
            color: #666;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .clear-filters-btn:hover {
            background-color: #e8e8e8;
            color: #333;
            transform: translateY(-2px);
        }

        .clear-filters-btn i {
            font-size: 0.8rem;
        }

        .filter-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .filter-group label {
            display: block;
            font-size: 0.9rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #555;
            position: relative;
            padding-left: 8px;
        }

        .filter-group label::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 14px;
            background-color: #004AAD;
            border-radius: 3px;
            opacity: 0.7;
        }

        .filter-select, .filter-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 0.9rem;
            color: #333;
            background-color: #f9f9f9;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
        }

        /* Custom select styling for category filter */
        .custom-select-wrapper {
            position: relative;
            user-select: none;
            width: 100%;
        }

        .custom-select {
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .custom-select__trigger {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.75rem 1rem;
            font-size: 0.9rem;
            color: #333;
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
        }

        .custom-select__trigger:hover {
            border-color: #bbb;
            background-color: #f5f5f5;
        }

        .custom-options {
            position: absolute;
            display: block;
            top: 100%;
            left: 0;
            right: 0;
            border: 1px solid #e0e0e0;
            border-top: 0;
            background: #fff;
            opacity: 0;
            visibility: hidden;
            pointer-events: none;
            z-index: 2;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 5px 10px rgba(0,0,0,0.1);
            max-height: 300px;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: #bbb #f1f1f1;
        }

        .custom-select.open .custom-options {
            opacity: 1;
            visibility: visible;
            pointer-events: all;
            animation: fadeIn 0.3s ease;
        }

        .custom-option {
            position: relative;
            display: block;
            padding: 10px 15px;
            font-size: 0.9rem;
            color: #333;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .custom-option:hover {
            background-color: #f0f7ff;
        }

        .custom-option.selected {
            color: #004AAD;
            background-color: #f0f7ff;
            font-weight: 500;
        }

        .option-group-label {
            display: block;
            padding: 10px 15px;
            font-size: 0.85rem;
            font-weight: 600;
            color: #004AAD;
            background-color: #f5f7fa;
            border-top: 1px solid #e0e0e0;
            border-bottom: 1px solid #e0e0e0;
            pointer-events: none;
        }

        .arrow {
            position: relative;
            height: 12px;
            width: 12px;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .arrow::before, .arrow::after {
            content: "";
            position: absolute;
            bottom: 0;
            width: 0.15rem;
            height: 100%;
            transition: all 0.3s ease;
            background-color: #666;
        }

        .arrow::before {
            left: -2px;
            transform: rotate(45deg);
        }

        .arrow::after {
            left: 2px;
            transform: rotate(-45deg);
        }

        .open .arrow::before {
            left: -2px;
            transform: rotate(-45deg);
        }

        .open .arrow::after {
            left: 2px;
            transform: rotate(45deg);
        }

        /* Style the scrollbar for webkit browsers */
        .custom-options::-webkit-scrollbar {
            width: 8px;
        }

        .custom-options::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .custom-options::-webkit-scrollbar-thumb {
            background: #bbb;
            border-radius: 4px;
        }

        .custom-options::-webkit-scrollbar-thumb:hover {
            background: #999;
        }

        .filter-select:focus, .filter-input:focus {
            outline: none;
            border-color: #004AAD;
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
            background-color: #fff;
        }

        .filter-select:hover, .filter-input:hover {
            border-color: #bbb;
            background-color: #f5f5f5;
        }

        .filter-section {
            margin-bottom: 1.5rem;
            border-top: 1px solid #eaedf2;
            padding-top: 1.5rem;
            position: relative;
        }

        .filter-section::before {
            content: '';
            position: absolute;
            top: -1px;
            left: 0;
            width: 50px;
            height: 1px;
            background-color: #004AAD;
        }

        .filter-section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            padding: 0.5rem 0;
            transition: all 0.2s ease;
        }

        .filter-section-header:hover h3 {
            color: #004AAD;
        }

        .filter-section-header h3 {
            font-size: 1.1rem;
            font-weight: 600;
            color: #444;
            transition: color 0.2s ease;
        }

        .filter-section-header i {
            color: #004AAD;
            transition: transform 0.3s ease;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(0, 74, 173, 0.08);
            border-radius: 50%;
        }

        .filter-section-header.active i {
            transform: rotate(180deg);
            background-color: rgba(0, 74, 173, 0.15);
        }

        .filter-section-content {
            display: none;
            padding-top: 1rem;
            animation: fadeIn 0.3s ease;
        }

        .filter-section-content.show {
            display: block;
        }

        .toggle-container {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            padding: 0.75rem;
            background-color: #f5f7fa;
            border-radius: 8px;
            transition: background-color 0.2s ease;
        }

        .toggle-container:hover {
            background-color: #eef2f7;
        }

        .toggle-label {
            font-size: 0.9rem;
            color: #555;
            font-weight: 500;
        }

        .toggle-label span {
            font-weight: 600;
            color: #e74c3c;
            transition: color 0.2s ease;
        }

        .toggle-label span.on {
            color: #2ecc71;
        }

        .toggle-switch {
            position: relative;
            width: 44px;
            height: 22px;
            background-color: #e74c3c;
            border-radius: 22px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.on {
            background-color: #2ecc71;
        }

        .toggle-switch .slider {
            position: absolute;
            width: 18px;
            height: 18px;
            background-color: white;
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .toggle-switch.on .slider {
            left: calc(100% - 20px);
        }

        .visibility-pill {
            display: inline-block;
            padding: 0.5rem 1rem;
            background-color: #f0f7ff;
            color: #004AAD;
            border-radius: 50px;
            font-size: 0.85rem;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0, 74, 173, 0.1);
            transition: all 0.2s ease;
            border: 1px solid rgba(0, 74, 173, 0.1);
        }

        .visibility-pill:hover {
            background-color: #e0efff;
            box-shadow: 0 3px 6px rgba(0, 74, 173, 0.15);
            transform: translateY(-1px);
        }

        .my-proposal-link {
            display: block;
            padding: 0.9rem 1.2rem;
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            color: #333;
            font-size: 0.95rem;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
            text-decoration: none;
        }

        .my-proposal-link:hover {
            border-color: #004AAD;
            background-color: #f0f7ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
        }

        .my-proposal-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .my-proposal-item span {
            font-weight: 500;
        }

        .my-proposal-item i {
            color: #004AAD;
            font-size: 0.8rem;
            transition: transform 0.2s ease;
        }

        .my-proposal-link:hover .my-proposal-item i {
            transform: translateX(3px);
        }

        /* Search and Jobs */
        .jobs-container {
            flex: 1;
        }

        .search-container {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .job-search-bar {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
        }

        .no-results {
            text-align: center;
            padding: 3rem 1rem;
            color: #6b7280;
            background-color: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .no-jobs {
            text-align: center;
            padding: 4rem 2rem;
            background: linear-gradient(to bottom, #ffffff, #f8f9fa);
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            grid-column: 1 / -1;
            border: 1px dashed rgba(0, 74, 173, 0.2);
            position: relative;
            overflow: hidden;
        }

        .no-jobs::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #004AAD, #CD208B);
            opacity: 0.5;
        }

        .no-jobs h2 {
            color: #004AAD;
            margin-bottom: 1rem;
            font-size: 1.75rem;
            font-weight: 700;
        }

        .no-jobs p {
            color: #6b7280;
            margin-bottom: 2rem;
            font-size: 1.1rem;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Job Cards - Single Card Per Row Layout */
        .jobs-list {
            display: flex;
            flex-direction: column;
            gap: 2rem;
            padding: 0.5rem;
            width: 100%;
        }

        /* Ensure each job card takes full width */
        .jobs-list .job-card {
            width: 100%;
            margin-bottom: 1.5rem;
        }

        /* Job card styles moved to optimized-job-cards.css */

        /* Header and avatar styles moved to optimized-job-cards.css */

        /* Client info and job title styles moved to optimized-job-cards.css */

        /* Job body and description styles moved to optimized-job-cards.css */

        /* Job meta styles moved to optimized-job-cards.css */

        /* Job tags and skills styles moved to optimized-job-cards.css */

        /* Job date styles moved to optimized-job-cards.css */

        /* Simplified Pagination Styles */
        .pagination-container {
            margin-top: 1.5rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }

        .pagination {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .pagination-btn {
            display: inline-block;
            padding: 0.5rem 1rem;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #004AAD;
            font-weight: 500;
            font-size: 0.9rem;
            cursor: pointer;
            text-decoration: none;
        }

        .pagination-btn:hover {
            background-color: #e9ecef;
        }

        .pagination-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            color: #6c757d;
        }

        .pagination-numbers {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            flex-wrap: wrap;
        }

        .pagination-number {
            display: inline-block;
            min-width: 32px;
            height: 32px;
            line-height: 32px;
            text-align: center;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #333;
            font-weight: 500;
            text-decoration: none;
        }

        .pagination-number:hover {
            background-color: #e9ecef;
        }

        .pagination-number.active {
            background-color: #004AAD;
            color: white;
            border-color: #004AAD;
        }

        .pagination-ellipsis {
            display: inline-block;
            min-width: 32px;
            height: 32px;
            line-height: 32px;
            text-align: center;
            color: #6c757d;
        }

        .pagination-info {
            margin-top: 0.5rem;
            color: #6c757d;
            font-size: 0.85rem;
            text-align: center;
        }

        /* Simplified Job Footer */
        .job-footer {
            padding: 1rem;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f8f9fa;
        }

        /* Simplified Job Price Container */
        .job-price-container {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .budget-label {
            font-size: 0.8rem;
            color: #6b7280;
            font-weight: 500;
        }

        .job-price {
            font-size: 1.1rem;
            font-weight: 700;
            color: #004AAD;
        }

        .budget-type {
            font-size: 0.8rem;
            color: #6b7280;
            font-weight: 500;
        }

        /* Simplified View Button */
        .view-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            background-color: #004AAD;
            color: white;
            font-weight: 600;
            border-radius: 4px;
            text-decoration: none;
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .view-btn:hover {
            background-color: #003b8a;
        }

        /* Simplified Pagination (Alternative) */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 1.5rem;
            gap: 0.25rem;
        }

        .page-item {
            display: inline-block;
            width: 32px;
            height: 32px;
            line-height: 32px;
            text-align: center;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            font-size: 0.875rem;
            color: #6b7280;
            cursor: pointer;
        }

        .page-item:hover {
            border-color: #004AAD;
            color: #004AAD;
        }

        .page-item.active {
            background-color: #004AAD;
            border-color: #004AAD;
            color: white;
        }

        .page-arrow {
            color: #6b7280;
        }

        .page-arrow:hover {
            color: #004AAD;
        }

        /* Content Sections */
        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        /* My Applications Styles */
        .jobs-header {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .jobs-title {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .jobs-filters {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .filter-select, .filter-input {
            padding: 0.625rem 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            width: 100%;
        }

        .jobs-grid {
            display: grid;
            gap: 1.5rem;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-pending {
            background-color: #fff7ed;
            color: #c2410c;
            border: 1px solid #fdba74;
        }

        .status-accepted {
            background-color: #ecfdf5;
            color: #047857;
            border: 1px solid #6ee7b7;
        }

        .status-rejected {
            background-color: #fef2f2;
            color: #b91c1c;
            border: 1px solid #fca5a5;
        }

        .status-review {
            background-color: #eff6ff;
            color: #1d4ed8;
            border: 1px solid #93c5fd;
        }

        .save-btn {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            background-color: transparent;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }

        .save-btn:hover {
            background-color: #f3f4f6;
        }

        .save-btn.saved {
            color: #2563eb;
        }

        .save-btn-text {
            display: none;
        }

        /* Job Offers Styles */
        .add-to-cart {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            background-color: #f3f4f6;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .add-to-cart:hover {
            background-color: #e5e7eb;
        }

        .add-to-cart.added {
            background-color: #dcfce7;
            border-color: #86efac;
            color: #166534;
        }

        /* My Clients Styles */
        .clients-section {
            padding: 1.5rem 0;
        }

        .clients-header {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .clients-title {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .clients-search {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .search-input {
            padding: 0.625rem 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            width: 100%;
        }

        .clients-scroll {
            overflow-x: auto;
            padding-bottom: 1rem;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: thin;
            scrollbar-color: #d1d5db transparent;
        }

        .clients-scroll::-webkit-scrollbar {
            height: 8px;
        }

        .clients-scroll::-webkit-scrollbar-track {
            background: transparent;
        }

        .clients-scroll::-webkit-scrollbar-thumb {
            background-color: #d1d5db;
            border-radius: 20px;
        }

        .clients-container {
            display: flex;
            gap: 1.5rem;
            padding: 0.5rem 0.25rem;
        }

        .client-card {
            background-color: white;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            position: relative;
            min-width: 300px;
            max-width: 400px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .client-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }

        .client-header {
            padding: 1.25rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .client-title {
            font-weight: 600;
            font-size: 1.125rem;
        }

        .favorite-btn {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            background-color: #f3f4f6;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .favorite-btn:hover {
            background-color: #e5e7eb;
        }

        .favorite-btn.favorited {
            background-color: #dcfce7;
            border-color: #86efac;
            color: #166534;
        }

        .client-body {
            padding: 1.25rem;
        }

        .client-description {
            color: #6b7280;
            margin-bottom: 1.25rem;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            font-size: 0.9375rem;
            max-height: 4.5em; /* Fallback for non-webkit browsers */
        }

        .client-details {
            display: grid;
            grid-template-columns: 1fr;
            gap: 0.75rem;
        }

        .client-detail {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }

        .client-detail i {
            color: #6b7280;
            width: 16px;
            text-align: center;
        }

        .client-footer {
            padding: 1.25rem;
            border-top: 1px solid #e5e7eb;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            animation: fadeIn 0.3s;
        }

        .modal-content {
            background-color: #fff;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            position: relative;
            animation: slideIn 0.3s;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
        }

        .modal-description {
            color: #6b7280;
            margin-bottom: 20px;
        }

        .close-modal {
            font-size: 28px;
            font-weight: bold;
            color: #666;
            cursor: pointer;
        }

        .close-modal:hover {
            color: #000;
        }

        .radio-option {
            display: block;
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .radio-option:hover {
            background-color: #f9fafb;
        }

        .radio-input {
            margin-right: 10px;
        }

        .radio-label {
            font-weight: 600;
            margin-left: 0.5rem;
        }

        .info {
            margin-left: 1.5rem;
            color: #6b7280;
            margin-top: 0.5rem;
        }

        .buttons {
            margin-top: 2rem;
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        .buttons button {
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
        }

        .buttons .cancel {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
        }

        .buttons .save {
            background: #d41b8c;
            color: white;
            border: none;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        /* Responsive Styles */
        @media (min-width: 640px) {
            .header-content {
                padding: 0 1.5rem;
            }

            .main-content {
                padding: 1.5rem 1.5rem 3rem;
            }

            .profile-info {
                flex-direction: row;
                align-items: center;
            }

            .profile-meta {
                flex-direction: row;
                gap: 1rem;
            }

            .jobs-filters {
                flex-direction: row;
                gap: 0.75rem;
            }

            .filter-select, .filter-input {
                width: auto;
            }

            .job-details {
                grid-template-columns: repeat(2, 1fr);
            }

            .save-btn-text {
                display: inline;
            }

            .clients-search {
                flex-direction: row;
                gap: 0.75rem;
            }

            .search-input {
                width: auto;
            }

            .client-details {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 768px) {
            .header-content {
                padding: 0 1.5rem;
            }

            .nav-links {
                display: flex;
            }

            .mobile-menu-btn {
                display: none;
            }

            .profile-content {
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
            }

            .dashboard-content {
                flex-direction: row;
            }

            .filters {
                width: 280px;
                align-self: flex-start;
                position: sticky;
                top: 80px;
            }

            .jobs-container {
                flex: 1;
                margin-left: 1.5rem;
            }

            /* Stats grid is now flex-based */

            .jobs-header {
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
            }

            .jobs-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .clients-header {
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
            }
        }

        @media (min-width: 1024px) {
            .header-content {
                padding: 0 2rem;
            }

            .main-content {
                padding: 2rem 2rem 4rem;
            }

            .filters {
                width: 320px;
            }

            .client-card {
                min-width: 350px;
            }
        }

        /* Animation for filter sections */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Enhanced search container styling */
        .enhanced-search-container {
            margin-bottom: 1.5rem;
            width: 100%;
        }

        .search-input-wrapper {
            display: flex;
            position: relative;
            width: 100%;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }

        .search-input-wrapper:focus-within {
            border-color: #004AAD;
            box-shadow: 0 6px 20px rgba(0, 74, 173, 0.15);
            transform: translateY(-2px);
        }

        .enhanced-search-input {
            flex: 1;
            padding: 1rem 1.5rem;
            border: none;
            font-size: 1rem;
            background-color: #fff;
            transition: all 0.3s ease;
        }

        .enhanced-search-input:focus {
            outline: none;
        }

        .search-button {
            background: linear-gradient(to right, #004AAD, #0063e5);
            border: none;
            padding: 0 1.5rem;
            color: white;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .search-button:hover {
            background: linear-gradient(to right, #003b8a, #0055c8);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 74, 173, 0.3);
        }

        .search-button:active, .search-button.active {
            background: linear-gradient(to right, #003070, #004aad);
            transform: translateY(0);
            box-shadow: 0 2px 6px rgba(0, 74, 173, 0.2);
        }

        .search-button i {
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .search-button:active i, .search-button.active i {
            transform: scale(0.9);
        }
    </style>
</head>
<body>
    <!-- Main Content -->
    <div class="container">
        <!-- Navbar -->
        <nav class="navbar">
            <div style="display: flex; align-items: center;">
                    <div class="logo">
                        <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                        <h1>GigGenius</h1>
                    </div>
                </a>
                <div class="nav-links">
                    <a href="{{ url_for('find_gigs') }}">Find Gigs</a>
                    <a href="{{ url_for('landing_page') }}">Proposals</a>

                    <!-- Log Work Hours Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Contracts
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">Log Hours</a>
                            <a href="{{ url_for('landing_page') }}">Work Diary</a>
                        </div>
                    </div>

                    <!-- Earnings Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Earnings
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">Billings and Earnings</a>
                            <a href="{{ url_for('landing_page') }}">Withdraw Earnings</a>
                            <a href="{{ url_for('landing_page') }}">Tax Info</a>
                        </div>
                    </div>

                    <a href="{{ url_for('messages') }}">Messages</a>
                </div>
            </div>
            <div class="right-section">
                <div class="search-container">
                    <div class="search-type-select">
                        <button id="searchTypeBtn" class="search-type-button">
                            <span id="selectedSearchType">Gigs</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div id="searchTypeDropdown" class="search-type-dropdown">
                            <div class="search-type-option" data-value="gigs">Gigs</div>
                            <div class="search-type-option" data-value="clients">Clients</div>
                            <div class="search-type-option" data-value="talents">Talents</div>
                        </div>
                    </div>
                    <div class="search-bar">
                        <input type="text" id="searchInput" placeholder="Search for gigs...">
                        <span class="icon">
                            <i class="fas fa-search"></i>
                        </span>
                    </div>
                </div>
                <div class="auth-buttons">
                    <div class="notification-icon">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </div>
                    <div class="profile-dropdown">
                        <div class="profile-button">
                            <img src="{{ url_for('api_profile_photo', user_type='genius', user_id=genius.id) }}" alt="Profile Picture">
                        </div>
                        <div class="profile-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">
                                <i class="fas fa-user"></i> My Profile
                            </a>
                            <a href="{{ url_for('landing_page') }}">
                                <i class="fas fa-cog"></i> Account Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="{{ url_for('logout') }}" class="logout-option" style="color: #dc3545 !important; font-weight: bold;">
                                <i class="fas fa-sign-out-alt"></i> Log Out
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </div>


    <div class="mobile-menu" id="mobileMenu">
        <a href="Dashboard.html">Find Gigs</a>
        <a href="My_proposal.html">Proposals</a>
        <a href="#">Contracts</a>
        <a href="Tracker.html">Log Works</a>
        <a href="#">Earnings</a>
        <a href="Billing_and_Earnings.html">Billings and Earnings</a>
        <a href="Withdraw_Earnings.html">Withdraw Earnings</a>
        <a href="Tax_info.html">Tax Info</a>
        <a href="#">Messages</a>
        <a href="{{ url_for('logout') }}" class="logout-option" style="color: #dc3545; font-weight: bold;"><i class="fas fa-sign-out-alt"></i> Log Out</a>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Profile Section -->
        <section class="profile-section">
            <div class="profile-card">
                <div class="profile-content">
                    <div class="profile-info">
                        <a href="#" class="profile-avatar" style="cursor: pointer; text-decoration: none;">
                            <img src="{{ url_for('api_profile_photo', user_type='genius', user_id=genius.id) }}" alt="{{ genius.first_name }} {{ genius.last_name }}">
                        </a>
                        <div class="profile-details">
                            <h2 class="profile-name">
                                Welcome <a href="#" style="text-decoration: none; color: inherit; cursor: pointer;"><span>{{ genius.first_name }} {{ genius.last_name }}</span></a>
                            </h2>
                            <div class="profile-meta">
                                <div class="profile-meta-item">
                                    <i class="fas fa-briefcase"></i>
                                    <span>{{ genius.position }}</span>
                                </div>
                                <div class="profile-meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>{{ genius.country }}</span>
                                </div>
                            </div>
                            <div class="profile-actions">
                                <button class="profile-btn btn-outline active" id="dashboardBtn">
                                    <i class="fas fa-home"></i>
                                    <span>Dashboard</span>
                                </button>
                                <a href="#" class="profile-btn btn-outline">
                                    <i class="fas fa-user"></i>
                                    <span>Profile</span>
                                </a>
                                <button class="profile-btn btn-outline" id="myApplicationsBtn">
                                    <i class="fas fa-folder"></i>
                                    <span>My Applications</span>
                                </button>
                                <button class="profile-btn btn-outline" id="jobOffersBtn">
                                    <i class="fas fa-bell"></i>
                                    <span>Job Offers</span>
                                </button>
                                <button class="profile-btn btn-outline" id="myClientsBtn">
                                    <i class="fas fa-star"></i>
                                    <span>My Clients</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Stats Section -->
        <section class="stats-section">
            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-dollar-sign stat-icon"></i>
                    <div class="stat-value">${{ genius.hourly_rate|default('0') }}</div>
                    <div class="stat-label">Hourly Rate</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-coins stat-icon"></i>
                    <div class="stat-value">$0</div>
                    <div class="stat-label">Total Earnings</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-briefcase stat-icon"></i>
                    <div class="stat-value">0</div>
                    <div class="stat-label">Hired</div>
                </div>

            </div>
        </section>

        <!-- Introduction Section -->
        <section class="introduction-section" id="introductionSection">
            <h3>Introduction</h3>
            <div class="text-wrapper">
                <p class="truncated-text">
                    "{{ genius.introduction }}"
                </p>
                <a href="#introduction" class="show-more-btn" style="text-decoration: none; color: #d12b82; cursor: pointer;">...Show More</a>
            </div>
        </section>

        <!-- Quote Slider -->
        <div class="quote-slider" id="quoteSlider">
            <h1 class="quote" id="currentQuote">
                <span class="main-text">Getting a job shouldn't be expensive.</span>
                <span class="sub-text">No connect or bids needed.</span>
            </h1>
            <div class="quote-navigation">
                <span class="quote-arrow left" onclick="changeQuote(-1)">
                    <i class="fas fa-chevron-left"></i>
                </span>
                <span class="quote-arrow right" onclick="changeQuote(1)">
                    <i class="fas fa-chevron-right"></i>
                </span>
            </div>
            <div class="quote-dots">
                <span class="quote-dot active" onclick="jumpToQuote(0)"></span>
                <span class="quote-dot" onclick="jumpToQuote(1)"></span>
                <span class="quote-dot" onclick="jumpToQuote(2)"></span>
            </div>
        </div>

        <!-- Dashboard Content (Default) -->
        <div id="dashboardContent" class="content-section active">
            <div class="dashboard-content">
                <!-- Filters Sidebar -->
                <aside class="filters">
                    <div class="filters-header">
                        <h2>Filter Gigs</h2>
                        <button id="clearFilters" class="clear-filters-btn">
                            <i class="fas fa-times"></i> Clear Filters
                        </button>
                    </div>

                    <div class="filter-group">
                        <label for="locationFilter">Location</label>
                        <div class="custom-select-wrapper">
                            <div class="custom-select" id="customLocationSelect">
                                <div class="custom-select__trigger">
                                    <span id="locationDisplay">All Locations</span>
                                    <div class="arrow"></div>
                                </div>
                                <div class="custom-options">
                                    <span class="custom-option selected" data-value="">All Locations</span>

                                    <span class="option-group-label">Popular</span>
                                    <span class="custom-option" data-value="Remote">Remote</span>
                                    <span class="custom-option" data-value="United States">United States</span>
                                    <span class="custom-option" data-value="United Kingdom">United Kingdom</span>
                                    <span class="custom-option" data-value="Canada">Canada</span>
                                    <span class="custom-option" data-value="Australia">Australia</span>

                                    <span class="option-group-label">Europe</span>
                                    <span class="custom-option" data-value="Germany">Germany</span>
                                    <span class="custom-option" data-value="France">France</span>
                                    <span class="custom-option" data-value="Spain">Spain</span>
                                    <span class="custom-option" data-value="Italy">Italy</span>
                                    <span class="custom-option" data-value="Netherlands">Netherlands</span>
                                    <span class="custom-option" data-value="Sweden">Sweden</span>
                                    <span class="custom-option" data-value="Switzerland">Switzerland</span>
                                    <span class="custom-option" data-value="Poland">Poland</span>
                                    <span class="custom-option" data-value="Belgium">Belgium</span>
                                    <span class="custom-option" data-value="Norway">Norway</span>
                                    <span class="custom-option" data-value="Denmark">Denmark</span>
                                    <span class="custom-option" data-value="Finland">Finland</span>
                                    <span class="custom-option" data-value="Ireland">Ireland</span>
                                    <span class="custom-option" data-value="Portugal">Portugal</span>
                                    <span class="custom-option" data-value="Austria">Austria</span>
                                    <span class="custom-option" data-value="Greece">Greece</span>

                                    <span class="option-group-label">Asia</span>
                                    <span class="custom-option" data-value="India">India</span>
                                    <span class="custom-option" data-value="Japan">Japan</span>
                                    <span class="custom-option" data-value="China">China</span>
                                    <span class="custom-option" data-value="Singapore">Singapore</span>
                                    <span class="custom-option" data-value="Philippines">Philippines</span>
                                    <span class="custom-option" data-value="Malaysia">Malaysia</span>
                                    <span class="custom-option" data-value="Indonesia">Indonesia</span>
                                    <span class="custom-option" data-value="Thailand">Thailand</span>
                                    <span class="custom-option" data-value="Vietnam">Vietnam</span>
                                    <span class="custom-option" data-value="South Korea">South Korea</span>
                                    <span class="custom-option" data-value="Hong Kong">Hong Kong</span>
                                    <span class="custom-option" data-value="Taiwan">Taiwan</span>
                                    <span class="custom-option" data-value="United Arab Emirates">United Arab Emirates</span>
                                    <span class="custom-option" data-value="Israel">Israel</span>

                                    <span class="option-group-label">Americas</span>
                                    <span class="custom-option" data-value="Brazil">Brazil</span>
                                    <span class="custom-option" data-value="Mexico">Mexico</span>
                                    <span class="custom-option" data-value="Argentina">Argentina</span>
                                    <span class="custom-option" data-value="Colombia">Colombia</span>
                                    <span class="custom-option" data-value="Chile">Chile</span>
                                    <span class="custom-option" data-value="Peru">Peru</span>

                                    <span class="option-group-label">Africa & Middle East</span>
                                    <span class="custom-option" data-value="South Africa">South Africa</span>
                                    <span class="custom-option" data-value="Nigeria">Nigeria</span>
                                    <span class="custom-option" data-value="Kenya">Kenya</span>
                                    <span class="custom-option" data-value="Egypt">Egypt</span>
                                    <span class="custom-option" data-value="Morocco">Morocco</span>
                                    <span class="custom-option" data-value="Ghana">Ghana</span>

                                    <span class="option-group-label">Oceania</span>
                                    <span class="custom-option" data-value="New Zealand">New Zealand</span>
                                    <span class="custom-option" data-value="Fiji">Fiji</span>
                                </div>
                            </div>
                            <input type="hidden" id="locationFilter" name="locationFilter" value="">
                        </div>
                    </div>

                    <div class="filter-group">
                        <label for="categoryFilter">Category</label>
                        <div class="custom-select-wrapper">
                            <div class="custom-select" id="customCategorySelect">
                                <div class="custom-select__trigger">
                                    <span id="categoryDisplay">All Categories</span>
                                    <div class="arrow"></div>
                                </div>
                                <div class="custom-options">
                                    <span class="custom-option selected" data-value="">All Categories</span>

                                    <span class="option-group-label">Programming & Development</span>
                                    <span class="custom-option" data-value="Web Development">Web Development</span>
                                    <span class="custom-option" data-value="Mobile Development">Mobile Development</span>
                                    <span class="custom-option" data-value="Full Stack">Full Stack Development</span>
                                    <span class="custom-option" data-value="Frontend">Frontend Development</span>
                                    <span class="custom-option" data-value="Backend">Backend Development</span>
                                    <span class="custom-option" data-value="Game Development">Game Development</span>
                                    <span class="custom-option" data-value="Desktop Applications">Desktop Applications</span>
                                    <span class="custom-option" data-value="E-commerce Development">E-commerce Development</span>
                                    <span class="custom-option" data-value="WordPress">WordPress Development</span>
                                    <span class="custom-option" data-value="Shopify">Shopify Development</span>
                                    <span class="custom-option" data-value="DevOps">DevOps & Infrastructure</span>
                                    <span class="custom-option" data-value="QA & Testing">QA & Testing</span>
                                    <span class="custom-option" data-value="Blockchain">Blockchain Development</span>
                                    <span class="custom-option" data-value="AR/VR">AR/VR Development</span>

                                    <span class="option-group-label">Design & Creative</span>
                                    <span class="custom-option" data-value="UI/UX">UI/UX Design</span>
                                    <span class="custom-option" data-value="Graphic Design">Graphic Design</span>
                                    <span class="custom-option" data-value="Logo Design">Logo Design</span>
                                    <span class="custom-option" data-value="Brand Design">Brand Design</span>
                                    <span class="custom-option" data-value="Web Design">Web Design</span>
                                    <span class="custom-option" data-value="Mobile App Design">Mobile App Design</span>
                                    <span class="custom-option" data-value="Illustration">Illustration</span>
                                    <span class="custom-option" data-value="Animation">Animation</span>
                                    <span class="custom-option" data-value="3D Modeling">3D Modeling & Rendering</span>
                                    <span class="custom-option" data-value="Product Design">Product Design</span>
                                    <span class="custom-option" data-value="Packaging Design">Packaging Design</span>
                                    <span class="custom-option" data-value="Print Design">Print Design</span>
                                    <span class="custom-option" data-value="Motion Graphics">Motion Graphics</span>

                                    <span class="option-group-label">Digital Marketing</span>
                                    <span class="custom-option" data-value="SEO">SEO Optimization</span>
                                    <span class="custom-option" data-value="Social Media">Social Media Marketing</span>
                                    <span class="custom-option" data-value="Content Marketing">Content Marketing</span>
                                    <span class="custom-option" data-value="Email Marketing">Email Marketing</span>
                                    <span class="custom-option" data-value="PPC">PPC & Paid Advertising</span>
                                    <span class="custom-option" data-value="SEM">Search Engine Marketing</span>
                                    <span class="custom-option" data-value="Influencer Marketing">Influencer Marketing</span>
                                    <span class="custom-option" data-value="Affiliate Marketing">Affiliate Marketing</span>
                                    <span class="custom-option" data-value="Marketing Strategy">Marketing Strategy</span>
                                    <span class="custom-option" data-value="Growth Hacking">Growth Hacking</span>
                                    <span class="custom-option" data-value="Analytics">Marketing Analytics</span>
                                    <span class="custom-option" data-value="Conversion Optimization">Conversion Optimization</span>

                                    <span class="option-group-label">Writing & Content</span>
                                    <span class="custom-option" data-value="Content Writing">Content Writing</span>
                                    <span class="custom-option" data-value="Copywriting">Copywriting</span>
                                    <span class="custom-option" data-value="Technical Writing">Technical Writing</span>
                                    <span class="custom-option" data-value="Blog Writing">Blog Writing</span>
                                    <span class="custom-option" data-value="Ghostwriting">Ghostwriting</span>
                                    <span class="custom-option" data-value="Editing">Editing & Proofreading</span>
                                    <span class="custom-option" data-value="UX Writing">UX Writing</span>
                                    <span class="custom-option" data-value="Grant Writing">Grant Writing</span>
                                    <span class="custom-option" data-value="Scriptwriting">Scriptwriting</span>
                                    <span class="custom-option" data-value="Translation">Translation</span>
                                    <span class="custom-option" data-value="Transcription">Transcription</span>

                                    <span class="option-group-label">Business & Finance</span>
                                    <span class="custom-option" data-value="Business Strategy">Business Strategy</span>
                                    <span class="custom-option" data-value="Financial Analysis">Financial Analysis</span>
                                    <span class="custom-option" data-value="Accounting">Accounting & Bookkeeping</span>
                                    <span class="custom-option" data-value="Tax Preparation">Tax Preparation</span>
                                    <span class="custom-option" data-value="Business Planning">Business Planning</span>
                                    <span class="custom-option" data-value="Market Research">Market Research</span>
                                    <span class="custom-option" data-value="Financial Modeling">Financial Modeling</span>
                                    <span class="custom-option" data-value="Investment Analysis">Investment Analysis</span>
                                    <span class="custom-option" data-value="Business Consulting">Business Consulting</span>
                                    <span class="custom-option" data-value="Legal Consulting">Legal Consulting</span>

                                    <span class="option-group-label">Sales & Customer Service</span>
                                    <span class="custom-option" data-value="Sales Strategy">Sales Strategy</span>
                                    <span class="custom-option" data-value="Lead Generation">Lead Generation</span>
                                    <span class="custom-option" data-value="CRM Management">CRM Management</span>
                                    <span class="custom-option" data-value="Customer Support">Customer Support</span>
                                    <span class="custom-option" data-value="Virtual Assistant">Virtual Assistant</span>
                                    <span class="custom-option" data-value="Telemarketing">Telemarketing</span>
                                    <span class="custom-option" data-value="Sales Funnel">Sales Funnel Optimization</span>

                                    <span class="option-group-label">Data & Analytics</span>
                                    <span class="custom-option" data-value="Data Analysis">Data Analysis</span>
                                    <span class="custom-option" data-value="Data Science">Data Science</span>
                                    <span class="custom-option" data-value="Machine Learning">Machine Learning</span>
                                    <span class="custom-option" data-value="Data Visualization">Data Visualization</span>
                                    <span class="custom-option" data-value="Business Intelligence">Business Intelligence</span>
                                    <span class="custom-option" data-value="Database Design">Database Design</span>
                                    <span class="custom-option" data-value="Big Data">Big Data</span>
                                    <span class="custom-option" data-value="Data Mining">Data Mining</span>
                                    <span class="custom-option" data-value="Data Engineering">Data Engineering</span>

                                    <span class="option-group-label">Audio & Video</span>
                                    <span class="custom-option" data-value="Video Editing">Video Editing</span>
                                    <span class="custom-option" data-value="Video Production">Video Production</span>
                                    <span class="custom-option" data-value="Audio Editing">Audio Editing</span>
                                    <span class="custom-option" data-value="Voice Over">Voice Over</span>
                                    <span class="custom-option" data-value="Music Production">Music Production</span>
                                    <span class="custom-option" data-value="Podcast Production">Podcast Production</span>
                                    <span class="custom-option" data-value="Sound Design">Sound Design</span>
                                    <span class="custom-option" data-value="Animation">Animation</span>
                                </div>
                            </div>
                            <input type="hidden" id="categoryFilter" name="categoryFilter" value="">
                        </div>
                    </div>

                    <div class="filter-group">
                        <label for="typeFilter">Job Type</label>
                        <div class="custom-select-wrapper">
                            <div class="custom-select" id="customTypeSelect">
                                <div class="custom-select__trigger">
                                    <span id="typeDisplay">All Job Types</span>
                                    <div class="arrow"></div>
                                </div>
                                <div class="custom-options">
                                    <span class="custom-option selected" data-value="">All Job Types</span>

                                    <span class="option-group-label">Employment Type</span>
                                    <span class="custom-option" data-value="Full-Time">Full Time</span>
                                    <span class="custom-option" data-value="Part-Time">Part Time</span>
                                    <span class="custom-option" data-value="Freelance">Freelance</span>
                                    <span class="custom-option" data-value="Contract">Contract</span>
                                    <span class="custom-option" data-value="Intern">Intern</span>

                                    <span class="option-group-label">Project Type</span>
                                    <span class="custom-option" data-value="One-time">One-time Project</span>
                                    <span class="custom-option" data-value="Ongoing">Ongoing Work</span>

                                    <span class="option-group-label">Payment Type</span>
                                    <span class="custom-option" data-value="Fixed-price">Fixed-price</span>
                                    <span class="custom-option" data-value="Hourly">Hourly Rate</span>
                                </div>
                            </div>
                            <input type="hidden" id="typeFilter" name="typeFilter" value="">
                        </div>
                    </div>

                    <div class="filter-group">
                        <label for="priceFilter">Min. Rate/Hour ($)</label>
                        <input type="number" id="priceFilter" class="filter-input" min="0" placeholder="Enter minimum rate">
                    </div>

                    <!-- Availability Section (Hidden by default) -->
                    <div class="filter-section">
                        <div class="filter-section-header" id="availabilityHeader">
                            <h3>Availability</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="filter-section-content" id="availabilityContent" style="display: none;">
                            <div class="toggle-container">
                                <div class="toggle-switch" id="availabilityToggle">
                                    <div class="slider"></div>
                                </div>
                                <span class="toggle-label">Availability Badge <span id="toggleStatus" class="off">OFF</span></span>
                            </div>
                        </div>
                    </div>

                    <!-- Preferences Section -->
                    <div class="filter-section">
                        <div class="filter-section-header" id="preferencesHeader">
                            <h3>Preferences</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="filter-section-content" id="preferencesContent">
                            <div class="filter-group">
                                <div class="visibility-header">
                                    <label>Profile Visibility</label>
                                    <a href="#" class="edit-visibility-btn" id="editVisibilityBtn">
                                        <i class="fas fa-pencil-alt"></i>
                                    </a>
                                </div>
                                <div class="visibility-pill">Public</div>
                            </div>

                            <div class="filter-group">
                                <div class="hours-header">
                                    <label>Hours per Week</label>
                                    <a href="#" class="edit-hours-btn">
                                        <i class="fas fa-pencil-alt"></i>
                                    </a>
                                </div>
                                <div class="visibility-pill">More than 20 hrs/week</div>
                            </div>
                        </div>
                    </div>

                    <!-- Proposal Section -->
                    <div class="filter-section">
                        <div class="filter-section-header" id="proposalHeader">
                            <h3>Proposal</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="filter-section-content" id="proposalContent">
                            <a href="My_proposal.html" class="my-proposal-link">
                                <div class="my-proposal-item">
                                    <span>My Proposal</span>
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </a>
                        </div>
                    </div>
                </aside>

                <!-- Jobs Container - OPTIMIZED SINGLE CARD LAYOUT -->
                <div class="jobs-container" style="width: 100%; max-width: 1200px; padding: 0; margin: 0 auto;">
                    <!-- Simplified search container -->
                    <div class="enhanced-search-container" style="margin-bottom: 1rem;">
                        <div class="search-input-wrapper" style="display: flex; border: 1px solid #e5e7eb; border-radius: 4px; overflow: hidden;">
                            <input type="text" class="enhanced-search-input" id="jobSearch" placeholder="Search for jobs, skills, or keywords..." style="flex: 1; padding: 0.75rem; border: none; outline: none;">
                            <button class="search-button" id="jobSearchBtn" style="background-color: #004AAD; color: white; border: none; padding: 0 1rem; cursor: pointer;">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>

                    <div id="noResults" class="no-results" style="display: none;">
                        No gigs match your current filters. Try adjusting your search criteria.
                    </div>

                    <!-- ONLY ONE CARD PER ROW - OPTIMIZED -->
                    <div class="jobs-list" id="jobsList">
                        {% if jobs %}
                            {% for job in jobs %}
                            <div class="job-card" data-location="{{ job.location|default('Remote') }}" data-category="{{ job.category|default('') }}" data-type="{{ job.job_type|default('') }}" data-price="{{ job.budget_amount|default(0) }}">
                                <!-- Job Header with Client Info -->
                                <div class="job-header">
                                    <div class="client-avatar">
                                        {% for client in clients %}
                                            {% if client.id == job.client_id %}
                                                <img src="{{ url_for('api_profile_photo', user_type='client', user_id=client.id) }}" alt="{{ client.first_name }} {{ client.last_name }}" class="avatar-img">
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                    <div class="job-title-client">
                                        <div class="client-info">
                                            <div class="client-profile">
                                                <div class="client-name">
                                                    {% for client in clients %}
                                                        {% if client.id == job.client_id %}
                                                            <div class="client-name-text">{{ client.business_name }}</div>
                                                            <div class="client-full-name">{{ client.first_name }} {{ client.last_name }}</div>
                                                        {% endif %}
                                                    {% endfor %}
                                                </div>
                                                <div class="client-location">
                                                    <div class="location-badge">
                                                        <i class="fas fa-map-marker-alt"></i>
                                                        {% for client in clients %}
                                                            {% if client.id == job.client_id %}
                                                                {{ client.country }}
                                                            {% endif %}
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="job-title-container">
                                            <h2>{{ job.title }}</h2>
                                        </div>
                                    </div>
                                </div>

                                <!-- Job Body -->
                                <div class="job-body">
                                    <div class="job-description">
                                        {{ job.description|truncate(200) }}
                                    </div>

                                    <!-- Project Scope Section -->
                                    <div class="job-tags-section">
                                        <h4 class="tags-section-title">Project Scope</h4>
                                        <div class="job-meta">
                                            <!-- First row -->
                                            <div class="job-meta-item">
                                                <i class="fas fa-layer-group"></i>
                                                <span>Project Size: <span class="value-text">{{ job.project_size }}</span></span>
                                            </div>
                                            <div class="job-meta-item">
                                                <i class="fas fa-clock"></i>
                                                <span>Duration: <span class="value-text">{{ job.duration }}</span></span>
                                            </div>
                                            <div class="job-meta-item">
                                                <i class="fas fa-user-tie"></i>
                                                <span>Experience: <span class="value-text">{{ job.experience_level }}</span></span>
                                            </div>
                                            <div class="job-meta-item">
                                                <i class="fas fa-briefcase"></i>
                                                <span>Job Type: <span class="value-text">{{ job.job_type }}</span></span>
                                            </div>

                                            <!-- Second row with budget info -->
                                            <div class="job-meta-item">
                                                <i class="fas fa-money-bill-wave"></i>
                                                <span>Budget Type: <span class="value-text">{{ job.budget_type }}</span></span>
                                            </div>
                                            <div class="job-meta-item">
                                                <i class="fas fa-dollar-sign"></i>
                                                <span>Budget Amount: <span class="value-text">${{ job.budget_amount }}</span></span>
                                            </div>

                                            <!-- Third row with hiring preferences -->
                                            {% if job.hiring_preference %}
                                            <div class="job-meta-item">
                                                <i class="fas fa-handshake"></i>
                                                <span>Hiring Preference: <span class="value-text">{{ job.hiring_preference }}</span></span>
                                            </div>
                                            {% endif %}

                                            {% if job.project_description %}
                                            <div class="job-meta-item">
                                                <i class="fas fa-file-alt"></i>
                                                <span>Project Description: <span class="value-text">{{ job.project_description|truncate(30) }}</span></span>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <!-- Category, Specialty and Skills - Enhanced with labels -->
                                    <div class="job-tags-section">
                                        <h4 class="tags-section-title">Skills & Expertise</h4>
                                        <div class="job-meta">
                                            {% if job.category %}
                                                <div class="job-meta-item">
                                                    <i class="fas fa-folder"></i>
                                                    <span>Category: <span class="value-text">{{ job.category }}</span></span>
                                                </div>
                                            {% endif %}

                                            {% if job.specialty %}
                                                <div class="job-meta-item">
                                                    <i class="fas fa-star"></i>
                                                    <span>Specialty: <span class="value-text">{{ job.specialty }}</span></span>
                                                </div>
                                            {% endif %}

                                            {% if job.skills %}
                                                <div class="skills-container">
                                                    <span class="skills-label">Skills:</span>
                                                    <div class="skills-tags">
                                                        {% for skill in job.skills.split(',') %}
                                                            <div class="job-tag skill-tag">{{ skill.strip() }}</div>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="job-date">
                                        <i class="far fa-calendar-alt"></i>
                                        Posted {{ job.created_at if job.created_at else 'Recently' }}
                                    </div>
                                </div>

                                <!-- Enhanced Job Footer -->
                                <div class="job-footer">
                                    <div class="job-price-container">
                                        <div class="budget-label">Budget:</div>
                                        <div class="job-price">
                                            {% if job.budget_type == 'Hourly' %}
                                                ${{ job.budget_amount }}/hr
                                            {% else %}
                                                ${{ job.budget_amount }}
                                            {% endif %}
                                        </div>
                                        <div class="budget-type">{{ job.budget_type }}</div>
                                    </div>
                                    <a href="{{ url_for('job_details', job_id=job.id) }}" class="view-btn">View Details</a>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="no-jobs">
                                <h2>No jobs available at the moment</h2>
                                <p>Check back later for new opportunities or adjust your search filters.</p>
                            </div>
                        {% endif %}

                        <!-- Pagination Controls -->
                        {% if pagination and pagination.total_pages > 1 %}
                        <div class="pagination-container">
                            <div class="pagination">
                                {% if pagination.has_prev %}
                                <a href="{{ url_for('genius_page', page=pagination.page-1) }}" class="pagination-btn prev">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </a>
                                {% else %}
                                <span class="pagination-btn prev disabled">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </span>
                                {% endif %}

                                <div class="pagination-numbers">
                                    {% set start_page = [1, pagination.page - 2]|max %}
                                    {% set end_page = [pagination.total_pages, start_page + 4]|min %}
                                    {% set start_page = [1, end_page - 4]|max %}

                                    {% if start_page > 1 %}
                                    <a href="{{ url_for('genius_page', page=1) }}" class="pagination-number">1</a>
                                    {% if start_page > 2 %}
                                    <span class="pagination-ellipsis">...</span>
                                    {% endif %}
                                    {% endif %}

                                    {% for p in range(start_page, end_page + 1) %}
                                    {% if p == pagination.page %}
                                    <span class="pagination-number active">{{ p }}</span>
                                    {% else %}
                                    <a href="{{ url_for('genius_page', page=p) }}" class="pagination-number">{{ p }}</a>
                                    {% endif %}
                                    {% endfor %}

                                    {% if end_page < pagination.total_pages %}
                                    {% if end_page < pagination.total_pages - 1 %}
                                    <span class="pagination-ellipsis">...</span>
                                    {% endif %}
                                    <a href="{{ url_for('genius_page', page=pagination.total_pages) }}" class="pagination-number">{{ pagination.total_pages }}</a>
                                    {% endif %}
                                </div>

                                {% if pagination.has_next %}
                                <a href="{{ url_for('genius_page', page=pagination.page+1) }}" class="pagination-btn next">
                                    Next <i class="fas fa-chevron-right"></i>
                                </a>
                                {% else %}
                                <span class="pagination-btn next disabled">
                                    Next <i class="fas fa-chevron-right"></i>
                                </span>
                                {% endif %}
                            </div>
                            <!-- Simplified pagination info -->
                            <div class="pagination-info">
                                Page {{ pagination.page }} of {{ pagination.total_pages }}
                            </div>
                        </div>
                        {% endif %}


    <script>
        // Initialize profile dropdown immediately
        window.addEventListener('load', function() {
            const profileDropdown = document.querySelector('.profile-dropdown');
            const profileButton = profileDropdown.querySelector('.profile-button');

            if (profileButton && profileDropdown) {
                // Toggle dropdown on profile button click
                profileButton.addEventListener('click', function(e) {
                    e.stopPropagation();
                    profileDropdown.classList.toggle('active');
                    console.log('Profile dropdown clicked, active:', profileDropdown.classList.contains('active'));
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!profileDropdown.contains(e.target)) {
                        profileDropdown.classList.remove('active');
                    }
                });
            }
        });

        // Prevent back button navigation to landing page
        window.addEventListener('pageshow', function(event) {
            // If the page is loaded from the browser cache (back button)
            if (event.persisted) {
                // Reload the page to ensure proper session state
                window.location.reload();
            }
        });

        // Disable browser cache for this page
        window.onload = function() {
            // Set cache control headers via meta tags
            document.head.innerHTML += '<meta http-equiv="Cache-Control" content="no-store, no-cache, must-revalidate, post-check=0, pre-check=0, max-age=0">';
            document.head.innerHTML += '<meta http-equiv="Pragma" content="no-cache">';
            document.head.innerHTML += '<meta http-equiv="Expires" content="-1">';
        };

        // Quote Slider Functionality
        let currentQuoteIndex = 0;
        const quotes = [
            {
                main: "Getting a job shouldn't be expensive.",
                sub: "No connect or bids needed."
            },
            {
                main: "Find your dream job with ease.",
                sub: "Personalized job matches for your skills."
            },
            {
                main: "Work with clients who value your expertise.",
                sub: "Quality over quantity, always."
            }
        ];

        function changeQuote(direction) {
            const quoteElement = document.getElementById('currentQuote');
            quoteElement.classList.add('fade');

            setTimeout(() => {
                currentQuoteIndex = (currentQuoteIndex + direction + quotes.length) % quotes.length;
                updateQuote();
                updateDots();
                quoteElement.classList.remove('fade');
            }, 300);
        }

        function jumpToQuote(index) {
            if (index === currentQuoteIndex) return;

            const quoteElement = document.getElementById('currentQuote');
            quoteElement.classList.add('fade');

            setTimeout(() => {
                currentQuoteIndex = index;
                updateQuote();
                updateDots();
                quoteElement.classList.remove('fade');
            }, 300);
        }

        function updateQuote() {
            const quoteElement = document.getElementById('currentQuote');
            const quote = quotes[currentQuoteIndex];
            quoteElement.innerHTML = `
                <span class="main-text">${quote.main}</span>
                <span class="sub-text">${quote.sub}</span>
            `;
        }

        function updateDots() {
            const dots = document.querySelectorAll('.quote-dot');
            dots.forEach((dot, index) => {
                if (index === currentQuoteIndex) {
                    dot.classList.add('active');
                } else {
                    dot.classList.remove('active');
                }
            });
        }

        // Auto-rotate quotes every 5 seconds
        setInterval(() => {
            changeQuote(1);
        }, 5000);

        document.addEventListener('DOMContentLoaded', function() {
            const searchTypeBtn = document.getElementById('searchTypeBtn');
            const searchTypeDropdown = document.getElementById('searchTypeDropdown');
            const selectedSearchType = document.getElementById('selectedSearchType');
            const searchInput = document.getElementById('searchInput');
            const options = document.querySelectorAll('.search-type-option');

            // Toggle dropdown
            searchTypeBtn.addEventListener('click', function() {
                searchTypeDropdown.classList.toggle('active');
            });

            // Handle option selection
            options.forEach(option => {
                option.addEventListener('click', function() {
                    const value = this.dataset.value;
                    selectedSearchType.textContent = this.textContent;
                    searchTypeDropdown.classList.remove('active');

                    // Update placeholder based on selection
                    const placeholders = {
                        gigs: 'Search for gigs...',
                    };
                    searchInput.placeholder = placeholders[value] || placeholders.all;
                });
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!searchTypeBtn.contains(e.target) && !searchTypeDropdown.contains(e.target)) {
                    searchTypeDropdown.classList.remove('active');
                }
            });
        });
        document.addEventListener('DOMContentLoaded', function() {
            // Close dropdowns when clicking outside
            window.addEventListener('click', function(e) {
                if (!e.target.matches('.nav-dropbtn')) {
                    const dropdowns = document.getElementsByClassName('nav-dropdown-content');
                    for (let dropdown of dropdowns) {
                        if (dropdown.classList.contains('show')) {
                            dropdown.classList.remove('show');
                        }
                    }
                }
            });
        });
        document.addEventListener('DOMContentLoaded', function() {
            const profileDropdown = document.querySelector('.profile-dropdown');
            const profileButton = profileDropdown.querySelector('.profile-button');

            // Toggle dropdown on profile button click
            profileButton.addEventListener('click', function(e) {
                e.stopPropagation();
                profileDropdown.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!profileDropdown.contains(e.target)) {
                    profileDropdown.classList.remove('active');
                }
            });
        });

        // Job filtering functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Get URL parameters for filters
            const urlParams = new URLSearchParams(window.location.search);
            const locationParam = urlParams.get('location') || '';
            const categoryParam = urlParams.get('category') || '';
            const typeParam = urlParams.get('type') || '';
            const priceParam = urlParams.get('price') || '';
            const searchParam = urlParams.get('search') || '';

            // Set search input value from URL parameter
            if (searchParam) {
                document.getElementById('jobSearch').value = searchParam;
            }

            // Set price filter value from URL parameter
            if (priceParam) {
                document.getElementById('priceFilter').value = priceParam;
            }

            // Filter section toggles
            const filterSectionHeaders = document.querySelectorAll('.filter-section-header');
            filterSectionHeaders.forEach(header => {
                const content = header.nextElementSibling;

                // Hide the availability section by default
                if (header.id === 'availabilityHeader') {
                    // Don't add active class or display block
                    content.style.display = 'none';
                }

                header.addEventListener('click', function() {
                    this.classList.toggle('active');

                    if (content.style.display === 'block') {
                        content.style.display = 'none';
                    } else {
                        content.style.display = 'block';
                    }
                });
            });

            // Toggle switch functionality
            const availabilityToggle = document.getElementById('availabilityToggle');
            const toggleStatus = document.getElementById('toggleStatus');

            availabilityToggle.addEventListener('click', function() {
                this.classList.toggle('on');
                if (this.classList.contains('on')) {
                    toggleStatus.textContent = 'ON';
                    toggleStatus.classList.remove('off');
                    toggleStatus.classList.add('on');
                } else {
                    toggleStatus.textContent = 'OFF';
                    toggleStatus.classList.remove('on');
                    toggleStatus.classList.add('off');
                }
            });

            // Custom select functionality
            function setupCustomSelect(selectId, displayId, inputId) {
                const customSelect = document.querySelector(selectId);
                const customSelectTrigger = customSelect.querySelector('.custom-select__trigger');
                const customOptions = customSelect.querySelectorAll('.custom-option');
                const displayElement = document.querySelector(displayId);
                const inputElement = document.querySelector(inputId);

                // Toggle custom select dropdown
                customSelectTrigger.addEventListener('click', function() {
                    customSelect.classList.toggle('open');

                    // Close other dropdowns when opening this one
                    document.querySelectorAll('.custom-select.open').forEach(select => {
                        if (select !== customSelect) {
                            select.classList.remove('open');
                        }
                    });

                    // Prevent body scrolling when dropdown is open
                    if (customSelect.classList.contains('open')) {
                        document.body.style.overflow = 'hidden';
                    } else {
                        document.body.style.overflow = '';
                    }
                });

                // Handle option selection
                customOptions.forEach(option => {
                    option.addEventListener('click', function() {
                        // Update display and hidden input
                        displayElement.textContent = this.textContent;
                        inputElement.value = this.getAttribute('data-value');

                        // Update selected state
                        customSelect.querySelector('.custom-option.selected').classList.remove('selected');
                        this.classList.add('selected');

                        // Close dropdown
                        customSelect.classList.remove('open');
                        document.body.style.overflow = '';

                        // Trigger filtering
                        filterJobs();
                    });
                });

                // Close custom select when clicking outside
                document.addEventListener('click', function(e) {
                    if (!customSelect.contains(e.target)) {
                        customSelect.classList.remove('open');
                        document.body.style.overflow = '';
                    }
                });

                // Close custom select when pressing Escape
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && customSelect.classList.contains('open')) {
                        customSelect.classList.remove('open');
                        document.body.style.overflow = '';
                    }
                });

                return {
                    select: customSelect,
                    display: displayElement,
                    input: inputElement,
                    options: customOptions,
                    reset: function() {
                        displayElement.textContent = customSelect.querySelector('.custom-option[data-value=""]').textContent;
                        inputElement.value = '';
                        customSelect.querySelector('.custom-option.selected').classList.remove('selected');
                        customSelect.querySelector('.custom-option[data-value=""]').classList.add('selected');
                    }
                };
            }

            // Setup custom selects
            const categorySelect = setupCustomSelect('#customCategorySelect', '#categoryDisplay', '#categoryFilter');
            const locationSelect = setupCustomSelect('#customLocationSelect', '#locationDisplay', '#locationFilter');
            const typeSelect = setupCustomSelect('#customTypeSelect', '#typeDisplay', '#typeFilter');

            // Set custom select values from URL parameters
            if (categoryParam) {
                const categoryOption = categorySelect.select.querySelector(`.custom-option[data-value="${categoryParam}"]`) ||
                                      categorySelect.select.querySelector(`.custom-option[data-value*="${categoryParam}"]`);
                if (categoryOption) {
                    categorySelect.display.textContent = categoryOption.textContent;
                    categorySelect.input.value = categoryOption.getAttribute('data-value');
                    categorySelect.select.querySelector('.custom-option.selected').classList.remove('selected');
                    categoryOption.classList.add('selected');
                }
            }

            if (locationParam) {
                const locationOption = locationSelect.select.querySelector(`.custom-option[data-value="${locationParam}"]`) ||
                                      locationSelect.select.querySelector(`.custom-option[data-value*="${locationParam}"]`);
                if (locationOption) {
                    locationSelect.display.textContent = locationOption.textContent;
                    locationSelect.input.value = locationOption.getAttribute('data-value');
                    locationSelect.select.querySelector('.custom-option.selected').classList.remove('selected');
                    locationOption.classList.add('selected');
                }
            }

            if (typeParam) {
                const typeOption = typeSelect.select.querySelector(`.custom-option[data-value="${typeParam}"]`) ||
                                  typeSelect.select.querySelector(`.custom-option[data-value*="${typeParam}"]`);
                if (typeOption) {
                    typeSelect.display.textContent = typeOption.textContent;
                    typeSelect.input.value = typeOption.getAttribute('data-value');
                    typeSelect.select.querySelector('.custom-option.selected').classList.remove('selected');
                    typeOption.classList.add('selected');
                }
            }

            // Job filtering
            const locationFilter = document.getElementById('locationFilter');
            const categoryFilter = document.getElementById('categoryFilter');
            const typeFilter = document.getElementById('typeFilter');
            const priceFilter = document.getElementById('priceFilter');
            const jobSearch = document.getElementById('jobSearch');
            const clearFiltersBtn = document.getElementById('clearFilters');
            const jobsList = document.getElementById('jobsList');
            const jobCards = document.querySelectorAll('.job-card');
            const noResults = document.getElementById('noResults');

            function filterJobs() {
                const locationValue = locationFilter.value.toLowerCase();
                const categoryValue = categoryFilter.value.toLowerCase();
                const typeValue = typeFilter.value.toLowerCase();
                const priceValue = priceFilter.value ? parseInt(priceFilter.value) : 0;
                const searchValue = jobSearch.value.toLowerCase().trim();

                // Store filter values in URL parameters for pagination
                const urlParams = new URLSearchParams(window.location.search);
                if (locationValue) urlParams.set('location', locationValue);
                else urlParams.delete('location');

                if (categoryValue) urlParams.set('category', categoryValue);
                else urlParams.delete('category');

                if (typeValue) urlParams.set('type', typeValue);
                else urlParams.delete('type');

                if (priceValue) urlParams.set('price', priceValue);
                else urlParams.delete('price');

                if (searchValue) urlParams.set('search', searchValue);
                else urlParams.delete('search');

                // Update pagination links with current filters
                document.querySelectorAll('.pagination a').forEach(link => {
                    const linkUrl = new URL(link.href);
                    const pageParam = linkUrl.searchParams.get('page');

                    // Create a new URL with the current filters
                    const newUrl = new URL(window.location.origin + window.location.pathname);
                    newUrl.searchParams = urlParams;
                    if (pageParam) newUrl.searchParams.set('page', pageParam);

                    // Update the link href
                    link.href = newUrl.toString();
                });

                let visibleCount = 0;

                jobCards.forEach(card => {
                    // Basic data attributes
                    const location = card.dataset.location ? card.dataset.location.toLowerCase() : '';
                    const category = card.dataset.category ? card.dataset.category.toLowerCase() : '';
                    const type = card.dataset.type ? card.dataset.type.toLowerCase() : '';
                    const price = card.dataset.price ? parseInt(card.dataset.price) : 0;

                    // Job title and description
                    const title = card.querySelector('.job-title-container h2') ?
                                 card.querySelector('.job-title-container h2').textContent.toLowerCase() : '';
                    const description = card.querySelector('.job-description') ?
                                       card.querySelector('.job-description').textContent.toLowerCase() : '';

                    // Client information
                    const clientName = card.querySelector('.client-name-text') ?
                                      card.querySelector('.client-name-text').textContent.toLowerCase() : '';
                    const clientFullName = card.querySelector('.client-full-name') ?
                                      card.querySelector('.client-full-name').textContent.toLowerCase() : '';

                    // Get all skill tags and value-text elements for comprehensive search
                    const skillTags = Array.from(card.querySelectorAll('.skill-tag')).map(tag =>
                                     tag.textContent.toLowerCase()).join(' ');

                    // Get all value-text elements (project size, duration, experience, etc.)
                    const valueTexts = Array.from(card.querySelectorAll('.value-text')).map(el =>
                                      el.textContent.toLowerCase()).join(' ');

                    // Get all job meta items for comprehensive search
                    const metaItems = Array.from(card.querySelectorAll('.job-meta-item')).map(item =>
                                     item.textContent.toLowerCase()).join(' ');

                    // Combine all searchable text
                    const allSearchableText = `${title} ${description} ${clientName} ${clientFullName} ${skillTags} ${valueTexts} ${metaItems} ${category} ${type}`;

                    // Check if matches filters
                    const matchesLocation = !locationValue || location.includes(locationValue);
                    const matchesCategory = !categoryValue || category.includes(categoryValue);
                    const matchesType = !typeValue || type.includes(typeValue);
                    const matchesPrice = !priceValue || price >= priceValue;

                    // For search, check if any word in the search query matches
                    let matchesSearch = true;
                    if (searchValue) {
                        const searchTerms = searchValue.split(/\s+/);
                        matchesSearch = searchTerms.every(term => allSearchableText.includes(term));
                    }

                    if (matchesLocation && matchesCategory && matchesType && matchesPrice && matchesSearch) {
                        card.style.display = '';
                        visibleCount++;
                    } else {
                        card.style.display = 'none';
                    }
                });

                noResults.style.display = visibleCount === 0 ? 'block' : 'none';
            }

            // Add event listeners
            if (locationFilter) locationFilter.addEventListener('change', filterJobs);
            if (categoryFilter) categoryFilter.addEventListener('change', filterJobs);
            if (typeFilter) typeFilter.addEventListener('change', filterJobs);
            if (priceFilter) priceFilter.addEventListener('input', filterJobs);
            if (jobSearch) jobSearch.addEventListener('input', filterJobs);

            // Add search button click handler
            const jobSearchBtn = document.getElementById('jobSearchBtn');
            if (jobSearchBtn) {
                jobSearchBtn.addEventListener('click', function() {
                    filterJobs();
                    // Removed animation
                });
            }

            // Add enter key press handler for search input
            if (jobSearch) {
                jobSearch.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        filterJobs();
                        // Removed animation
                    }
                });
            }

            // Clear filters
            if (clearFiltersBtn) {
                clearFiltersBtn.addEventListener('click', function() {
                    // Reset standard filters
                    priceFilter.value = '';
                    jobSearch.value = '';

                    // Reset custom selects
                    categorySelect.reset();
                    locationSelect.reset();
                    typeSelect.reset();

                    // Clear URL parameters and redirect to first page
                    window.location.href = window.location.pathname;
                });
            }

            // Profile dropdown functionality
            const profileDropdown = document.querySelector('.profile-dropdown');
            const profileButton = profileDropdown.querySelector('.profile-button');

            // Toggle dropdown on profile button click
            profileButton.addEventListener('click', function(e) {
                e.stopPropagation();
                profileDropdown.classList.toggle('active');
                console.log('Profile dropdown clicked, active:', profileDropdown.classList.contains('active'));
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!profileDropdown.contains(e.target)) {
                    profileDropdown.classList.remove('active');
                }
            });
        });
    </script>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>For Clients</h3>
                    <a href="{{ url_for('how_to_hire') }}">How to Hire</a>
                    <a href="{{ url_for('marketplace') }}">Marketplace</a>
                    <a href="{{ url_for('payroll_services') }}">Payroll Services</a>
                    <a href="{{ url_for('service_catalog') }}">Service Catalog</a>
                    <a href="{{ url_for('business_networking') }}">Business Networking</a>
                    <a href="{{ url_for('ph_business_loan') }}">PH Business Loan</a>
                </div>
                <div class="footer-section">
                    <h3>For Geniuses</h3>
                    <a href="{{ url_for('how_it_works') }}">How It Works?</a>
                    <a href="{{ url_for('why_cant_apply') }}">Why Can't I Apply?</a>
                    <a href="{{ url_for('direct_contracts') }}">Direct Contracts</a>
                    <a href="{{ url_for('find_mentors') }}">Find Mentors</a>
                    <a href="{{ url_for('mentor_application') }}">Mentor Application</a>
                    <a href="{{ url_for('ph_health_insurance') }}">PH Health Insurance</a>
                    <a href="{{ url_for('ph_life_insurance') }}">PH Life Insurance</a>
                </div>
                <div class="footer-section">
                    <h3>Resources</h3>
                    <a href="{{ url_for('help_and_support') }}">Help & Support</a>
                    <a href="{{ url_for('news_and_events') }}">News & Events</a>
                    <a href="{{ url_for('affiliate_program') }}">Affiliate Program</a>
                </div>
                <div class="footer-section">
                    <h3>Company</h3>
                    <a href="{{ url_for('about_us') }}">About Us</a>
                    <a href="{{ url_for('contact_us') }}">Contact Us</a>
                    <a href="{{ url_for('charity_projects') }}">Charity Projects</a>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="social-links">
                    <span>Follow Us:</span>
                    <a href="https://www.facebook.com/giggenius.io"><i class="bi bi-facebook"></i></a>
                    <a href="https://www.instagram.com/giggenius.io/"><i class="bi bi-instagram"></i></a>
                    <a href="https://twitter.com/giggenius_io"><i class="bi bi-twitter-x"></i></a>
                    <a href="https://www.tiktok.com/@giggenius.io"><i class="bi bi-tiktok"></i></a>
                    <a href="https://www.youtube.com/@giggenius"><i class="bi bi-youtube"></i></a>
                    <a href="https://www.linkedin.com/company/gig-genius/"><i class="bi bi-linkedin"></i></a>
                </div>
                <p>©2025 GigGenius by <a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
                <div class="footer-links">
                    <a href="{{ url_for('terms_of_service') }}">Terms of Service</a>
                    <a href="{{ url_for('privacy_policy') }}">Privacy Policy</a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>
