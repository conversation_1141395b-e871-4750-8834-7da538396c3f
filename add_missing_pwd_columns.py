import mysql.connector
import sys

# Database configuration - using the same as app.py
db_config = {
    'host': '**************',
    'user': 'giggenius_user',
    'password': 'Happiness1524!',
    'database': 'giggenius',
    'connect_timeout': 10,
    'use_pure': True,
    'autocommit': True
}

def add_missing_columns():
    """Add only the missing PWD approval columns"""
    try:
        print("🔄 Adding missing PWD approval columns...")
        print("=" * 50)
        
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()
        
        tables = ['register_genius', 'approve_genius', 'register_client', 'approve_client']
        
        # SQL commands to add missing columns
        add_column_commands = [
            "ADD COLUMN pwd_approval_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending' COMMENT 'PWD verification status'",
            "ADD COLUMN pwd_approved_by INT NULL COMMENT 'Admin ID who approved PWD status'",
            "ADD COLUMN pwd_approved_at DATETIME NULL COMMENT 'When PWD status was approved'",
            "ADD COLUMN pwd_rejection_reason TEXT NULL COMMENT 'Reason for PWD rejection (if applicable)'"
        ]
        
        for table in tables:
            print(f"\n📋 Processing table: {table}")
            
            for i, column_def in enumerate(add_column_commands, 1):
                try:
                    sql = f"ALTER TABLE {table} {column_def}"
                    print(f"   {i}. Adding column...")
                    cursor.execute(sql)
                    print(f"      ✅ Success")
                except mysql.connector.Error as err:
                    if "Duplicate column name" in str(err):
                        print(f"      ⚠️  Column already exists - skipping")
                    else:
                        print(f"      ❌ Error: {err}")
                        raise
        
        # Create indexes for the new columns
        print(f"\n📋 Creating indexes...")
        index_commands = [
            "CREATE INDEX idx_register_genius_pwd_approval ON register_genius(pwd_approval_status)",
            "CREATE INDEX idx_approve_genius_pwd_approval ON approve_genius(pwd_approval_status)",
            "CREATE INDEX idx_register_client_pwd_approval ON register_client(pwd_approval_status)",
            "CREATE INDEX idx_approve_client_pwd_approval ON approve_client(pwd_approval_status)"
        ]
        
        for i, index_cmd in enumerate(index_commands, 1):
            try:
                print(f"   {i}. Creating index...")
                cursor.execute(index_cmd)
                print(f"      ✅ Success")
            except mysql.connector.Error as err:
                if "Duplicate key name" in str(err):
                    print(f"      ⚠️  Index already exists - skipping")
                else:
                    print(f"      ❌ Error: {err}")
                    # Don't raise for index errors, continue
        
        # Update existing records
        print(f"\n📋 Updating existing records...")
        update_commands = [
            # Set approved status for existing PWD users
            "UPDATE register_genius SET pwd_approval_status = 'approved' WHERE is_pwd = 1",
            "UPDATE approve_genius SET pwd_approval_status = 'approved' WHERE is_pwd = 1",
            "UPDATE register_client SET pwd_approval_status = 'approved' WHERE is_pwd = 1",
            "UPDATE approve_client SET pwd_approval_status = 'approved' WHERE is_pwd = 1",
            # Set approved status for non-PWD users (they don't need PWD verification)
            "UPDATE register_genius SET pwd_approval_status = 'approved' WHERE is_pwd = 0",
            "UPDATE approve_genius SET pwd_approval_status = 'approved' WHERE is_pwd = 0",
            "UPDATE register_client SET pwd_approval_status = 'approved' WHERE is_pwd = 0",
            "UPDATE approve_client SET pwd_approval_status = 'approved' WHERE is_pwd = 0"
        ]
        
        for i, update_cmd in enumerate(update_commands, 1):
            try:
                print(f"   {i}. Updating records...")
                cursor.execute(update_cmd)
                affected_rows = cursor.rowcount
                print(f"      ✅ Updated {affected_rows} rows")
            except mysql.connector.Error as err:
                print(f"      ❌ Error: {err}")
                # Don't raise for update errors, continue
        
        cursor.close()
        conn.close()
        
        print("\n" + "=" * 50)
        print("✅ PWD approval columns added successfully!")
        print("\n📋 Summary:")
        print("   • Added pwd_approval_status field to all user tables")
        print("   • Added pwd_approved_by field for audit trail")
        print("   • Added pwd_approved_at timestamp field")
        print("   • Added pwd_rejection_reason field")
        print("   • Created indexes for better performance")
        print("   • Updated existing records with default values")
        
        return True
        
    except mysql.connector.Error as err:
        print(f"❌ Database Error: {err}")
        return False
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        return False

if __name__ == "__main__":
    success = add_missing_columns()
    sys.exit(0 if success else 1)
