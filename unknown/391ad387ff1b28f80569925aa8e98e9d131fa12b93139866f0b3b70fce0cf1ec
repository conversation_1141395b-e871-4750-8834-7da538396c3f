:root {
    /* Primary colors */
    --primary-blue: #004AAD;
    --primary-pink: #CD208B;
    --primary-light-blue: #E6F0FF;
    --primary-light-pink: #FFF0F8;
    
    /* Neutral colors */
    --neutral-100: #FFFFFF;
    --neutral-200: #F8FAFC;
    --neutral-300: #EEF2F6;
    --neutral-400: #E2E8F0;
    --neutral-500: #CBD5E1;
    --neutral-600: #94A3B8;
    --neutral-700: #64748B;
    --neutral-800: #334155;
    --neutral-900: #1E293B;
    
    /* Accent colors */
    --success: #10B981;
    --warning: #F59E0B;
    --error: #EF4444;
    --info: #3B82F6;
    
    /* Typography */
    --font-family: 'Poppins', system-ui, -apple-system, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    
    /* Borders */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-full: 9999px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    --shadow-blue: 0 8px 16px rgba(0, 74, 173, 0.15);
    --shadow-pink: 0 8px 16px rgba(205, 32, 139, 0.15);
    
    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--neutral-200);
    color: var(--neutral-900);
    line-height: 1.6;
    min-height: 100vh;
    padding: 0;
    margin: 0;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 20px auto;
    padding: var(--spacing-8);
    background-color: var(--neutral-100);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    min-height: calc(100vh - 40px);
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Navigation Header */
.navigation-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-6);
    padding-bottom: var(--spacing-4);
    border-bottom: 1px solid var(--neutral-300);
}

.back-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    background-color: var(--neutral-100);
    border: 2px solid var(--neutral-300);
    border-radius: var(--border-radius-md);
    color: var(--neutral-700);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-normal);
    font-family: var(--font-family);
    text-decoration: none;
}

.back-button:hover {
    background-color: var(--primary-light-blue);
    border-color: var(--primary-blue);
    color: var(--primary-blue);
    transform: translateX(-2px);
}

.back-button i {
    font-size: var(--font-size-sm);
}

.page-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--neutral-900);
    margin: 0;
}

/* Main content wrapper */
.main-content {
    display: flex;
    flex: 1;
}

/* Progress indicator */
.progress-indicator {
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-8);
    padding: var(--spacing-4) 0;
}

.progress-step {
    display: flex;
    align-items: center;
    margin: 0 var(--spacing-4);
}

.step-number {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--border-radius-full);
    background-color: var(--neutral-300);
    color: var(--neutral-700);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: var(--font-size-lg);
    margin-right: var(--spacing-2);
    transition: var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.step-number.active {
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
    color: var(--neutral-100);
    box-shadow: var(--shadow-blue);
}

.step-label {
    font-size: var(--font-size-sm);
    color: var(--neutral-700);
    font-weight: 500;
}

.step-label.active {
    color: var(--primary-blue);
    font-weight: 600;
}

.step-connector {
    flex: 1;
    height: 2px;
    background-color: var(--neutral-300);
    margin: 0 var(--spacing-2);
    min-width: 2rem;
    max-width: 4rem;
}

.step-connector.active {
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-pink));
}

/* Left side */
.left-side {
    width: 40%;
    padding-right: var(--spacing-8);
    border-right: 1px solid var(--neutral-300);
    display: flex;
    flex-direction: column;
}

/* Right side */
.right-side {
    width: 60%;
    padding-left: var(--spacing-8);
}

/* Step indicator */
.step {
    font-size: var(--font-size-sm);
    color: var(--primary-blue);
    margin-bottom: var(--spacing-4);
    background: var(--primary-light-blue);
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--border-radius-full);
    display: inline-block;
    font-weight: 500;
}

/* Title */
.title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
    color: var(--neutral-900);
    line-height: 1.3;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Description */
.description {
    color: var(--neutral-700);
    line-height: 1.8;
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-6);
}

/* Form elements */
input[type="text"] {
    width: 100%;
    padding: var(--spacing-4);
    font-size: var(--font-size-md);
    margin-top: var(--spacing-2);
    margin-bottom: var(--spacing-6);
    border: 2px solid var(--neutral-300);
    border-radius: var(--border-radius-md);
    transition: var(--transition-normal);
    font-family: var(--font-family);
}

input[type="text"]:focus {
    border-color: var(--primary-blue);
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
}

input[type="text"]::placeholder {
    color: var(--neutral-500);
}

/* Examples section */
.examples {
    background: var(--neutral-200);
    padding: var(--spacing-6);
    border-radius: var(--border-radius-lg);
    margin: var(--spacing-6) 0;
    border-left: 4px solid var(--primary-blue);
}

.examples strong {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-4);
    display: block;
    color: var(--neutral-900);
}

.examples ul {
    list-style-type: none;
    padding-left: 0;
    margin: var(--spacing-4) 0;
}

.examples ul li {
    padding: var(--spacing-2) 0;
    color: var(--neutral-700);
    position: relative;
    padding-left: var(--spacing-6);
    margin-bottom: var(--spacing-2);
}

.examples ul li:before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1rem;
    height: 1rem;
    background-color: var(--primary-light-blue);
    border-radius: var(--border-radius-full);
    border: 2px solid var(--primary-blue);
}

/* Job category */
.job-category {
    margin-top: var(--spacing-8);
}

.job-category strong {
    display: block;
    margin-bottom: var(--spacing-4);
    color: var(--neutral-900);
    font-size: var(--font-size-md);
}

.job-category label {
    display: flex;
    align-items: center;
    margin: var(--spacing-2) 0;
    font-size: var(--font-size-md);
    color: var(--neutral-800);
    padding: var(--spacing-3) var(--spacing-4);
    border-radius: var(--border-radius-md);
    transition: var(--transition-fast);
    cursor: pointer;
    border: 1px solid var(--neutral-300);
}

.job-category label:hover {
    background: var(--primary-light-blue);
    border-color: var(--primary-blue);
}

.job-category input[type="radio"] {
    margin-right: var(--spacing-3);
    accent-color: var(--primary-blue);
    width: 1.2rem;
    height: 1.2rem;
}

/* Category actions */
.category-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-6);
}

.see-all {
    color: var(--primary-blue);
    cursor: pointer;
    font-weight: 600;
    display: inline-block;
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--border-radius-md);
    transition: var(--transition-fast);
    font-size: var(--font-size-sm);
}

.see-all:hover {
    background: var(--primary-light-blue);
}

.next-button {
    background-color: var(--primary-blue);
    color: var(--neutral-100);
    padding: var(--spacing-3) var(--spacing-6);
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition-normal);
    font-size: var(--font-size-md);
    box-shadow: var(--shadow-blue);
}

.next-button:hover {
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-pink));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal-content {
    background-color: var(--neutral-100);
    margin: 10% auto;
    padding: var(--spacing-8);
    width: 90%;
    max-width: 550px;
    border-radius: var(--border-radius-lg);
    position: relative;
    box-shadow: var(--shadow-xl);
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close {
    position: absolute;
    right: var(--spacing-6);
    top: var(--spacing-6);
    font-size: var(--font-size-2xl);
    cursor: pointer;
    color: var(--neutral-600);
    transition: color 0.2s ease;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-full);
}

.close:hover {
    color: var(--neutral-900);
    background-color: var(--neutral-200);
}

.modal h2 {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-6);
    color: var(--neutral-900);
    font-weight: 700;
}

.label {
    font-size: var(--font-size-sm);
    color: var(--neutral-700);
    margin: var(--spacing-4) 0 var(--spacing-2) 0;
    font-weight: 500;
}

select {
    width: 100%;
    padding: var(--spacing-3);
    margin-bottom: var(--spacing-4);
    border: 2px solid var(--neutral-300);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-md);
    color: var(--neutral-900);
    appearance: none;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23606771' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E") no-repeat right 12px center;
    background-color: var(--neutral-100);
    transition: var(--transition-normal);
    font-family: var(--font-family);
}

select:focus {
    border-color: var(--primary-blue);
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
}

.modal-buttons {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-3);
    margin-top: var(--spacing-8);
}

.modal-buttons button {
    padding: var(--spacing-3) var(--spacing-6);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-md);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    font-family: var(--font-family);
}

.modal-buttons button:first-child {
    background-color: var(--neutral-200);
    border: none;
    color: var(--neutral-700);
}

.modal-buttons button:first-child:hover {
    background-color: var(--neutral-300);
    color: var(--neutral-900);
}

.modal-buttons button:last-child {
    background-color: var(--primary-blue);
    color: var(--neutral-100);
    border: none;
    box-shadow: var(--shadow-blue);
}

.modal-buttons button:last-child:hover {
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-pink));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: var(--spacing-4);
        margin: 10px;
    }

    .navigation-header {
        margin-bottom: var(--spacing-4);
        padding-bottom: var(--spacing-3);
    }

    .page-title {
        font-size: var(--font-size-lg);
    }

    .back-button {
        padding: var(--spacing-2) var(--spacing-3);
        font-size: var(--font-size-xs);
    }

    .main-content {
        flex-direction: column;
    }

    .left-side, .right-side {
        width: 100%;
        padding: 0;
    }

    .left-side {
        border-right: none;
        border-bottom: 1px solid var(--neutral-300);
        padding-bottom: var(--spacing-6);
        margin-bottom: var(--spacing-6);
    }
    
    .title {
        font-size: var(--font-size-2xl);
    }
    
    .examples {
        padding: var(--spacing-4);
    }
    
    .modal-content {
        width: 95%;
        padding: var(--spacing-6);
    }
}

/* Animation styles */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes fadeIn {
    0% { opacity: 0; transform: translateY(-10px); }
    100% { opacity: 1; transform: translateY(0); }
}

.pulse {
    animation: pulse 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-indicator {
    animation: fadeIn 0.8s ease-out forwards;
}

.step-number {
    position: relative;
}

.step-number::after {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
}

.step-number.active::after {
    opacity: 0.3;
    animation: pulse 1.5s infinite;
}

/* Decorative elements */
.container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 10% 10%, rgba(0, 74, 173, 0.03) 0%, transparent 30%),
        radial-gradient(circle at 90% 90%, rgba(205, 32, 139, 0.03) 0%, transparent 30%);
    pointer-events: none;
    z-index: -1;
    border-radius: var(--border-radius-lg);
}

/* Selected category styling */
.job-category label.selected {
    background-color: var(--primary-light-blue);
    border-color: var(--primary-blue);
    font-weight: 500;
}

/* Tooltip styles */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: var(--neutral-800);
    color: var(--neutral-100);
    text-align: center;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-3);
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s;
    font-size: var(--font-size-xs);
    box-shadow: var(--shadow-lg);
}

.tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--neutral-800) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}
