<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us | GigGenius</title>
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/logo.png') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>

        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
        }

        html {
            font-size: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: all 0.3s ease-in-out;
        }

        body {
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        /* Button Styles */
        .auth-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
            position: relative;
        }

        .btn {
            padding: 0 1.5rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 120px;
            height: 50px;
        }

        .btn-primary {
            background: var(--primary-pink);
            border: 2px solid var(--primary-pink);
            color: var(--text-light);
        }

        .btn-outline {
            background: white;
            border: 2px solid var(--primary-blue);
            color: var(--primary-blue);
        }

        .btn-gigs {
            background: var(--primary-blue);
            border: 2px solid var(--primary-blue);
            color: var(--text-light);
        }

        .btn-primary:hover {
            background: white;
            border: 2px solid var(--primary-pink);
            color: var(--primary-pink);
            text-decoration: none;
        }

        .btn-outline:hover {
            background: var(--primary-blue);
            color: var(--text-light);
            text-decoration: none;
        }

        .btn-gigs:hover {
            background: white;
            color: var(--primary-blue);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: var(--text-light);
            padding: 2rem;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            position: relative;
            text-align: center;
        }

        .modal-content h2 {
            font-size: 24px;
            color: var(--primary-blue);
        }

        .modal-buttons {
            margin-top: 1.5rem;
            display: flex;
            justify-content: center;
            padding: 0 2rem;
        }

        .modal-buttons .btn {
            width: auto;
            min-width: 160px;
            max-width: 80%;
            margin: 0 auto;
        }

        .social-login-btn {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            margin-bottom: 10px;
        }

        .social-login-btn img {
            width: 20px;
            height: 20px;
            object-fit: contain;
        }

        .close {
            position: absolute;
            right: 1rem;
            top: 0.1rem;
            font-size: 2rem;
            cursor: pointer;
        }

        .role-selection {
            margin: 2rem 0;
        }

        .role-option {
            border: 2px solid var(--primary-blue);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .role-option:hover {
            background-color: var(--text-light);
        }

        .role-option.selected {
            border-color: var(--primary-pink);
            background-color: var(--text-light);
        }

        .role-option input[type="radio"] {
            margin-right: 0.5rem;
        }

        .login-link {
            text-align: center;
            margin-top: 15px;
            font-size: 0.9rem;
            color: var(--text-gray);
        }

        .login-link a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        .login-modal-content {
            max-width: 400px;
            padding: 2rem;
        }

        .login-container {
            width: 100%;
        }

        .login-container h2 {
            text-align: center;
            color: var(--primary-blue);
            margin-bottom: 1.5rem;
        }

        .login-container form .btn-primary {
            width: auto;
        }

        .form-group {
            position: relative;
            margin-bottom: 1rem;
        }

        .form-group i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-gray);
        }

        .form-group input {
            width: 100%;
            padding: 0.8rem 1rem 0.8rem 2.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .checkbox-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .forgot-password-link {
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 0.9rem;
        }

        .forgot-password-link:hover {
            text-decoration: underline;
        }

        .or-separator {
            text-align: center;
            margin: 1rem 0;
            position: relative;
        }

        .or-separator::before,
        .or-separator::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 45%;
            height: 1px;
            background-color: #ddd;
        }

        .or-separator::before {
            left: 0;
        }

        .or-separator::after {
            right: 0;
        }

        .signup-link {
            text-align: center;
            margin-top: 15px;
            font-size: 0.9rem;
            color: var(--text-gray);
        }

        .signup-link a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
        }

        .signup-link a:hover {
            text-decoration: underline;
        }

        .forgot-password-modal {
            max-width: 400px;
            padding: 2rem;
        }

        .forgot-password-modal h2 {
            color: var(--primary-blue);
            margin-bottom: 1rem;
        }

        .forgot-password-modal input {
            width: 100%;
            padding: 0.8rem;
            margin: 1rem 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        /* Container Styles */
        .container {
            max-width: 2000px;
            margin: auto;
            padding: auto;
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0rem 1rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 5.5rem;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.7rem;
            color: var(--primary-pink);
            margin-right: 1rem;
        }

        .logo img {
            width: 5rem;
            height: 5rem;
        }

        .logo h1 {
            font-size: 1.7rem;
            margin-left: -0.5rem;
            font-weight: bold;
        }

        .nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            padding: 0.5rem 1rem;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .nav-links a:hover, .nav-links a.active {
            color: var(--primary-pink);
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .search-auth-content {
            display: flex;
            align-items: center;
            gap: 0;
            padding: 0.5rem 1rem;
        }

        .search-type-select {
            position: relative;
        }

        .search-type-button {
            height: 50px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0 1rem;
            border: 2px solid var(--primary-blue);
            border-radius: 8px 0 0 8px;
            border-right: none;
            background: white;
            cursor: pointer;
            font-size: 1rem;
            color: var(--primary-blue);
        }

        .search-type-button:hover {
            color: var(--primary-pink);
            border-color: var(--primary-pink);
        }

        .search-type-button:after {
            content: '▼';
            font-size: 1rem;
        }

        .search-type-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 8px;
            margin-top: 0.5rem;
            display: none;
            z-index: 1000;
            width: max-content;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .search-type-dropdown.active {
            display: block;
        }

        .search-type-option {
            padding: 1rem;
            cursor: pointer;
            color: var(--primary-blue);
        }

        .search-type-option:hover {
            color: var(--primary-pink);
        }

        .search-bar {
            height: 50px;
            display: flex;
            align-items: center;
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 0 8px 8px 0;
            width: 200px;
            margin-right: 1rem;
        }

        .search-bar:hover {
            border-color: var(--primary-pink);
        }

        .search-type-button:hover + .search-bar {
            border-color: var(--primary-pink);
        }

        .search-bar input {
            border: none;
            outline: none;
            padding: 0 1rem;
            width: 100%;
            height: 100%;
            font-size: 1rem;
        }

        .search-bar input:focus {
            outline: none;
        }

        .search-bar .icon {
            color: var(--primary-blue);
            padding: 0 0.5rem;
            font-size: 1rem;
        }

        .search-bar:hover .icon {
            color: var(--primary-pink);
        }

        /* Footer Styles */
        footer {
            background: var(--primary-blue);
            padding: 2.5rem 5%;
            align-items: center;
            padding-bottom: 2rem;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 3rem;
            margin-bottom: 2rem;
        }

        .footer-column h3 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: var(--text-light);
        }

        .footer-column a {
            font-size: 1.1rem;
            display: block;
            color: var(--text-light);
            text-decoration: none;
            margin-bottom: 0.5rem;
            transition: text-decoration 0.3s ease;
        }

        .footer-column a:hover {
            text-decoration: underline;
        }

        .footer-bottom {
            font-size: 1.1rem;
            color: var(--text-light);
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid white;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10rem;
        }

        .footer-bottom a {
            color: var(--text-light);
            margin: 0 10px;
            text-decoration: none;
        }

        .footer-bottom a:hover {
            text-decoration: underline;
        }

        .footer-bottom .social-icons img {
            width: 1rem;
            height: 1rem;
        }

        .social-icons .bi {
            font-size: 1.5rem;
            margin-right: 10px;
            border-radius: 50%;
            color: var(--text-light);
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .social-icons .bi:hover {
            transform: scale(1.2);
            color: var(--primary-pink);
        }

        /* Hamburger Menu Styles */
        .hamburger {
            position: relative; /* Change from fixed to relative */
            display: none;
            cursor: pointer;
            padding: 15px;
            background: none;
            border: none;
            z-index: 1001;
        }

        .hamburger span {
            display: block;
            width: 25px;
            height: 3px;
            margin: 5px 0;
            background-color: var(--primary-blue);
            transition: all 0.3s ease;
        }

        /* Side nav styles */
        .side-nav {
            position: fixed;
            top: 2rem;
            left: -100%;
            height: 100vh;
            width: 100%;
            background-color: white;
            z-index: 1000;
            transition: left 0.3s ease;
        }

        .side-nav.active {
            left: 0;
        }

        .side-nav-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0,0,0,0.5);
            z-index: 999;
            display: none;
        }

        .side-nav-overlay.active {
            display: block;
        }

        .side-nav-content {
            padding: 60px 0 20px 0;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            width: 100%;
            max-width: 300px;
            padding: 20px;
            color: var(--primary-blue);
            text-decoration: none;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
            font-size: 1.2rem;
            text-align: center;
            margin: 10px 0;
            font-weight: 500;
        }

        .nav-item:hover {
            color: var(--primary-pink);
        }

        .side-nav.active + .hamburger span {
            background-color: var(--primary-blue);
        }

        /* Contact Us Styles */
        .contact-hero {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-pink) 100%);
            color: white;
            padding: 6rem 2rem;
            text-align: center;
            margin-bottom: 3rem;
            width: 100%;
            position: relative;
            overflow: hidden;
            border-radius: 15px;
        }

        .contact-hero::before {
            content: '';
            position: absolute;
            top: -150%;
            left: -150%;
            width: 400%;
            height: 400%;
            background-image: url('../static/img/giggenius_logo.jpg');
            background-size: 150px 150px;
            background-repeat: repeat;
            transform: rotate(-15deg);
            opacity: 0.08;
            pointer-events: none;
            z-index: 1;
        }

        .contact-hero .highlight {
            color: var(--yellow);
        }

        .contact-hero h1,
        .contact-hero p {
            position: relative;
            z-index: 2;
        }

        .contact-hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
        }

        .contact-hero p {
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .content-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
        }

        .contact-info {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .contact-info h2 {
            color: var(--primary-blue);
        }

        .contact-card {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            padding: 1rem;
            border-radius: 10px;
            background: #f8f9fa;
            transition: transform 0.3s ease;
        }

        .contact-card:hover {
            transform: translateY(-5px);
        }

        .contact-card i {
            font-size: 1.5rem;
            color: var(--primary-blue);
            margin-right: 1rem;
        }

        .contact-form {
            background: white;
            padding: 2.5rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .contact-form h2 {
            color: var(--primary-blue);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            border-color: var(--primary-blue);
            outline: none;
        }

        .form-group select {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            background-color: white;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14L2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 1rem center;
            cursor: pointer;
        }

        .form-group select:focus {
            border-color: var(--primary-blue);
            outline: none;
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
        }

        .form-group select:hover {
            border-color: var(--primary-pink);
        }

        #otherSubjectGroup input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        #otherSubjectGroup input:focus {
            border-color: var(--primary-blue);
            outline: none;
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
        }

        #otherSubjectGroup input:hover {
            border-color: var(--primary-pink);
        }

        .map-container {
            grid-column: 1 / -1;
            height: 400px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .social-links {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-top: 0.5rem;
        }

        .social-links a {
            text-decoration: none;
            transition: transform 0.3s ease;
        }

        .social-links a:hover {
            transform: scale(1.1);
        }

        .social-links img {
            width: 2rem;
            height: 2rem;
            object-fit: contain;
        }

        .required {
            color: red;
        }
        .main-content {
            max-width: 1600px;
            margin: 0 auto;
            padding: 1rem;
        }
        /* TV */
        @media screen and (min-width: 1921px) and (max-width: 3840px) {
            .section1 .image {
                display: none;
            }
        }

        @media screen and (max-width: 1500px) {
            .navbar {
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem;
                background: white;
                z-index: 1003;
            }
            .logo img {
                width: 4rem;
                height: 4rem;
            }
        }

        @media screen and (max-width: 1300px) {
            .search-container,
            .search-type-select,
            .search-bar {
                display: none;
            }
        }

        /* Tablets & Small Laptops */
        @media screen and (max-width: 1200px) {
            .container {
                width: 100%;
                max-width: 100%;
                margin: 0;
                padding: 0;
                overflow-x: hidden;
            }
            .navbar {
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem;
                background: white;
                z-index: 1003;
            }
            .logo img {
                width: 4rem;
                height: 4rem;
            }
            .search-container,
            .search-type-select,
            .search-bar {
                display: none;
            }
        }

        @media screen and (max-width: 1024px) {
            .hamburger {
                display: block;
            }
            .nav-links {
                display: none;
            }
            .logo h1 {
                display: none;
            }
            .logo img {
                width: 4rem;
                height: 4rem;
            }
        }

        /* Mobile Devices */
        @media screen and (max-width: 768px) {
            footer {
                padding: 2rem 1rem;
            }
            .footer-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
                margin-bottom: 1.5rem;
            }
            .footer-column {
                margin-bottom: 1rem;
                border-bottom: 2px solid rgba(255, 255, 255, 0.2);
                padding-bottom: 1rem;
            }
            .footer-column h3 {
                font-size: 1.5rem;
                margin-bottom: 0.8rem;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-weight: 600;
                gap: 0.5rem;
            }
            .footer-column h3::after {
                content: '▼';
                font-size: 1rem;
                transition: transform 0.3s ease;
                margin-left: auto;
            }
            .footer-column h3.active::after {
                transform: rotate(180deg);
            }
            .footer-column .footer-links {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.3s ease-out;
            }
            .footer-column .footer-links.show {
                max-height: 500px;
            }
            .footer-column a {
                font-size: 1.2rem;
                margin-bottom: 0.5rem;
                display: block;
            }
            .footer-bottom {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
                border-top: none;
                padding: 0;
            }
            .footer-bottom p {
                font-size: 1.2rem;
                margin-bottom: 0.5rem;
            }
            .social-icons {
                display: flex;
                justify-content: center;
                gap: 1rem;
                margin-top: 0.5rem;
            }
            .social-icons .bi {
                font-size: 1.5rem;
            }
            .container {
                grid-template-columns: 1fr;
            }
            .contact-hero h1 {
                font-size: 2.5rem;
            }
        }

        @media screen and (max-width: 600px) {
            .sidebar {
                width: 100% !important;
                margin-bottom: 2rem;
            }
            .main-content {
                padding: 0 1rem !important;
            }
            .section-content {
                padding: 0 1rem;
            }
        }

        /* Small Mobile Devices */
        @media screen and (max-width: 480px) {
            .container {
                width: 100%;
                max-width: 100%;
                margin: 0;
                padding: 0;
                overflow-x: hidden;
                min-width: 320px; /* Add minimum width */
            }
            .navbar {
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0;
                background: white;
                z-index: 1003;
            }
            .hamburger {
                display: block;
            }
            .nav-links {
                display: none;
            }
            .logo {
                margin-left: -0.5rem;
            }
            .logo h1 {
                display: none;
            }
            .logo img {
                width: 3.5rem;
                height: 3.5rem;
            }
            .search-container,
            .search-type-select,
            .search-bar {
                display: none;
            }
            .navbar .auth-buttons {
                display: flex;
                gap: 0.4rem;
                align-items: center;
                position: relative;
                transform: scale(0.9);
                transform-origin: right;
                font-weight: 500;
            }
            .footer-column h3 {
                font-size: 1.2rem;
            }
            .footer-column a {
                font-size: 1rem;
            }
            .footer-bottom p {
                font-size: 1.1rem;
            }
            .social-icons {
                display: flex;
                justify-content: center;
                gap: 0.3rem;
                margin-top: 0.5rem;
            }
            .social-icons .bi {
                font-size: 1.5rem;
            }
            .footer-bottom .social-icons img {
                width: 20px;
                height: 20px;
            }
            .hamburger.active span:nth-child(1) {
                transform: rotate(45deg) translate(5px, 5px);
            }

            .hamburger.active span:nth-child(2) {
                opacity: 0;
            }

            .hamburger.active span:nth-child(3) {
                transform: rotate(-45deg) translate(7px, -7px);
            }
            .modal-content,
            .login-modal-content,
            .forgot-password-modal,
            .security-code-modal {
                width: 95%;
                max-height: 90vh;
                overflow-y: auto;
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 10000;
                padding: 1.5rem;
                align-items: center;
                text-align: center;
            }
            .modal h2,
            .modal p,
            .modal .login-container,
            .modal .signup-link,
            .modal .login-link,
            .modal .forgot-password-link,
            .modal .checkbox-container,
            .modal .or-separator {
                text-align: center;
            }
            .form-group {
                width: 100%;
                max-width: 100%;
                text-align: center;
                margin-bottom: 15px;
            }
            .form-group input {
                width: 100%;
                text-align: center;
                padding: 12px;
            }
            .role-selection {
                width: 100%;
                text-align: center;
            }
            .role-option {
                text-align: center;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 15px;
                margin: 10px 0;
            }
            .social-login-btn {
                width: 100%;
                justify-content: center;
                margin: 10px auto;
            }
            .modal-buttons {
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .modal-buttons .btn {
                min-width: 140px;
                padding: 0.8rem 1.2rem;
                margin: 0 auto;
            }
            #loginModal,
            #joinModal {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.7);
                align-items: center;
                justify-content: center;
                z-index: 9999;
            }

            .section {
                margin-bottom: 2rem;
            }

            .section-content {
                font-size: 0.9rem;
            }

            ul {
                padding-left: 1.5rem;
            }
        }
        @media (max-width: 360px) {
            /* Header/Navbar adjustments */
            .navbar {
                padding: 0.5rem;
                margin-left: -0.5rem;
            }

            .navbar .auth-buttons {
                gap: 0.3rem;
                margin-left: -1.8rem;
            }

            .navbar .auth-buttons .btn {
                padding: 0.4rem 0.8rem;
                font-size: 0.9rem;
            }

            .logo {
                margin-left: -0.5rem;
            }

            .contact-hero {
                padding: 2rem 1rem;
                text-align: center;
            }

            .contact-hero h1 {
                font-size: 1.8rem;
                margin-bottom: 0.8rem;
            }

            .contact-hero p {
                font-size: 0.9rem;
                line-height: 1.4;
                padding: 0 0.5rem;
            }

            .content-container {
                padding: 1rem;
            }

            .contact-grid {
                grid-template-columns: 1fr;  /* Change to single column */
                gap: 1.5rem;
            }

            .contact-info,
            .contact-form,
            .map-container {
                width: 100%;
            }

            /* Ensure proper spacing between sections */
            .contact-info {
                margin-bottom: 1.5rem;
            }

            .contact-form {
                margin-bottom: 1.5rem;
            }

            /* Adjust map height for better mobile view */
            .map-container {
                height: 250px;
            }

            .contact-info {
                padding: 1rem;
            }

            .contact-info h2 {
                font-size: 1.5rem;
                margin-bottom: 1rem;
            }

            .contact-card {
                padding: 0.8rem;
                margin-bottom: 1rem;
            }

            .contact-card h3 {
                font-size: 1rem;
            }

            .contact-card p {
                font-size: 0.9rem;
            }

            .contact-form {
                padding: 1rem;
            }

            .contact-form h2 {
                font-size: 1.5rem;
                margin-bottom: 1rem;
            }

            .form-group label {
                font-size: 0.9rem;
            }

            .form-group input,
            .form-group textarea {
                padding: 0.5rem;
                font-size: 0.9rem;
            }

            .map-container {
                height: 250px;
            }

            .modal-content,
            .login-modal-content,
            .forgot-password-modal,
            .security-code-modal {
                width: 95%;
                padding: 1rem;
                max-height: 85vh;
            }

            .form-group input {
                padding: 0.7rem 1rem 0.7rem 2.2rem;
                font-size: 0.9rem;
            }

            .modal-content h2 {
                font-size: 20px;
            }

            .close {
                right: 0.5rem;
                top: 0;
            }
        }
    </style>
</head>
<!-- Modal for Login -->
<div id="loginModal" class="modal">
    <div class="modal-content login-modal-content">
        <span class="close" onclick="closeLoginModal()">&times;</span>
        <div class="login-container">
            <h2>Login to GigGenius</h2>
            <div id="loginErrorMessage" style="color: red; margin-bottom: 1rem; display: none;"></div>
            <form id="loginForm" method="POST" action="{{ url_for('login') }}">
                <div class="form-group">
                    <i class="bi bi-envelope"></i>
                    <input type="email" name="email" id="email" placeholder="Email" required>
                </div>
                <div class="form-group">
                    <i class="bi bi-lock"></i>
                    <input type="password" name="password" id="password" placeholder="Password" required>
                </div>
                <div class="checkbox-container">
                    <label>
                        <input type="checkbox" id="showPassword" onclick="togglePasswordVisibility()"> Show Password
                    </label>
                    <a href="javascript:void(0)" class="forgot-password-link" onclick="openForgotPasswordModal()">Forgot Password?</a>
                </div>
                <button type="submit" class="btn btn-primary">LOGIN</button>
            </form>
            <div class="signup-link">
                Don't have a GigGenius account? <a href="javascript:void(0)" onclick="closeLoginModal(); openModal();">Sign Up</a>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Forgot Password -->
<div id="forgotPasswordModal" class="modal">
    <div class="modal-content forgot-password-modal">
        <span class="close" onclick="closeForgotPasswordModal()">&times;</span>
        <h2>Forgot Password</h2>
        <p>Please enter your email address to reset your password.</p>
        <input type="email" id="forgotPasswordEmail" placeholder="Email" required>
        <a href="javascript:void(0)" class="btn btn-primary" onclick="submitForgotPassword()" style="width: auto;">Submit</a>
    </div>
</div>

<!-- Modal for Join -->
<div id="joinModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeModal()">&times;</span>
        <h2>Join as Genius or Client</h2>
        <div id="roleMessage" style="color: red; display: none;">Please select a role!</div>
        <div class="role-selection">
            <div class="role-option" onclick="selectOption('genius')">
                <input type="radio" name="role" id="geniusRole">
                <label for="geniusRole">
                    <i class="bi bi-person"></i> I'm a Genius (Freelancer)
                </label>
            </div>
            <div class="role-option" onclick="selectOption('client')">
                <input type="radio" name="role" id="clientRole">
                <label for="clientRole">
                    <i class="bi bi-briefcase"></i> I'm a Client (Business Owner)
                </label>
            </div>
        </div>
        <div class="modal-buttons">
            <a href="javascript:void(0)" class="btn btn-primary" onclick="continueToRegistration()">Continue</a>
        </div>
        <p class="login-link">
            Already have an account? <a href="javascript:void(0)" onclick="closeModal(); openLoginModal()">Log In</a>
        </p>
    </div>
</div>

<div id="messageModal" class="modal">
    <div class="modal-content" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; padding: 2rem; border-radius: 10px; max-width: 400px;">
        <span class="close" onclick="closeMessageModal()" style="position: absolute; right: 15px; top: 10px;">&times;</span>
        <div id="modalContent">
            <i class="bi bi-check-circle-fill success-icon" style="color: #28a745; font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
            <h2>Thank You!</h2>
            <p>Your message has been sent successfully. We'll get back to you soon.</p>
        </div>
    </div>
</div>
<body>
    <!-- Add this side navigation panel right after the opening <body> tag -->
        <div class="side-nav" id="sideNav">
            <div class="side-nav-content">
                <div class="nav-items">
                    <a href="{{ url_for('landing_page') }}" class="nav-item">Home</a>
                    <a href="{{ url_for('affiliate_program') }}" class="nav-item">Affiliate Program</a>
                    <a href="{{ url_for('news_and_events') }}" class="nav-item">News & Events</a>
                    <a href="{{ url_for('why_giggenius') }}" class="nav-item">Why GigGenius</a>
                </div>
            </div>
        </div>
        <div class="side-nav-overlay" id="sideNavOverlay" onclick="toggleMenu()"></div>

        <!-- Main Content -->
        <div class="container">
            <!-- Navbar -->
            <nav class="navbar">
                <div style="display: flex; align-items: center;">
                    <button class="hamburger" onclick="toggleMenu()">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                    <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                        <div class="logo">
                            <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                            <h1>GigGenius</h1>
                        </div>
                    </a>
                    <div class="nav-links">
                        <a href="{{ url_for('landing_page') }}">Home</a>
                        <a href="{{ url_for('affiliate_program') }}">Affiliate Program</a>
                        <a href="{{ url_for('news_and_events') }}">News & Events</a>
                        <a href="{{ url_for('why_giggenius') }}">Why GigGenius</a>
                    </div>
                </div>
                <div class="navbar-right">
                    <div class="search-auth-content">
                        <div class="search-type-select">
                            <button class="search-type-button" id="searchTypeBtn">
                                <span id="selectedSearchType">All</span>
                            </button>
                            <div class="search-type-dropdown" id="searchTypeDropdown">
                                <div class="search-type-option" data-value="all">All</div>
                                <div class="search-type-option" data-value="genius">Geniuses</div>
                                <div class="search-type-option" data-value="gigs">Gigs</div>
                            </div>
                        </div>
                        <div class="search-bar">
                            <input type="text" id="searchInput" placeholder="Search...">
                            <i class="fas fa-search icon"></i>
                        </div>
                        <div class="auth-buttons">
                            <a href="javascript:void(0)" onclick="openLoginModal()" class="btn btn-outline">Log In</a>
                            <a href="javascript:void(0)" onclick="openModal()" class="btn btn-primary">Join</a>
                        </div>
                    </div>
                </div>
            </nav>

        <main class="main-content">
            <div class="contact-hero">
                <h1>Get in <span class="highlight">Touch</span></h1>
                <p>Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.</p>
            </div>

            <div class="content-container">
                <div class="contact-grid">
                    <div class="contact-info">
                        <h2>Contact Information</h2>
                        <div class="contact-card">
                            <i class="bi bi-geo-alt"></i>
                            <div>
                                <h3>Office Address:</h3>
                                <p>Unit 202 Rhodora Building,179 A. Bonifacio Avenue,<br>
                                Tañong, Marikina City</p>
                            </div>
                        </div>
                        <div class="contact-card">
                            <i class="bi bi-envelope"></i>
                            <div>
                                <h3>Email Us:</h3>
                                <p><EMAIL></p>
                            </div>
                        </div>
                    </div>

                    <div class="contact-form">
                        <h2>Send Us a Message</h2>
                        <form id="contactForm" onsubmit="return submitForm(event)">
                            <div class="form-group">
                                <label for="name" style="text-align: left;">Full Name <span class="required">*</span></label>
                                <input type="text" id="name" name="name" required>
                            </div>
                            <div class="form-group">
                                <label for="email" style="text-align: left;"> Email Address <span class="required">*</span></label>
                                <input type="email" id="email" name="email" required>
                            </div>
                            <div class="form-group">
                                <label for="subject" style="text-align: left;">Subject <span class="required">*</span></label>
                                <select id="subject" name="subject" required>
                                    <option value="" disabled selected>Select a subject</option>
                                    <option value="General Inquiry">General Inquiry</option>
                                    <option value="Technical Support">Technical Support</option>
                                    <option value="Billing Issue">Billing Issue</option>
                                    <option value="Feature Request">Feature Request</option>
                                    <option value="Bug Report">Bug Report</option>
                                    <option value="Partnership Opportunity">Partnership Opportunity</option>
                                    <option value="Account Issues">Account Issues</option>
                                    <option value="Feedback">Feedback</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                            <div class="form-group" id="otherSubjectGroup" style="display: none;">
                                <label for="otherSubject" style="text-align: left;">Specify Subject <span class="required">*</span></label>
                                <input type="text" id="otherSubject" name="otherSubject">
                            </div>
                            <div class="form-group">
                                <label for="message" style="text-align: left;">Message <span class="required">*</span></label>
                                <textarea id="message" name="message" rows="5" required></textarea>
                            </div>
                            <div style="text-align: center;">
                                <button type="submit" class="btn btn-primary">Send Message</button>
                            </div>
                        </form>
                    </div>

                    <div class="map-container">
                        <iframe
                            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d187!2d121.0863709!3d14.6342797!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3397b9b30b1b55a3%3A0xfb9c2206a082d53a!2sGigGenius!5e1!3m2!1sen!2sph!4v1709799072812!5m2!1sen!2sph"
                            width="100%"
                            height="100%"
                            style="border:0;"
                            allowfullscreen=""
                            loading="lazy"
                            referrerpolicy="no-referrer-when-downgrade">
                        </iframe>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer>
            <div class="footer-grid">
                <div class="footer-column">
                    <h3>For Clients</h3>
                    <div class="footer-links">
                    <a href="{{ url_for('how_to_hire') }}">How to Hire</a>
                    <a href="{{ url_for('accounting_services') }}">Accounting Services</a>
                    <a href="{{ url_for('events') }}">Events & Collaborations</a>
                    <a href="{{ url_for('ph_business_loan') }}">PH Business Loan</a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>For Geniuses</h3>
                    <div class="footer-links">
                    <a href="{{ url_for('how_it_works') }}">How It Works?</a>
                    <a href="{{ url_for('why_cant_apply') }}">About Rejected Accounts</a>
                    <a href="{{ url_for('find_mentors') }}">GigGenius University</a>
                    <a href="{{ url_for('ph_health_insurance') }}">PH Health Insurance</a>
                    <a href="{{ url_for('ph_life_insurance') }}">PH Life Insurance</a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <div class="footer-links">
                    <a href="{{ url_for('help_and_support') }}">Help & Support</a>
                    <a href="{{ url_for('news_and_events') }}">News & Events</a>
                    <a href="{{ url_for('affiliate_program') }}">Affiliate Program</a>
                    </div>
                </div>
                <div class="footer-column">
                    <h3>Company</h3>
                    <div class="footer-links">
                    <a href="{{ url_for('about_us') }}">About Us</a>
                    <a href="{{ url_for('contact_us') }}">Contact Us</a>
                    <a href="{{ url_for('charity_projects') }}">Charity Projects</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>Follow Us:
                    <span class="social-icons">
                        <a href="https://www.facebook.com/giggenius.io" class="bi bi-facebook"></a>
                        <a href="https://www.instagram.com/gig.genius.io/" class="bi bi-instagram"></a>
                        <a href="https://www.threads.com/@gig.genius.io" class="bi bi-threads"></a>
                        <a href="https://twitter.com/giggenius_io" class="bi bi-twitter-x"></a>
                        <a href="https://www.tiktok.com/@giggenius.io" class="bi bi-tiktok"></a>
                        <a href="https://www.youtube.com/@giggenius" class="bi bi-youtube"></a>
                        <a href="https://www.linkedin.com/company/gig-genius/" class="bi bi-linkedin"></a>
                    </span>
                </p>
                <p>©2025 GigGenius by<a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
                <p>
                    <a href="{{ url_for('terms_of_service') }}">Terms of Service</a> |
                    <a href="{{ url_for('privacy_policy') }}">Privacy Policy</a>
                </p>
            </div>
        </footer>
    </div>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-33SRJGWX6H"></script>
    <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-33SRJGWX6H');
    </script>

    <script>
        // Navbar
        function toggleMenu() {
            const sideNav = document.getElementById('sideNav');
            const overlay = document.getElementById('sideNavOverlay');
            const hamburger = document.querySelector('.hamburger');
            const spans = hamburger.getElementsByTagName('span');

            sideNav.classList.toggle('active');
            overlay.classList.toggle('active');

            // Animate hamburger
            if (sideNav.classList.contains('active')) {
                spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                spans[1].style.opacity = '0';
                spans[2].style.transform = 'rotate(-45deg) translate(7px, -7px)';
                document.body.style.overflow = 'hidden'; // Prevent scrolling when menu is open
            } else {
                spans[0].style.transform = 'none';
                spans[1].style.opacity = '1';
                spans[2].style.transform = 'none';
                document.body.style.overflow = 'auto'; // Restore scrolling when menu is closed
            }
        }

        // Close side nav when clicking outside
        document.addEventListener('click', (e) => {
            const sideNav = document.getElementById('sideNav');
            const hamburger = document.querySelector('.hamburger');

            if (sideNav.classList.contains('active') &&
                !sideNav.contains(e.target) &&
                !hamburger.contains(e.target)) {
                toggleMenu();
            }
        });

        document.addEventListener('DOMContentLoaded', function() {
            const footerHeadings = document.querySelectorAll('.footer-column h3');

            footerHeadings.forEach(heading => {
                heading.addEventListener('click', function() {
                    // Toggle active class on heading
                    this.classList.toggle('active');

                    // Get the next sibling element (the links container)
                    const linksContainer = this.nextElementSibling;

                    // Toggle the show class
                    linksContainer.classList.toggle('show');
                });
            });
        });

        let selectedRole = null;

        // Modal
        function closeAllModals() {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.style.display = 'none';
            });
            document.body.style.overflow = 'auto';
        }
        function openModal() {
            closeAllModals();
            document.getElementById('joinModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
        function closeModal() {
            document.getElementById('joinModal').style.display = 'none';
            document.body.style.overflow = 'auto';
            document.getElementById('roleMessage').style.display = 'none';
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.getElementById('geniusRole').checked = false;
            document.getElementById('clientRole').checked = false;
        }

        // Join Modal
        function selectOption(role) {
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });

            event.currentTarget.classList.add('selected');
            document.getElementById('roleMessage').style.display = 'none';

            if (role === 'genius') {
                document.getElementById('geniusRole').checked = true;
                document.getElementById('clientRole').checked = false;
            } else {
                document.getElementById('clientRole').checked = true;
                document.getElementById('geniusRole').checked = false;
            }
        }

        function continueToRegistration() {
            const geniusRole = document.getElementById('geniusRole').checked;
            const clientRole = document.getElementById('clientRole').checked;
            const roleMessage = document.getElementById('roleMessage');

            if (!geniusRole && !clientRole) {
                roleMessage.style.display = 'block';
                return;
            }

            if (geniusRole) {
                window.location.href = "{{ url_for('genius_registration') }}";
            } else {
                window.location.href = "{{ url_for('client_registration') }}";
            }
        }

        // Login Modal
        function openLoginModal() {
            closeAllModals();
            document.getElementById('loginModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
        function closeLoginModal() {
            document.getElementById('loginModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        function openForgotPasswordModal() {
            closeAllModals();
            document.getElementById('forgotPasswordModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
        function closeForgotPasswordModal() {
            document.getElementById('forgotPasswordModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        function togglePasswordVisibility() {
            const passwordInput = document.getElementById('password');
            passwordInput.type = passwordInput.type === 'password' ? 'text' : 'password';
        }
        function submitForgotPassword() {
            const email = document.getElementById('forgotPasswordEmail').value;
            closeForgotPasswordModal();
        }

        // Security Code Modal
        window.onclick = function(event) {
            const joinModal = document.getElementById('joinModal');
            const loginModal = document.getElementById('loginModal');
            const forgotPasswordModal = document.getElementById('forgotPasswordModal');

            if (event.target === joinModal) {
                closeModal();
            }
            if (event.target === loginModal) {
                closeLoginModal();
            }
            if (event.target === forgotPasswordModal) {
                closeForgotPasswordModal();
            }
        }

        // Scroll to Section
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Search Type Dropdown
        document.addEventListener('DOMContentLoaded', function() {
        const searchTypeBtn = document.getElementById('searchTypeBtn');
        const searchTypeDropdown = document.getElementById('searchTypeDropdown');
        const selectedSearchType = document.getElementById('selectedSearchType');
        const searchInput = document.getElementById('searchInput');
        const options = document.querySelectorAll('.search-type-option');
        // Toggle dropdown
        searchTypeBtn.addEventListener('click', function() {
            searchTypeDropdown.classList.toggle('active');
        });
        // Handle option selection
        options.forEach(option => {
            option.addEventListener('click', function() {
                const value = this.dataset.value;
                selectedSearchType.textContent = this.textContent;
                searchTypeDropdown.classList.remove('active');
                // Update placeholder based on selection
                const placeholders = {
                    genius: 'Search for genius...',
                    gigs: 'Search for gigs...',
                    projects: 'Search for projects...',
                    all: 'Search...'
                };
                searchInput.placeholder = placeholders[value] || placeholders.all;
            });
        });
        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.search-type-select')) {
                searchTypeDropdown.classList.remove('active');
            }
        });
        });

        // Login Form
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch('/login', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                console.log('Login response:', data);  // Debug log

                if (data.success) {
                    window.location.href = data.redirect;
                } else {
                    const errorMessage = document.getElementById('loginErrorMessage');
                    errorMessage.textContent = data.error;
                    errorMessage.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Login error:', error);  // Debug log
                const errorMessage = document.getElementById('loginErrorMessage');
                errorMessage.textContent = 'An error occurred during login';
                errorMessage.style.display = 'block';
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
            // Get all sidebar links
            const sidebarLinks = document.querySelectorAll('.sidebar-nav a');

            // Add click event listener to each link
            sidebarLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all links
                    sidebarLinks.forEach(l => l.classList.remove('active'));

                    // Add active class to clicked link
                    this.classList.add('active');

                    // Scroll to the target section
                    const targetId = this.getAttribute('href');
                    const targetSection = document.querySelector(targetId);
                    if (targetSection) {
                        targetSection.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });
        });
        function closeMessageModal() {
            document.getElementById('messageModal').style.display = 'none';
        }

        document.getElementById('subject').addEventListener('change', function() {
            const otherSubjectGroup = document.getElementById('otherSubjectGroup');
            const otherSubjectInput = document.getElementById('otherSubject');

            if (this.value === 'Other') {
                otherSubjectGroup.style.display = 'block';
                otherSubjectInput.required = true;
            } else {
                otherSubjectGroup.style.display = 'none';
                otherSubjectInput.required = false;
                otherSubjectInput.value = '';
            }
        });

        async function submitForm(event) {
            event.preventDefault();

            const form = document.getElementById('contactForm');
            const formData = new FormData(form);

            // Handle the subject field
            const subjectSelect = document.getElementById('subject');
            const otherSubject = document.getElementById('otherSubject');

            if (subjectSelect.value === 'Other') {
                formData.set('subject', otherSubject.value);
            }

            try {
                const response = await fetch('/submit_contact', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    document.getElementById('messageModal').style.display = 'block';
                    form.reset();
                    document.getElementById('otherSubjectGroup').style.display = 'none';
                } else {
                    alert('Error: ' + data.error);
                }
            } catch (error) {
                alert('An error occurred while sending your message. Please try again.');
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('messageModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }

        // LinkedIn OAuth function
        function signInWithLinkedIn() {
            window.location.href = "{{ url_for('auth_linkedin') }}";
        }

        // Google OAuth function (placeholder for future implementation)
        function signInWithGoogle() {
            alert("Google sign-in is not yet implemented.");
        }
    </script>
</body>
</html>
