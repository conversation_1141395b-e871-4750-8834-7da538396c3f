-- Create admin table for GigGenius application
-- This script creates the admin table and inserts the default admin user

-- Create admin table if it doesn't exist
CREATE TABLE IF NOT EXISTS admin (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    first_name <PERSON><PERSON><PERSON><PERSON>(50),
    last_name <PERSON><PERSON><PERSON><PERSON>(50),
    profile_picture_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Insert default admin user if not exists
-- Username: <EMAIL>
-- Password: Happiness1524!
INSERT IGNORE INTO admin (username, password, email, first_name, last_name, profile_picture_url) 
VALUES (
    '<EMAIL>', 
    'Happiness1524!', 
    '<EMAIL>', 
    'Admin', 
    'User',
    '/static/img/logo.png'
);

-- Show the admin table structure
DESCRIBE admin;

-- Show the admin users
SELECT id, username, email, first_name, last_name, created_at, is_active FROM admin;
