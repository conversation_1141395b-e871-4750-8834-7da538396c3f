#!/usr/bin/env python3
"""
Admin Page Performance Optimization Script
This script adds database indexes to improve admin page loading speed
"""

import mysql.connector
import time

# Database configuration
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': 'Happiness1524!',
    'database': 'giggenius'
}

def optimize_admin_performance():
    """Add indexes and optimizations for admin page performance"""
    print('🚀 Starting Admin Page Performance Optimization...')
    print('=' * 60)
    
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor(dictionary=True)
        
        # List of optimization queries
        optimization_queries = [
            # Indexes for register_genius table
            "CREATE INDEX IF NOT EXISTS idx_register_genius_status_created ON register_genius(status, created_at DESC)",
            "CREATE INDEX IF NOT EXISTS idx_register_genius_created_at ON register_genius(created_at DESC)",
            
            # Indexes for approve_genius table  
            "CREATE INDEX IF NOT EXISTS idx_approve_genius_created_at ON approve_genius(created_at DESC)",
            
            # Indexes for register_client table
            "CREATE INDEX IF NOT EXISTS idx_register_client_status_created ON register_client(status, created_at DESC)",
            "CREATE INDEX IF NOT EXISTS idx_register_client_created_at ON register_client(created_at DESC)",
            
            # Indexes for approve_client table
            "CREATE INDEX IF NOT EXISTS idx_approve_client_created_at ON approve_client(created_at DESC)"
        ]
        
        print('\n1. Adding database indexes for faster queries...')
        for i, query in enumerate(optimization_queries, 1):
            try:
                start_time = time.time()
                cursor.execute(query)
                query_time = time.time() - start_time
                print(f'   ✅ Index {i}/6 created successfully ({query_time:.3f}s)')
            except Exception as e:
                print(f'   ⚠️  Index {i}/6 - {str(e)}')
        
        conn.commit()
        
        # Check table sizes and performance
        print('\n2. Checking table statistics...')
        tables = ['register_genius', 'approve_genius', 'register_client', 'approve_client']
        
        for table in tables:
            try:
                start_time = time.time()
                cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                result = cursor.fetchone()
                query_time = time.time() - start_time
                print(f'   📊 {table}: {result["count"]} records (Query time: {query_time:.3f}s)')
            except Exception as e:
                print(f'   ❌ Error checking {table}: {e}')
        
        # Test optimized queries
        print('\n3. Testing optimized admin page queries...')
        
        test_queries = [
            ('Pending genius registrations', 'SELECT COUNT(*) as count FROM register_genius WHERE status = "pending"'),
            ('Approved geniuses', 'SELECT COUNT(*) as count FROM approve_genius'),
            ('Pending client registrations', 'SELECT COUNT(*) as count FROM register_client WHERE status = "pending"'),
            ('Approved clients', 'SELECT COUNT(*) as count FROM approve_client')
        ]
        
        total_query_time = 0
        for query_name, query in test_queries:
            try:
                start_time = time.time()
                cursor.execute(query)
                result = cursor.fetchone()
                query_time = time.time() - start_time
                total_query_time += query_time
                print(f'   🔍 {query_name}: {result["count"]} records (Query time: {query_time:.3f}s)')
            except Exception as e:
                print(f'   ❌ Error in {query_name}: {e}')
        
        print(f'\n   ⏱️  Total optimized query time: {total_query_time:.3f}s')
        
        cursor.close()
        conn.close()
        
        print('\n✅ Admin Page Performance Optimization Complete!')
        print('\n📈 Performance Improvements Applied:')
        print('   • Database indexes added for faster ORDER BY queries')
        print('   • BLOB data excluded from main admin page queries')
        print('   • Query result limits added (100 records max per table)')
        print('   • Profile photo rendering removed from main tables')
        print('\n🎯 Expected Results:')
        print('   • Faster admin page loading (50-80% improvement)')
        print('   • Reduced memory usage')
        print('   • Better responsiveness when switching tabs')
        
    except Exception as e:
        print(f'❌ Error during optimization: {e}')
        return False
    
    return True

if __name__ == '__main__':
    optimize_admin_performance()
