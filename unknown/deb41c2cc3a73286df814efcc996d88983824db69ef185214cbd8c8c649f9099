-- <PERSON>QL script to add 'not_applicable' option to PWD approval status enum
-- This handles existing users who registered before PWD system was implemented

-- First, let's check current enum values
SELECT COLUMN_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'giggenius' 
AND TABLE_NAME = 'register_genius' 
AND COLUMN_NAME = 'pwd_approval_status';

-- Add 'not_applicable' to the enum for all tables
-- This allows us to mark existing users as not applicable for PWD

-- Update register_genius table
ALTER TABLE register_genius 
MODIFY COLUMN pwd_approval_status ENUM('pending', 'approved', 'rejected', 'not_applicable') 
DEFAULT 'not_applicable' 
COMMENT 'PWD verification status - not_applicable for existing users';

-- Update approve_genius table
ALTER TABLE approve_genius 
MODIFY COLUMN pwd_approval_status ENUM('pending', 'approved', 'rejected', 'not_applicable') 
DEFAULT 'not_applicable' 
COMMENT 'PWD verification status - not_applicable for existing users';

-- Update register_client table
ALTER TABLE register_client 
MODIFY COLUMN pwd_approval_status ENUM('pending', 'approved', 'rejected', 'not_applicable') 
DEFAULT 'not_applicable' 
COMMENT 'PWD verification status - not_applicable for existing users';

-- Update approve_client table
ALTER TABLE approve_client 
MODIFY COLUMN pwd_approval_status ENUM('pending', 'approved', 'rejected', 'not_applicable') 
DEFAULT 'not_applicable' 
COMMENT 'PWD verification status - not_applicable for existing users';

-- Update existing users to have 'not_applicable' status
-- These are users who registered before PWD system was implemented

-- Update register_genius - existing users
UPDATE register_genius 
SET pwd_approval_status = 'not_applicable',
    is_pwd = 0,
    commission_rate = 10.00,
    pwd_condition = NULL,
    pwd_proof = NULL,
    pwd_approved_by = NULL,
    pwd_approved_at = NULL,
    pwd_rejection_reason = NULL
WHERE (is_pwd IS NULL OR is_pwd = 0)
AND (pwd_approval_status IS NULL OR pwd_approval_status = 'pending');

-- Update approve_genius - existing users
UPDATE approve_genius 
SET pwd_approval_status = 'not_applicable',
    is_pwd = 0,
    commission_rate = 10.00,
    pwd_condition = NULL,
    pwd_proof = NULL,
    pwd_approved_by = NULL,
    pwd_approved_at = NULL,
    pwd_rejection_reason = NULL
WHERE (is_pwd IS NULL OR is_pwd = 0)
AND (pwd_approval_status IS NULL OR pwd_approval_status = 'pending');

-- Update register_client - existing users
UPDATE register_client 
SET pwd_approval_status = 'not_applicable',
    is_pwd = 0,
    commission_rate = 10.00,
    pwd_condition = NULL,
    pwd_proof = NULL,
    pwd_approved_by = NULL,
    pwd_approved_at = NULL,
    pwd_rejection_reason = NULL
WHERE (is_pwd IS NULL OR is_pwd = 0)
AND (pwd_approval_status IS NULL OR pwd_approval_status = 'pending');

-- Update approve_client - existing users
UPDATE approve_client 
SET pwd_approval_status = 'not_applicable',
    is_pwd = 0,
    commission_rate = 10.00,
    pwd_condition = NULL,
    pwd_proof = NULL,
    pwd_approved_by = NULL,
    pwd_approved_at = NULL,
    pwd_rejection_reason = NULL
WHERE (is_pwd IS NULL OR is_pwd = 0)
AND (pwd_approval_status IS NULL OR pwd_approval_status = 'pending');

-- Verify the changes
SELECT 'register_genius' as table_name, pwd_approval_status, COUNT(*) as count
FROM register_genius 
GROUP BY pwd_approval_status
UNION ALL
SELECT 'approve_genius' as table_name, pwd_approval_status, COUNT(*) as count
FROM approve_genius 
GROUP BY pwd_approval_status
UNION ALL
SELECT 'register_client' as table_name, pwd_approval_status, COUNT(*) as count
FROM register_client 
GROUP BY pwd_approval_status
UNION ALL
SELECT 'approve_client' as table_name, pwd_approval_status, COUNT(*) as count
FROM approve_client 
GROUP BY pwd_approval_status
ORDER BY table_name, pwd_approval_status;

-- Show commission rate distribution
SELECT 'Commission Rate Distribution' as info, commission_rate, COUNT(*) as count
FROM (
    SELECT commission_rate FROM register_genius
    UNION ALL
    SELECT commission_rate FROM approve_genius
    UNION ALL
    SELECT commission_rate FROM register_client
    UNION ALL
    SELECT commission_rate FROM approve_client
) as all_users
GROUP BY commission_rate
ORDER BY commission_rate;
