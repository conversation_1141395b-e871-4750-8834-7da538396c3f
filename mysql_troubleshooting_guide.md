# MySQL Connection Troubleshooting Guide

This guide will help you troubleshoot and resolve the MySQL connection issues you're experiencing.

## 1. Check if MySQL Service is Running

Run the `check_mysql_service.py` script to verify if MySQL is running and accessible:

```
python check_mysql_service.py
```

If the MySQL service is not running, start it:
- Open Services (search for "services" in Windows search)
- Find "MySQL" in the list
- Right-click and select "Start"

## 2. Try Different Connection Methods

Run the `simple_db_test.py` script to try different connection configurations:

```
python simple_db_test.py
```

This script will try various combinations of:
- Different hosts (localhost vs 127.0.0.1)
- Different authentication methods
- Different users (giguser vs root)
- With and without passwords

## 3. Try PyMySQL Instead of mysql-connector

Install PyMySQL:

```
pip install pymysql
```

Then run the `try_pymysql.py` script:

```
python try_pymysql.py
```

Sometimes different database drivers can have different behavior with authentication.

## 4. Verify User Credentials in MySQL

Connect to MySQL using the command line:

```
mysql -u root -p
```

Enter your root password when prompted.

Then check if the giguser exists:

```sql
SELECT User, Host, plugin FROM mysql.user WHERE User = 'giguser';
```

Check the privileges:

```sql
SHOW GRANTS FOR 'giguser'@'localhost';
```

## 5. Recreate the User with Proper Authentication

If the user exists but you can't connect, recreate it with the proper authentication method:

```sql
-- Drop the user if it exists
DROP USER IF EXISTS 'giguser'@'localhost';
DROP USER IF EXISTS 'giguser'@'%';

-- Create the user with the mysql_native_password authentication method
CREATE USER 'giguser'@'localhost' IDENTIFIED WITH mysql_native_password BY 'Happiness1524!';
CREATE USER 'giguser'@'%' IDENTIFIED WITH mysql_native_password BY 'Happiness1524!';

-- Grant privileges
GRANT ALL PRIVILEGES ON giggenius.* TO 'giguser'@'localhost';
GRANT ALL PRIVILEGES ON giggenius.* TO 'giguser'@'%';

-- Flush privileges
FLUSH PRIVILEGES;
```

## 6. Check MySQL Configuration

The MySQL configuration file (my.ini or my.cnf) might have settings that are preventing connections. Look for:

- `bind-address`: Should be 127.0.0.1 or 0.0.0.0 to allow connections
- `skip-networking`: Should be commented out or set to OFF
- `default_authentication_plugin`: Should be mysql_native_password for older clients

## 7. Test Your Flask Application

Run your Flask application with the updated connection code:

```
python app.py
```

The updated code will try both mysql-connector and PyMySQL (if installed) and provide detailed error messages.

## 8. Common Issues and Solutions

1. **Access denied errors**:
   - Verify username and password
   - Check if the user has the correct privileges
   - Try recreating the user with the mysql_native_password authentication method

2. **Can't connect to MySQL server**:
   - Make sure MySQL is running
   - Check if the port is open (default is 3306)
   - Check firewall settings

3. **Unknown database errors**:
   - Verify the database name (case-sensitive in some configurations)
   - Check if the database exists
   - Make sure the user has access to the database

## 9. Last Resort: Use SQLite for Development

If you can't resolve the MySQL issues and need to continue development, consider temporarily switching to SQLite, which doesn't require a separate server:

```python
# SQLite configuration
import sqlite3

def get_db_connection():
    conn = sqlite3.connect('giggenius.db')
    conn.row_factory = sqlite3.Row
    return conn
```

This would require modifying your database schema and queries to be compatible with SQLite.
