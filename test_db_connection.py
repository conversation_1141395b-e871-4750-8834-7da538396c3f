import mysql.connector
import sys

# Database configuration - using the same settings as in app.py
db_config = {
    'host': 'localhost',
    'user': 'root',  # Using root user (XAMPP default)
    'password': '',  # Empty password (XAMPP default)
    'database': 'giggenius'  # Using lowercase database name
}

def test_connection():
    try:
        # Attempt to establish a connection
        conn = mysql.connector.connect(**db_config)

        # If connection is successful
        if conn.is_connected():
            print("✅ Successfully connected to MySQL database!")

            # Get server information
            db_info = conn.get_server_info()
            print(f"MySQL Server version: {db_info}")

            # Get cursor and execute a simple query
            cursor = conn.cursor()
            cursor.execute("SELECT DATABASE();")
            db_name = cursor.fetchone()[0]
            print(f"Connected to database: {db_name}")

            # List tables in the database
            cursor.execute("SHOW TABLES;")
            tables = cursor.fetchall()

            if tables:
                print("\nTables in the database:")
                for table in tables:
                    print(f"- {table[0]}")
            else:
                print("\nNo tables found in the database.")

            cursor.close()
            conn.close()
            return True

    except mysql.connector.Error as e:
        print(f"❌ Error connecting to MySQL database: {e}")
        return False

if __name__ == "__main__":
    success = test_connection()
    sys.exit(0 if success else 1)
