-- Admin Page Performance Optimization Script
-- This script adds indexes and optimizations to improve admin page loading speed

-- Add indexes for faster queries on admin page
-- These indexes will speed up the ORDER BY created_at DESC queries

-- Index for register_genius table
CREATE INDEX IF NOT EXISTS idx_register_genius_status_created ON register_genius(status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_register_genius_created_at ON register_genius(created_at DESC);

-- Index for approve_genius table  
CREATE INDEX IF NOT EXISTS idx_approve_genius_created_at ON approve_genius(created_at DESC);

-- Index for register_client table
CREATE INDEX IF NOT EXISTS idx_register_client_status_created ON register_client(status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_register_client_created_at ON register_client(created_at DESC);

-- Index for approve_client table
CREATE INDEX IF NOT EXISTS idx_approve_client_created_at ON approve_client(created_at DESC);

-- Show current table sizes for monitoring
SELECT 
    'register_genius' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_records,
    AVG(LENGTH(profile_photo)) as avg_photo_size_bytes
FROM register_genius
UNION ALL
SELECT 
    'approve_genius' as table_name,
    COUNT(*) as total_records,
    0 as pending_records,
    AVG(LENGTH(profile_photo)) as avg_photo_size_bytes
FROM approve_genius
UNION ALL
SELECT 
    'register_client' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_records,
    AVG(LENGTH(profile_photo)) as avg_photo_size_bytes
FROM register_client
UNION ALL
SELECT 
    'approve_client' as table_name,
    COUNT(*) as total_records,
    0 as pending_records,
    AVG(LENGTH(profile_photo)) as avg_photo_size_bytes
FROM approve_client;

-- Show indexes created
SHOW INDEX FROM register_genius WHERE Key_name LIKE 'idx_%';
SHOW INDEX FROM approve_genius WHERE Key_name LIKE 'idx_%';
SHOW INDEX FROM register_client WHERE Key_name LIKE 'idx_%';
SHOW INDEX FROM approve_client WHERE Key_name LIKE 'idx_%';
