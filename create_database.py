import mysql.connector
import sys

# Database configuration without specifying a database
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': 'Happiness1524!'
    # No database specified
}

def create_database():
    try:
        print("Attempting to connect to MySQL...")
        conn = mysql.connector.connect(**db_config)
        
        if conn.is_connected():
            print("✅ Successfully connected to MySQL!")
            
            cursor = conn.cursor()
            
            # Check if giggenius database exists
            print("\nChecking if 'giggenius' database exists...")
            cursor.execute("SHOW DATABASES LIKE 'giggenius';")
            result = cursor.fetchone()
            
            if result:
                print("✅ 'giggenius' database already exists")
            else:
                print("❌ 'giggenius' database does not exist")
                
                # Create the database
                print("\nCreating 'giggenius' database...")
                cursor.execute("CREATE DATABASE giggenius;")
                print("✅ 'giggenius' database created successfully!")
            
            # Use the giggenius database
            print("\nSwitching to 'giggenius' database...")
            cursor.execute("USE giggenius;")
            
            # Check if required tables exist
            print("\nChecking for required tables...")
            cursor.execute("SHOW TABLES;")
            tables = cursor.fetchall()
            
            existing_tables = [table[0].lower() for table in tables]
            print(f"Existing tables: {existing_tables if existing_tables else 'None'}")
            
            # Create required tables if they don't exist
            required_tables = {
                'genius': """
                CREATE TABLE genius (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    first_name VARCHAR(50) NOT NULL,
                    last_name VARCHAR(50) NOT NULL,
                    email VARCHAR(100) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    profile_photo VARCHAR(255),
                    expertise_level VARCHAR(50),
                    country VARCHAR(50),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """,
                'clients': """
                CREATE TABLE clients (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    first_name VARCHAR(50) NOT NULL,
                    last_name VARCHAR(50) NOT NULL,
                    work_email VARCHAR(100) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    profile_photo VARCHAR(255),
                    position VARCHAR(100),
                    country VARCHAR(50),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """
            }
            
            for table_name, create_sql in required_tables.items():
                if table_name.lower() not in existing_tables:
                    print(f"\nCreating '{table_name}' table...")
                    cursor.execute(create_sql)
                    print(f"✅ '{table_name}' table created successfully!")
                else:
                    print(f"\n'{table_name}' table already exists")
            
            # Show the final table list
            print("\nFinal list of tables in 'giggenius' database:")
            cursor.execute("SHOW TABLES;")
            tables = cursor.fetchall()
            
            if tables:
                for table in tables:
                    print(f"- {table[0]}")
            else:
                print("No tables found")
            
            cursor.close()
            conn.close()
            print("\nDatabase setup completed successfully!")
            return True
            
    except mysql.connector.Error as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = create_database()
    sys.exit(0 if success else 1)
