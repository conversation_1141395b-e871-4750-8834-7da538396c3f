# Deployment Guide for GigGenius

This guide explains how to deploy your Flask application to the same server as your MySQL database, ensuring proper database connectivity while maintaining your GitHub webhook auto-deployment workflow.

## Understanding Your Current Setup

Your application is already configured with:
1. A GitHub webhook endpoint (`/github-webhook`) that pulls the latest code when changes are pushed
2. Gunicorn as the WSGI server for production
3. MySQL database on the same server

## 1. Server Environment Setup

### Environment Variables

Create a `.env` file on your server (not in your Git repository) to store sensitive information:

```bash
# Create .env file in the application directory
sudo nano /var/www/gig-genius/.env
```

Add the following environment variables:

```
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=Happiness1524!
DB_NAME=giggenius
GITHUB_WEBHOOK_SECRET=Happines1524!
```

### Load Environment Variables

Create a script to load these variables before starting your application:

```bash
# Create env_loader.sh
sudo nano /var/www/gig-genius/env_loader.sh
```

Add the following content:

```bash
#!/bin/bash
set -a
source /var/www/gig-genius/.env
set +a
exec "$@"
```

Make it executable:

```bash
sudo chmod +x /var/www/gig-genius/env_loader.sh
```

## 2. Gunicorn Service Configuration

Update your Gunicorn service to use the environment loader:

```bash
sudo nano /etc/systemd/system/gunicorn.service
```

Ensure it contains:

```
[Unit]
Description=Gunicorn instance to serve GigGenius
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/var/www/gig-genius
ExecStart=/var/www/gig-genius/env_loader.sh /var/www/gig-genius/venv/bin/gunicorn --workers 3 --bind 0.0.0.0:8000 app:app
Restart=always

[Install]
WantedBy=multi-user.target
```

Reload and restart the service:

```bash
sudo systemctl daemon-reload
sudo systemctl restart gunicorn
```

## 3. Nginx Configuration (if using Nginx as reverse proxy)

Ensure Nginx is configured to proxy requests to Gunicorn:

```bash
sudo nano /etc/nginx/sites-available/gig-genius
```

Example configuration:

```
server {
    listen 80;
    server_name gig-genius.io www.gig-genius.io;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /static {
        alias /var/www/gig-genius/static;
    }
}
```

Enable the site and restart Nginx:

```bash
sudo ln -s /etc/nginx/sites-available/gig-genius /etc/nginx/sites-enabled/
sudo systemctl restart nginx
```

## 4. GitHub Webhook Setup

1. Go to your GitHub repository
2. Navigate to Settings > Webhooks > Add webhook
3. Set Payload URL to: `https://gig-genius.io/github-webhook`
4. Set Content type to: `application/json`
5. Set Secret to: `Happines1524!` (or your custom secret)
6. Select "Just the push event"
7. Ensure "Active" is checked
8. Click "Add webhook"

## 5. Testing the Deployment

1. Make a small change to your repository
2. Commit and push the change
3. Check the server logs to verify the webhook received the push and pulled the changes:

```bash
sudo journalctl -u gunicorn -f
```

## 6. Troubleshooting

### Webhook Issues
- Check webhook delivery in GitHub repository settings
- Verify the webhook secret matches
- Ensure the server can access GitHub (outbound connections)

### Database Connection Issues
- Verify MySQL is running: `sudo systemctl status mysql`
- Check MySQL user permissions: `mysql -u root -p`
- Verify database exists: `SHOW DATABASES;`

### Application Issues
- Check application logs: `sudo journalctl -u gunicorn -f`
- Verify environment variables are loaded: `sudo cat /proc/$(pgrep -f gunicorn)/environ | tr '\0' '\n' | grep DB_`

## 7. Local Development

For local development, you can:

1. Install MySQL locally
2. Create a local `.env` file with your development settings
3. Use a tool like `python-dotenv` to load environment variables

Example local `.env` file:
```
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_local_password
DB_NAME=giggenius
```

## 8. Security Considerations

- Keep your `.env` file secure and never commit it to your repository
- Regularly update your webhook secret
- Consider using a more secure password for your production database
- Set up proper firewall rules to restrict access to your server
