import mysql.connector
import sys

# Database configuration - using the same as app.py
db_config = {
    'host': '**************',
    'user': 'giggenius_user',
    'password': 'Happiness1524!',
    'database': 'giggenius',
    'connect_timeout': 10,
    'use_pure': True,
    'autocommit': True
}

def check_pwd_columns():
    """Check which PWD columns exist in the database"""
    try:
        print("🔍 Checking PWD columns in database...")
        print("=" * 50)
        
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()
        
        tables = ['register_genius', 'approve_genius', 'register_client', 'approve_client']
        pwd_columns = ['is_pwd', 'pwd_condition', 'pwd_proof', 'commission_rate', 
                      'pwd_approval_status', 'pwd_approved_by', 'pwd_approved_at', 'pwd_rejection_reason']
        
        for table in tables:
            print(f"\n📋 Table: {table}")
            
            # Get all columns for this table
            cursor.execute(f"DESCRIBE {table}")
            existing_columns = [row[0] for row in cursor.fetchall()]
            
            for column in pwd_columns:
                if column in existing_columns:
                    print(f"   ✅ {column} - EXISTS")
                else:
                    print(f"   ❌ {column} - MISSING")
        
        cursor.close()
        conn.close()
        
        print("\n" + "=" * 50)
        print("✅ Column check completed!")
        
        return True
        
    except mysql.connector.Error as err:
        print(f"❌ Database Error: {err}")
        return False
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        return False

if __name__ == "__main__":
    success = check_pwd_columns()
    sys.exit(0 if success else 1)
