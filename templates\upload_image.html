<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Upload Image</title>
  <style>
    .upload-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 50px;
    }

    .upload-circle {
      width: 150px;
      height: 150px;
      border-radius: 50%;
      background-color: #f0f0f0;
      border: 2px dashed #ccc;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      cursor: pointer;
      margin-bottom: 20px;
      position: relative;
    }

    .upload-circle img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: none;
    }

    .upload-circle input[type="file"] {
      display: none;
    }

    .upload-label {
      position: absolute;
      color: #555;
      text-align: center;
      pointer-events: none;
    }
  </style>
</head>
<body>
  <div class="upload-wrapper">
    <form method="POST" enctype="multipart/form-data">
      <label class="upload-circle" id="uploadCircle">
        <span class="upload-label" id="uploadLabel">Choose Image</span>
        <img id="preview" />
        <input type="file" name="file" accept="image/*" onchange="previewImage(event)" required>
      </label>
      <button type="submit">Submit</button>
    </form>

    {% if image_data %}
      <h2>Last Uploaded Image:</h2>
      <img src="data:image/jpeg;base64,{{ image_data }}" style="max-width: 300px; height: auto;" />
    {% endif %}

    <form method="GET" action="/view_images">
      <input type="submit" value="View All Images">
    </form>
  </div>

  <script>
    function previewImage(event) {
      const reader = new FileReader();
      const file = event.target.files[0];
      const preview = document.getElementById('preview');
      const label = document.getElementById('uploadLabel');

      reader.onload = function () {
        preview.src = reader.result;
        preview.style.display = 'block';
        label.style.display = 'none';
      };

      if (file) {
        reader.readAsDataURL(file);
      }
    }
  </script>
</body>
</html>
