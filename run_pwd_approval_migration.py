import mysql.connector
import sys

# Database configuration - using the same as app.py
db_config = {
    'host': '**************',
    'user': 'giggenius_user',
    'password': 'Happiness1524!',
    'database': 'giggenius',
    'connect_timeout': 10,
    'use_pure': True,
    'autocommit': True
}

def run_migration():
    """Run the PWD approval fields migration"""
    try:
        print("🔄 Starting PWD approval fields migration...")
        print("=" * 50)

        # Connect to database
        print(f"   Connecting to: {db_config['user']}@{db_config['host']}")
        conn = mysql.connector.connect(**db_config)
        print(f"   ✅ Connected successfully")

        cursor = conn.cursor()

        # Read the SQL file
        with open('add_pwd_approval_fields.sql', 'r') as file:
            sql_content = file.read()

        # Split SQL commands by semicolon and execute each one
        sql_commands = [cmd.strip() for cmd in sql_content.split(';') if cmd.strip()]

        for i, command in enumerate(sql_commands, 1):
            if command.upper().startswith(('ALTER', 'CREATE', 'UPDATE', 'DESCRIBE')):
                try:
                    print(f"   {i}. Executing: {command[:50]}...")
                    cursor.execute(command)
                    conn.commit()
                    print(f"      ✅ Success")
                except mysql.connector.Error as err:
                    if "Duplicate column name" in str(err):
                        print(f"      ⚠️  Column already exists - skipping")
                    elif "Duplicate key name" in str(err):
                        print(f"      ⚠️  Index already exists - skipping")
                    else:
                        print(f"      ❌ Error: {err}")
                        raise

        cursor.close()
        conn.close()

        print("\n" + "=" * 50)
        print("✅ PWD approval fields migration completed successfully!")
        print("\n📋 Migration Summary:")
        print("   • Added pwd_approval_status field to all user tables")
        print("   • Added pwd_approved_by field for audit trail")
        print("   • Added pwd_approved_at timestamp field")
        print("   • Added pwd_rejection_reason field")
        print("   • Created indexes for better performance")
        print("   • Updated existing records with default values")

        return True

    except FileNotFoundError:
        print("❌ Error: add_pwd_approval_fields.sql file not found")
        return False
    except mysql.connector.Error as err:
        print(f"❌ Database Error: {err}")
        return False
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        return False

if __name__ == "__main__":
    success = run_migration()
    sys.exit(0 if success else 1)
