import mysql.connector
import sys

# Try with explicit auth_plugin
db_config = {
    'host': 'localhost',
    'user': 'giguser',
    'password': 'Happiness1524!',
    'database': 'giggenius',
    'auth_plugin': 'mysql_native_password'  # Try with explicit auth plugin
}

def test_connection():
    try:
        print("Attempting to connect with explicit auth_plugin...")
        conn = mysql.connector.connect(**db_config)
        
        if conn.is_connected():
            print("✅ Successfully connected to MySQL database!")
            db_info = conn.get_server_info()
            print(f"MySQL Server version: {db_info}")
            cursor = conn.cursor()
            cursor.execute("SELECT DATABASE();")
            db_name = cursor.fetchone()[0]
            print(f"Connected to database: {db_name}")
            cursor.close()
            conn.close()
            return True
    except mysql.connector.Error as e:
        print(f"❌ Error connecting to MySQL database: {e}")
        return False

if __name__ == "__main__":
    success = test_connection()
    sys.exit(0 if success else 1)
