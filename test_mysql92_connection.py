import mysql.connector
import sys

def test_mysql_connection():
    # Try different combinations of credentials
    connection_configs = [
        {
            "params": {
                "host": "localhost",
                "user": "root",
                "password": "Happiness1524!"
            },
            "description": "Default credentials"
        },
        {
            "params": {
                "host": "127.0.0.1",  # Try IP instead of hostname
                "user": "root",
                "password": "Happiness1524!"
            },
            "description": "Using IP instead of hostname"
        },
        {
            "params": {
                "host": "localhost",
                "port": 3306,  # Explicitly specify default port
                "user": "root",
                "password": "Happiness1524!"
            },
            "description": "Explicitly specifying port 3306"
        },
        {
            "params": {
                "host": "localhost",
                "port": 3307,  # Try alternate port
                "user": "root",
                "password": "Happiness1524!"
            },
            "description": "Using alternate port 3307"
        },
        {
            "params": {
                "host": "localhost",
                "user": "root",
                "password": ""  # Empty password
            },
            "description": "Empty password"
        }
    ]

    success = False

    for config in connection_configs:
        try:
            print(f"\nAttempting connection with {config['description']}...")
            conn = mysql.connector.connect(**config['params'])

            if conn.is_connected():
                print(f"✅ Successfully connected to MySQL server with {config['description']}!")
                db_info = conn.get_server_info()
                print(f"MySQL Server version: {db_info}")

                # Try to create the giggenius database if it doesn't exist
                cursor = conn.cursor()

                # Check if giggenius database exists
                cursor.execute("SHOW DATABASES LIKE 'giggenius';")
                result = cursor.fetchone()

                if result:
                    print("✅ 'giggenius' database exists.")

                    # Now try to connect to the giggenius database
                    try:
                        cursor.execute("USE giggenius;")
                        print("✅ Successfully connected to 'giggenius' database.")

                        # Check tables
                        cursor.execute("SHOW TABLES;")
                        tables = cursor.fetchall()
                        if tables:
                            print("\nTables in the database:")
                            for table in tables:
                                print(f"- {table[0]}")
                        else:
                            print("\nNo tables found in the 'giggenius' database.")

                    except mysql.connector.Error as e:
                        print(f"❌ Error accessing 'giggenius' database: {e}")
                else:
                    print("❌ 'giggenius' database does not exist.")
                    print("Would you like to create it? (This script doesn't create it automatically)")

                cursor.close()
                conn.close()
                success = True

                print("\nSUCCESS! Use these connection parameters in your app.py:")
                for key, value in config['params'].items():
                    print(f"    '{key}': '{value}',")

                break  # Exit the loop if successful

        except mysql.connector.Error as e:
            print(f"❌ Error: {e}")

    if not success:
        print("\n❌ All connection attempts failed.")
        print("Please check that:")
        print("1. MySQL service is running")
        print("2. You're using the correct username and password")
        print("3. The MySQL server is configured to allow connections from your application")

    return success

if __name__ == "__main__":
    success = test_mysql_connection()
    sys.exit(0 if success else 1)
