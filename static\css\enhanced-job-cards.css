/* Enhanced Job Cards Styling */

/* ONLY ONE CARD PER ROW - NO GRID OR FLEX */
.jobs-list {
    display: block;
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    padding: 0;
}

/* Job card styling - LinkedIn Style */
.job-card {
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    width: 100%;
    margin-bottom: 15px !important;
    padding: 0;
}

/* Job header */
.job-header {
    display: flex;
    flex-direction: column;
    padding: 16px;
    background-color: #f3f7fb;
    border-bottom: 1px solid #e0e0e0;
    border-radius: 8px 8px 0 0;
}

/* Client profile container */
.client-profile-container {
    display: flex;
    flex-direction: column;
}

.client-profile-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.client-photo {
    width: 40px;
    height: 40px;
    margin-right: 10px;
    border-radius: 4px;
    object-fit: cover;
    border: 1px solid #e0e0e0;
}

.client-details {
    display: flex;
    flex-direction: column;
}

.client-name {
    font-size: 14px;
    font-weight: 600;
    color: #0a66c2;
    margin-bottom: 2px;
}

.client-position {
    font-size: 12px;
    color: #666;
}

/* Badge container */
.badge-container {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-top: 4px;
}

/* Location badge style */
.location-badge {
    display: inline-flex;
    align-items: center;
    background-color: #0a66c2;
    color: white;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    width: fit-content;
}

.location-badge i {
    margin-right: 4px;
    font-size: 10px;
}

/* Posted badge style */
.posted-badge {
    display: inline-block;
    background-color: #0a66c2;
    color: white;
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 4px;
    width: fit-content;
}

/* Job title section - LinkedIn Style */
.job-title-section {
    display: flex;
    flex-direction: column;
    padding: 16px;
    position: relative;
    border-bottom: 1px solid #e0e0e0;
}

.job-title-label {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    margin-bottom: 4px;
    position: relative;
    display: inline-block;
    border-bottom: 2px solid #0a66c2;
    padding-bottom: 2px;
    text-transform: uppercase;
}

.job-title-value {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    line-height: 1.3;
}

/* Budget badge styling */
.job-budget-badge {
    position: absolute;
    top: 16px;
    right: 16px;
    background-color: #f3f7fb;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 6px 10px;
    text-align: center;
}

.budget-type-label {
    font-size: 11px;
    color: #0a66c2;
    font-weight: 600;
    margin-bottom: 2px;
    text-transform: uppercase;
}

.budget-amount {
    font-size: 14px;
    font-weight: 700;
    color: #0a66c2;
}

/* Job description - LinkedIn Style */
.job-description {
    font-size: 14px;
    color: #555;
    line-height: 1.5;
    padding: 16px;
    border-bottom: 1px solid #e0e0e0;
}

/* Category and Specialty Section - LinkedIn Style */
.category-specialty-section {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    padding: 16px;
    border-bottom: 1px solid #e0e0e0;
}

.category-item, .specialty-item {
    display: flex;
    align-items: center;
}

.label {
    font-size: 12px;
    font-weight: 600;
    color: #444;
    margin-right: 5px;
}

.value-link {
    font-size: 12px;
    color: #0a66c2;
    font-weight: 500;
    text-decoration: none;
}

.value-link:hover {
    text-decoration: underline;
}

/* Job details section - LinkedIn Style */
.job-details-section {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    padding: 16px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f9f9f9;
}

.job-detail-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #555;
    background-color: white;
    padding: 6px 12px;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
}

.job-detail-item i {
    margin-right: 6px;
    color: #0a66c2;
    font-size: 12px;
}

/* Skills Section - LinkedIn Style */
.skills-section {
    padding: 16px;
    border-bottom: 1px solid #e0e0e0;
}

.skills-header {
    font-size: 12px;
    font-weight: 600;
    color: #444;
    margin-bottom: 10px;
}

.skills-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.skill-tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    background-color: #e1f0fa;
    color: #0a66c2;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    border: 1px solid rgba(10, 102, 194, 0.2);
}

/* Job footer - LinkedIn Style */
.job-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: auto;
    padding: 16px;
    background-color: white;
    border-top: 1px solid #e0e0e0;
}

.view-job-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    background-color: #0a66c2;
    color: white;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 600;
    text-decoration: none;
    transition: background-color 0.2s ease;
    border: none;
    cursor: pointer;
}

.view-job-btn:hover {
    background-color: #004182;
}

/* No jobs message */
.no-jobs-message {
    text-align: center;
    padding: 40px 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    color: #666;
}

.no-jobs-message i {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 15px;
}

.no-jobs-message h3 {
    font-size: 20px;
    margin-bottom: 10px;
    color: #333;
}

.no-jobs-message p {
    font-size: 14px;
    max-width: 400px;
    margin: 0 auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .job-title-budget {
        flex-direction: column;
    }

    .job-budget-container {
        text-align: left;
        margin-top: 10px;
    }

    .category-specialty-section {
        flex-direction: column;
        gap: 8px;
    }

    .skills-list {
        flex-wrap: wrap;
    }

    .skill-tag {
        margin-bottom: 5px;
    }
}
