# GigGenius Security Implementation Guide

This guide provides specific implementation steps to address the security issues identified in the GigGenius web application. It includes code examples, configuration changes, and best practices to improve the overall security posture of the application.

## Table of Contents

1. [Password Security](#password-security)
2. [Environment Variables](#environment-variables)
3. [CSRF Protection](#csrf-protection)
4. [Security Headers](#security-headers)
5. [Input Validation](#input-validation)
6. [File Upload Security](#file-upload-security)
7. [Session Security](#session-security)
8. [Database Security](#database-security)
9. [Error Handling](#error-handling)
10. [API Security](#api-security)

## Password Security

### Implementing Password Hashing

Replace plaintext password storage with secure password hashing using Werkzeug's security functions.

1. **Add the required imports**:

```python
from werkzeug.security import generate_password_hash, check_password_hash
```

2. **Modify user registration to hash passwords**:

```python
# When registering a new user
def register_user():
    # ... existing code ...
    
    # Hash the password before storing it
    hashed_password = generate_password_hash(
        request.form['password'],
        method='pbkdf2:sha256:150000'  # Use a strong hashing method with many iterations
    )
    
    # Store the hashed password in the database
    cursor.execute("""
        INSERT INTO users (username, password_hash, ...)
        VALUES (%s, %s, ...)
    """, (username, hashed_password, ...))
    
    # ... rest of the code ...
```

3. **Modify login to verify hashed passwords**:

```python
# When authenticating a user
def login():
    # ... existing code ...
    
    cursor.execute("""
        SELECT id, username, password_hash
        FROM users
        WHERE username = %s
    """, (username,))
    
    user = cursor.fetchone()
    
    if user and check_password_hash(user['password_hash'], request.form['password']):
        # Password is correct, log the user in
        session['user_id'] = user['id']
        # ... rest of login code ...
    else:
        # Invalid credentials
        return jsonify({'success': False, 'error': 'Invalid username or password'})
```

4. **Update database schema**:

```sql
-- Modify the users table to use password_hash instead of password
ALTER TABLE users CHANGE COLUMN password password_hash VARCHAR(255) NOT NULL;
```

### Implementing Password Policies

Add password strength validation to ensure users create strong passwords.

```python
def validate_password(password):
    """Validate password strength."""
    if len(password) < 12:
        return False, "Password must be at least 12 characters long"
    
    if not any(c.isupper() for c in password):
        return False, "Password must contain at least one uppercase letter"
    
    if not any(c.islower() for c in password):
        return False, "Password must contain at least one lowercase letter"
    
    if not any(c.isdigit() for c in password):
        return False, "Password must contain at least one number"
    
    if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?/" for c in password):
        return False, "Password must contain at least one special character"
    
    return True, "Password is strong"
```

## Environment Variables

### Moving Sensitive Configuration to Environment Variables

1. **Install python-dotenv**:

```bash
pip install python-dotenv
```

2. **Create a .env file** (do not commit this to version control):

```
# .env file
DB_HOST=**************
DB_USER=giggenius_user
DB_PASSWORD=Happiness1524!
DB_NAME=giggenius
SECRET_KEY=your-strong-fixed-secret-key
GITHUB_WEBHOOK_SECRET=Happines1524!
```

3. **Update app.py to use environment variables**:

```python
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY')

# Database configuration
db_config = {
    'host': os.environ.get('DB_HOST'),
    'user': os.environ.get('DB_USER'),
    'password': os.environ.get('DB_PASSWORD'),
    'database': os.environ.get('DB_NAME')
}
```

4. **Update webhook.py to use environment variables**:

```python
GITHUB_SECRET = os.environ.get("GITHUB_WEBHOOK_SECRET", "").encode()
```

5. **Add .env to .gitignore**:

```
# .gitignore
.env
```

## CSRF Protection

### Implementing CSRF Protection with Flask-WTF

1. **Install Flask-WTF**:

```bash
pip install Flask-WTF
```

2. **Initialize CSRF protection in app.py**:

```python
from flask_wtf.csrf import CSRFProtect

app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY')
csrf = CSRFProtect(app)
```

3. **Add CSRF token to forms**:

```html
<form method="POST" action="/login">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
    <!-- Rest of the form -->
</form>
```

4. **For AJAX requests, include the CSRF token in headers**:

```javascript
// Add this to your JavaScript files
function getCsrfToken() {
    return document.querySelector('meta[name="csrf-token"]').getAttribute('content');
}

// When making AJAX requests
fetch('/api/endpoint', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': getCsrfToken()
    },
    body: JSON.stringify(data)
})
```

5. **Add the CSRF token meta tag to your base template**:

```html
<head>
    <!-- Other head elements -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
```

## Security Headers

### Implementing Security Headers

1. **Install Flask-Talisman**:

```bash
pip install flask-talisman
```

2. **Initialize Talisman in app.py**:

```python
from flask_talisman import Talisman

app = Flask(__name__)
# ... other app configuration ...

# Configure security headers
csp = {
    'default-src': '\'self\'',
    'img-src': ['\'self\'', 'data:'],
    'script-src': ['\'self\''],
    'style-src': ['\'self\'', '\'unsafe-inline\''],
    'font-src': ['\'self\''],
    'frame-ancestors': ['\'none\'']
}

Talisman(app,
    content_security_policy=csp,
    content_security_policy_nonce_in=['script-src'],
    force_https=True,  # Set to False during local development
    strict_transport_security=True,
    strict_transport_security_preload=True,
    session_cookie_secure=True,
    session_cookie_http_only=True
)
```

## Input Validation

### Implementing Server-Side Input Validation

1. **Create validation functions**:

```python
def validate_email(email):
    """Validate email format."""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_name(name):
    """Validate name (no special characters, reasonable length)."""
    import re
    return re.match(r'^[a-zA-Z\s\'-]{2,50}$', name) is not None

def validate_phone(phone):
    """Validate phone number."""
    import re
    return re.match(r'^\+?[0-9]{10,15}$', phone) is not None
```

2. **Apply validation in routes**:

```python
@app.route('/register_genius', methods=['POST'])
def register_genius():
    # Get form data
    email = request.form.get('email', '').strip()
    first_name = request.form.get('firstName', '').strip()
    last_name = request.form.get('lastName', '').strip()
    phone = request.form.get('mobile', '').strip()
    password = request.form.get('password', '')
    
    # Validate inputs
    errors = []
    
    if not validate_email(email):
        errors.append('Invalid email format')
    
    if not validate_name(first_name):
        errors.append('Invalid first name')
    
    if not validate_name(last_name):
        errors.append('Invalid last name')
    
    if not validate_phone(phone):
        errors.append('Invalid phone number')
    
    is_valid_password, password_error = validate_password(password)
    if not is_valid_password:
        errors.append(password_error)
    
    if errors:
        return jsonify({
            'success': False,
            'errors': errors
        }), 400
    
    # Proceed with registration if validation passes
    # ...
```

## File Upload Security

### Implementing Secure File Upload Handling

1. **Validate file types and sizes**:

```python
def is_allowed_file(filename):
    """Check if the file has an allowed extension."""
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def secure_filename_with_uuid(filename):
    """Generate a secure filename with UUID to prevent path traversal."""
    import uuid
    from werkzeug.utils import secure_filename
    
    # Get the file extension
    ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
    
    # Generate a UUID and combine with secure filename
    return f"{uuid.uuid4().hex}.{ext}"

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400
    
    file = request.files['file']
    
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400
    
    if not is_allowed_file(file.filename):
        return jsonify({'error': 'File type not allowed'}), 400
    
    # Check file size (limit to 5MB)
    if len(file.read()) > 5 * 1024 * 1024:
        return jsonify({'error': 'File too large (max 5MB)'}), 400
    
    # Reset file pointer after reading
    file.seek(0)
    
    # Generate secure filename
    filename = secure_filename_with_uuid(file.filename)
    
    # Save the file to a secure location outside web root
    upload_folder = os.path.join(app.root_path, 'secure_uploads')
    os.makedirs(upload_folder, exist_ok=True)
    file.save(os.path.join(upload_folder, filename))
    
    return jsonify({'success': True, 'filename': filename})
```

## Session Security

### Enhancing Session Security

1. **Configure secure session settings in app.py**:

```python
import datetime

app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY')

# Session configuration
app.config['SESSION_COOKIE_SECURE'] = True  # Only send cookie over HTTPS
app.config['SESSION_COOKIE_HTTPONLY'] = True  # Prevent JavaScript access to session cookie
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'  # Restrict cookie to same site
app.config['PERMANENT_SESSION_LIFETIME'] = datetime.timedelta(hours=1)  # Session expiration
```

2. **Implement session regeneration on login and privilege changes**:

```python
@app.route('/login', methods=['POST'])
def login():
    # ... authentication code ...
    
    if user and check_password_hash(user['password_hash'], password):
        # Clear any existing session
        session.clear()
        
        # Regenerate the session
        session.regenerate()
        
        # Set session data
        session['user_id'] = user['id']
        session['user_type'] = user['role']
        session['login_time'] = datetime.datetime.now().isoformat()
        
        # Set session to expire after inactivity
        session.permanent = True
        
        # ... rest of login code ...
```

## Database Security

### Enhancing Database Security

1. **Implement connection pooling**:

```python
from mysql.connector.pooling import MySQLConnectionPool

# Create a connection pool
db_pool = None

def initialize_db_pool():
    global db_pool
    db_pool = MySQLConnectionPool(
        pool_name="giggenius_pool",
        pool_size=5,
        **db_config
    )

def get_db_connection():
    try:
        conn = db_pool.get_connection()
        return conn
    except Error as e:
        print(f"Error connecting to database: {e}")
        return None

# Initialize the pool when the app starts
initialize_db_pool()
```

2. **Use context managers for database connections**:

```python
def execute_query(query, params=None):
    """Execute a query and return the results."""
    conn = None
    cursor = None
    try:
        conn = get_db_connection()
        if conn is None:
            return None
        
        cursor = conn.cursor(dictionary=True)
        cursor.execute(query, params or ())
        
        if query.strip().upper().startswith('SELECT'):
            return cursor.fetchall()
        else:
            conn.commit()
            return cursor.rowcount
    except Error as e:
        if conn:
            conn.rollback()
        print(f"Database error: {e}")
        return None
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
```

## Error Handling

### Implementing Proper Error Handling

1. **Create custom error handlers**:

```python
@app.errorhandler(404)
def page_not_found(e):
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_server_error(e):
    return render_template('errors/500.html'), 500

@app.errorhandler(403)
def forbidden(e):
    return render_template('errors/403.html'), 403

@app.errorhandler(400)
def bad_request(e):
    return render_template('errors/400.html'), 400
```

2. **Implement a generic exception handler**:

```python
@app.errorhandler(Exception)
def handle_exception(e):
    # Log the error
    app.logger.error(f"Unhandled exception: {str(e)}")
    
    # Return a generic error message in production
    if app.config['DEBUG']:
        # In development, show detailed error information
        return render_template('errors/exception.html', error=str(e)), 500
    else:
        # In production, show a generic error message
        return render_template('errors/500.html'), 500
```

## API Security

### Implementing API Security

1. **Add rate limiting**:

```bash
pip install Flask-Limiter
```

```python
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

# Apply rate limiting to specific endpoints
@app.route('/api/login', methods=['POST'])
@limiter.limit("5 per minute")
def api_login():
    # ... login code ...
```

2. **Implement API authentication with tokens**:

```python
import jwt
import datetime

# Function to generate a JWT token
def generate_token(user_id, role):
    payload = {
        'exp': datetime.datetime.utcnow() + datetime.timedelta(days=1),
        'iat': datetime.datetime.utcnow(),
        'sub': user_id,
        'role': role
    }
    return jwt.encode(
        payload,
        os.environ.get('JWT_SECRET_KEY'),
        algorithm='HS256'
    )

# Decorator to verify JWT token
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        
        # Get token from Authorization header
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
        
        if not token:
            return jsonify({'message': 'Token is missing'}), 401
        
        try:
            # Decode the token
            data = jwt.decode(
                token,
                os.environ.get('JWT_SECRET_KEY'),
                algorithms=['HS256']
            )
            current_user_id = data['sub']
            current_user_role = data['role']
        except:
            return jsonify({'message': 'Token is invalid'}), 401
        
        # Pass the user info to the decorated function
        return f(current_user_id, current_user_role, *args, **kwargs)
    
    return decorated

# Example protected API endpoint
@app.route('/api/protected', methods=['GET'])
@token_required
def protected_api(current_user_id, current_user_role):
    return jsonify({'message': f'Hello User {current_user_id} with role {current_user_role}'})
```

## Implementation Checklist

- [ ] Implement password hashing
- [ ] Move sensitive configuration to environment variables
- [ ] Add CSRF protection
- [ ] Configure security headers
- [ ] Enhance input validation
- [ ] Improve file upload security
- [ ] Strengthen session security
- [ ] Enhance database security
- [ ] Implement proper error handling
- [ ] Add API security measures
