-- Create the giggenius database if it doesn't exist
CREATE DATABASE IF NOT EXISTS giggenius;

-- Create a new user for the application
-- Replace 'your_password' with a secure password
CREATE USER IF NOT EXISTS 'giggenius_user'@'localhost' IDENTIFIED BY 'Happiness1524!';

-- Grant privileges to the new user on the giggenius database
GRANT ALL PRIVILEGES ON giggenius.* TO 'giggenius_user'@'localhost';

-- Apply the changes
FLUSH PRIVILEGES;
