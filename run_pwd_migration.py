#!/usr/bin/env python3
"""
Script to run PWD database migration
"""

import mysql.connector
import sys

# Database configuration (using same as app.py)
db_config = {
    'host': '**************',
    'user': 'giggenius_user',
    'password': 'Happiness1524!',
    'database': 'giggenius',
    'connect_timeout': 10,
    'use_pure': True,
    'autocommit': True
}

# SQL commands to add PWD fields
sql_commands = [
    # Add PWD fields to register_genius table
    """
    ALTER TABLE register_genius
    ADD COLUMN is_pwd TINYINT(1) DEFAULT 0 COMMENT 'Whether user is a Person with Disability',
    ADD COLUMN pwd_condition TEXT NULL COMMENT 'Description of PWD condition (internal use only)',
    ADD COLUMN pwd_proof LONGBLOB NULL COMMENT 'PWD proof document data',
    ADD COLUMN commission_rate DECIMAL(5,2) DEFAULT 10.00 COMMENT 'Commission rate percentage (10.00 for regular, 5.00 for PWD)',
    ADD COLUMN pwd_approval_status ENUM('pending', 'approved', 'rejected', 'not_applicable') DEFAULT 'not_applicable' COMMENT 'PWD verification status',
    ADD COLUMN pwd_approved_by INT NULL COMMENT 'Admin ID who approved/rejected PWD status',
    ADD COLUMN pwd_approved_at DATETIME NULL COMMENT 'When PWD status was approved/rejected',
    ADD COLUMN pwd_rejection_reason TEXT NULL COMMENT 'Reason for PWD rejection'
    """,

    # Add PWD fields to approve_genius table (for approved users)
    """
    ALTER TABLE approve_genius
    ADD COLUMN is_pwd TINYINT(1) DEFAULT 0 COMMENT 'Whether user is a Person with Disability',
    ADD COLUMN pwd_condition TEXT NULL COMMENT 'Description of PWD condition (internal use only)',
    ADD COLUMN pwd_proof LONGBLOB NULL COMMENT 'PWD proof document data',
    ADD COLUMN commission_rate DECIMAL(5,2) DEFAULT 10.00 COMMENT 'Commission rate percentage (10.00 for regular, 5.00 for PWD)',
    ADD COLUMN pwd_approval_status ENUM('pending', 'approved', 'rejected', 'not_applicable') DEFAULT 'not_applicable' COMMENT 'PWD verification status',
    ADD COLUMN pwd_approved_by INT NULL COMMENT 'Admin ID who approved/rejected PWD status',
    ADD COLUMN pwd_approved_at DATETIME NULL COMMENT 'When PWD status was approved/rejected',
    ADD COLUMN pwd_rejection_reason TEXT NULL COMMENT 'Reason for PWD rejection'
    """,

    # Add PWD fields to register_client table (clients can also be PWD)
    """
    ALTER TABLE register_client
    ADD COLUMN is_pwd TINYINT(1) DEFAULT 0 COMMENT 'Whether user is a Person with Disability',
    ADD COLUMN pwd_condition TEXT NULL COMMENT 'Description of PWD condition (internal use only)',
    ADD COLUMN pwd_proof LONGBLOB NULL COMMENT 'PWD proof document data',
    ADD COLUMN commission_rate DECIMAL(5,2) DEFAULT 10.00 COMMENT 'Commission rate percentage (10.00 for regular, 5.00 for PWD)',
    ADD COLUMN pwd_approval_status ENUM('pending', 'approved', 'rejected', 'not_applicable') DEFAULT 'not_applicable' COMMENT 'PWD verification status',
    ADD COLUMN pwd_approved_by INT NULL COMMENT 'Admin ID who approved/rejected PWD status',
    ADD COLUMN pwd_approved_at DATETIME NULL COMMENT 'When PWD status was approved/rejected',
    ADD COLUMN pwd_rejection_reason TEXT NULL COMMENT 'Reason for PWD rejection'
    """,

    # Add PWD fields to approve_client table (for approved clients)
    """
    ALTER TABLE approve_client
    ADD COLUMN is_pwd TINYINT(1) DEFAULT 0 COMMENT 'Whether user is a Person with Disability',
    ADD COLUMN pwd_condition TEXT NULL COMMENT 'Description of PWD condition (internal use only)',
    ADD COLUMN pwd_proof LONGBLOB NULL COMMENT 'PWD proof document data',
    ADD COLUMN commission_rate DECIMAL(5,2) DEFAULT 10.00 COMMENT 'Commission rate percentage (10.00 for regular, 5.00 for PWD)',
    ADD COLUMN pwd_approval_status ENUM('pending', 'approved', 'rejected', 'not_applicable') DEFAULT 'not_applicable' COMMENT 'PWD verification status',
    ADD COLUMN pwd_approved_by INT NULL COMMENT 'Admin ID who approved/rejected PWD status',
    ADD COLUMN pwd_approved_at DATETIME NULL COMMENT 'When PWD status was approved/rejected',
    ADD COLUMN pwd_rejection_reason TEXT NULL COMMENT 'Reason for PWD rejection'
    """,

    # Create indexes for better performance
    "CREATE INDEX idx_register_genius_is_pwd ON register_genius(is_pwd)",
    "CREATE INDEX idx_approve_genius_is_pwd ON approve_genius(is_pwd)",
    "CREATE INDEX idx_register_client_is_pwd ON register_client(is_pwd)",
    "CREATE INDEX idx_approve_client_is_pwd ON approve_client(is_pwd)",

    # Update existing records to have default commission rates and 'not_applicable' PWD status
    "UPDATE register_genius SET commission_rate = 10.00, pwd_approval_status = 'not_applicable' WHERE commission_rate IS NULL",
    "UPDATE approve_genius SET commission_rate = 10.00, pwd_approval_status = 'not_applicable' WHERE commission_rate IS NULL",
    "UPDATE register_client SET commission_rate = 10.00, pwd_approval_status = 'not_applicable' WHERE commission_rate IS NULL",
    "UPDATE approve_client SET commission_rate = 10.00, pwd_approval_status = 'not_applicable' WHERE commission_rate IS NULL",

    # Ensure all existing users (non-PWD) have 'not_applicable' status
    "UPDATE register_genius SET pwd_approval_status = 'not_applicable' WHERE is_pwd = 0 OR is_pwd IS NULL",
    "UPDATE approve_genius SET pwd_approval_status = 'not_applicable' WHERE is_pwd = 0 OR is_pwd IS NULL",
    "UPDATE register_client SET pwd_approval_status = 'not_applicable' WHERE is_pwd = 0 OR is_pwd IS NULL",
    "UPDATE approve_client SET pwd_approval_status = 'not_applicable' WHERE is_pwd = 0 OR is_pwd IS NULL"
]

def run_migration():
    """Run the PWD database migration"""
    try:
        print("Connecting to MySQL database...")
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()

        print("Running PWD migration...")

        for i, sql in enumerate(sql_commands, 1):
            try:
                print(f"Executing command {i}/{len(sql_commands)}...")
                cursor.execute(sql)
                conn.commit()
                print(f"✓ Command {i} executed successfully")
            except mysql.connector.Error as err:
                if "Duplicate column name" in str(err):
                    print(f"⚠ Command {i} skipped - column already exists")
                elif "Duplicate key name" in str(err):
                    print(f"⚠ Command {i} skipped - index already exists")
                else:
                    print(f"✗ Error in command {i}: {err}")
                    # Continue with other commands

        # Show updated table structures
        print("\nShowing updated table structures...")
        tables = ['register_genius', 'approve_genius', 'register_client', 'approve_client']

        for table in tables:
            print(f"\n{table} table structure:")
            cursor.execute(f"DESCRIBE {table}")
            columns = cursor.fetchall()
            for column in columns:
                if any(pwd_field in column[0] for pwd_field in ['is_pwd', 'pwd_condition', 'pwd_proof', 'commission_rate']):
                    print(f"  ✓ {column[0]} - {column[1]} - {column[5] if column[5] else 'No comment'}")

        cursor.close()
        conn.close()

        print("\n✅ PWD migration completed successfully!")
        print("\nPWD System Features Added:")
        print("- PWD checkbox in registration forms")
        print("- PWD condition description field")
        print("- PWD proof document upload")
        print("- Commission rate system (10% regular, 5% PWD)")
        print("- Database indexes for performance")

    except mysql.connector.Error as err:
        print(f"Database Error: {err}")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    run_migration()
