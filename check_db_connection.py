import mysql.connector
import sys

# Database configuration with exact capitalization
db_config = {
    'host': 'localhost',
    'user': 'GigGenius',
    'password': 'Happiness1524!',
    'database': 'GigGenius'
}

def test_connection():
    try:
        print("Attempting to connect to MySQL database...")
        conn = mysql.connector.connect(**db_config)
        
        if conn.is_connected():
            print("✅ SUCCESS! Connected to MySQL database")
            db_info = conn.get_server_info()
            print(f"MySQL Server version: {db_info}")
            
            cursor = conn.cursor()
            cursor.execute("SELECT DATABASE();")
            db_name = cursor.fetchone()[0]
            print(f"Connected to database: {db_name}")
            
            # Check for tables
            cursor.execute("SHOW TABLES;")
            tables = cursor.fetchall()
            
            if tables:
                print("\nTables in the database:")
                for table in tables:
                    print(f"- {table[0]}")
            else:
                print("\nNo tables found in the database yet.")
                print("This is normal if you just created the database.")
                
            cursor.close()
            conn.close()
            
            print("\nYour database connection is working correctly!")
            print("The app.py file is already configured with these credentials.")
            return True
            
    except mysql.connector.Error as e:
        print(f"❌ Error connecting to MySQL database: {e}")
        
        # Provide troubleshooting tips
        print("\nTroubleshooting tips:")
        print("1. Make sure MySQL server is running")
        print("2. Verify that you've executed these SQL commands in phpMyAdmin:")
        print("   CREATE USER 'GigGenius'@'localhost' IDENTIFIED BY 'Happiness1524!';")
        print("   CREATE DATABASE GigGenius;")
        print("   GRANT ALL PRIVILEGES ON GigGenius.* TO 'GigGenius'@'localhost' IDENTIFIED BY 'Happiness1524!';")
        print("3. Check for any typos in the username, password, or database name")
        print("4. Make sure you're using the exact capitalization (MySQL can be case-sensitive)")
        
        return False

if __name__ == "__main__":
    success = test_connection()
    sys.exit(0 if success else 1)
