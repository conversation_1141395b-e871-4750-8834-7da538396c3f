import os
import subprocess
import sys
from pathlib import Path

def check_common_paths():
    print("=== Checking for MySQL installations ===")
    
    # Common installation paths
    common_paths = [
        "C:\\Program Files\\MySQL",
        "C:\\Program Files (x86)\\MySQL",
        "C:\\xampp\\mysql",
        "C:\\wamp\\bin\\mysql",
        "C:\\wamp64\\bin\\mysql",
        "C:\\laragon\\bin\\mysql",
        "C:\\ProgramData\\MySQL",
        "C:\\MySQL"
    ]
    
    found_installations = []
    
    for path in common_paths:
        if os.path.exists(path):
            print(f"✅ Found potential MySQL installation at: {path}")
            found_installations.append(path)
            
            # Look for bin directory
            bin_dir = os.path.join(path, "bin")
            if os.path.exists(bin_dir):
                print(f"  - Found bin directory: {bin_dir}")
                
                # Check for mysql client
                mysql_client = os.path.join(bin_dir, "mysql.exe")
                if os.path.exists(mysql_client):
                    print(f"  - Found MySQL client: {mysql_client}")
            
            # Look for my.ini or my.cnf
            config_files = [
                os.path.join(path, "my.ini"),
                os.path.join(path, "my.cnf"),
                os.path.join(path, "data", "my.ini"),
                os.path.join(path, "data", "my.cnf")
            ]
            
            for config_file in config_files:
                if os.path.exists(config_file):
                    print(f"  - Found configuration file: {config_file}")
    
    if not found_installations:
        print("❌ No MySQL installations found in common paths")
    
    # Check for XAMPP, WAMP, or similar packages
    packages = [
        ("XAMPP", "C:\\xampp"),
        ("WAMP", "C:\\wamp"),
        ("WAMP64", "C:\\wamp64"),
        ("Laragon", "C:\\laragon")
    ]
    
    for name, path in packages:
        if os.path.exists(path):
            print(f"\n✅ Found {name} installation at: {path}")
            
            # Check for control panel
            control_panels = [
                os.path.join(path, f"{name.lower()}-control.exe"),
                os.path.join(path, "xampp-control.exe"),
                os.path.join(path, "wampmanager.exe"),
                os.path.join(path, "laragon.exe")
            ]
            
            for panel in control_panels:
                if os.path.exists(panel):
                    print(f"  - Found control panel: {panel}")
                    print(f"  - You can use this to start/stop MySQL")
    
    # Check for MySQL in PATH
    try:
        print("\nChecking if MySQL is in PATH...")
        result = subprocess.run(["where", "mysql"], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ MySQL found in PATH:")
            for line in result.stdout.strip().split('\n'):
                print(f"  - {line}")
        else:
            print("❌ MySQL not found in PATH")
    except Exception as e:
        print(f"Error checking PATH: {e}")
    
    # Check for phpMyAdmin
    phpmyadmin_paths = [
        "C:\\xampp\\phpMyAdmin",
        "C:\\wamp\\apps\\phpmyadmin",
        "C:\\wamp64\\apps\\phpmyadmin",
        "C:\\laragon\\etc\\apps\\phpMyAdmin"
    ]
    
    for path in phpmyadmin_paths:
        if os.path.exists(path):
            print(f"\n✅ Found phpMyAdmin at: {path}")
            print("  - This suggests you're using a web server package with MySQL")
    
    print("\n=== Suggestions ===")
    print("1. If you found XAMPP, WAMP, or similar package:")
    print("   - Use the control panel to make sure MySQL is running")
    print("   - Check the MySQL logs for errors")
    print("   - Use phpMyAdmin to manage your database")
    print("2. If you found a standalone MySQL installation:")
    print("   - Check if the service is running")
    print("   - Look at the configuration file (my.ini or my.cnf)")
    print("   - Try connecting with the MySQL client")
    print("3. If no installation was found:")
    print("   - You might be using a non-standard installation")
    print("   - Check if MySQL is running in a Docker container")
    print("   - Consider installing MySQL or using a package like XAMPP")

if __name__ == "__main__":
    check_common_paths()
