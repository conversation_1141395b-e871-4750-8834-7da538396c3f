import mysql.connector
import sys
import requests
import socket

# Configuration for remote database
remote_config = {
    'host': 'gig-genius.io',  # The hostname from your phpMyAdmin URL
    'user': 'root',           # Default username, change if different
    'password': 'Happiness1524!',  # Password from your code
    'database': 'giggenius'   # Database name from your code
}

# Alternative configuration with different port
remote_config_alt = {
    'host': 'gig-genius.io',
    'user': 'root',
    'password': 'Happiness1524!',
    'database': 'giggenius',
    'port': 3306  # Default MySQL port
}

def check_site_availability(url):
    """Check if the website is available"""
    try:
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            return True, f"Website is available. Status code: {response.status_code}"
        else:
            return False, f"Website returned status code: {response.status_code}"
    except requests.RequestException as e:
        return False, f"Error connecting to website: {e}"

def check_mysql_port(host, port=3306):
    """Check if MySQL port is open on the host"""
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(5)
    result = sock.connect_ex((host, port))
    sock.close()
    if result == 0:
        return True, f"Port {port} is open on {host}"
    else:
        return False, f"Port {port} is closed on {host}"

def test_connection(config, name):
    """Test database connection with given configuration"""
    print(f"\nTesting connection with {name} configuration...")
    print(f"Configuration: {config}")
    
    try:
        connection = mysql.connector.connect(**config)
        
        if connection.is_connected():
            db_info = connection.get_server_info()
            cursor = connection.cursor()
            
            # Check if we're connected to a specific database
            if 'database' in config:
                cursor.execute("SELECT DATABASE();")
                db_name = cursor.fetchone()[0]
                print(f"✅ Connected to MySQL Server version {db_info}")
                print(f"✅ Connected to database: {db_name}")
                
                # Test a simple query to verify full functionality
                try:
                    cursor.execute("SHOW TABLES;")
                    tables = cursor.fetchall()
                    print(f"✅ Database contains {len(tables)} tables")
                    if tables:
                        print("Tables in database:")
                        for table in tables:
                            print(f"  - {table[0]}")
                except mysql.connector.Error as err:
                    print(f"❌ Query error: {err}")
            else:
                print(f"✅ Connected to MySQL Server version {db_info} (no specific database)")
                
                # List available databases
                try:
                    cursor.execute("SHOW DATABASES;")
                    databases = cursor.fetchall()
                    print(f"✅ Server has {len(databases)} databases")
                    print("Available databases:")
                    for db in databases:
                        print(f"  - {db[0]}")
                        if db[0].lower() == 'giggenius':
                            print(f"    ✅ Found 'giggenius' database!")
                except mysql.connector.Error as err:
                    print(f"❌ Query error: {err}")
            
            cursor.close()
            connection.close()
            print("✅ Connection closed successfully")
            return True
            
    except mysql.connector.Error as err:
        print(f"❌ Error: {err}")
        if hasattr(mysql.connector, 'errorcode'):
            if err.errno == mysql.connector.errorcode.ER_ACCESS_DENIED_ERROR:
                print("   This may be due to incorrect username or password")
            elif err.errno == mysql.connector.errorcode.ER_BAD_DB_ERROR:
                print("   Database does not exist")
            else:
                print(f"   Error code: {err.errno}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

# Main execution
print("=== Remote MySQL Database Connection Verification ===")

# First check if the website is available
print("\nChecking if gig-genius.io is available...")
site_available, site_message = check_site_availability("https://gig-genius.io")
print(site_message)

# Check if MySQL port is open
print("\nChecking if MySQL port is open on gig-genius.io...")
port_open, port_message = check_mysql_port("gig-genius.io")
print(port_message)

if not site_available:
    print("\n⚠️ The website gig-genius.io is not accessible. This might affect database connectivity.")
    user_input = input("Do you want to continue testing database connections anyway? (y/n): ")
    if user_input.lower() != 'y':
        sys.exit(1)

# Try connecting to the remote database
print("\nAttempting to connect to the remote database...")
remote_success = test_connection(remote_config, "Remote database")

if not remote_success:
    print("\nTrying alternative configuration with explicit port...")
    remote_alt_success = test_connection(remote_config_alt, "Remote database (with port)")
else:
    remote_alt_success = False

# Summary
print("\n=== Summary ===")
if remote_success or remote_alt_success:
    print("✅ Successfully connected to the remote database!")
    
    # Provide instructions for updating app.py
    print("\nTo update your Flask application to use this remote database:")
    print("1. Open app.py")
    print("2. Update the db_config as follows:")
    
    if remote_success:
        print("\ndb_config = {")
        print("    'host': 'gig-genius.io',")
        print("    'user': 'root',")
        print("    'password': 'Happiness1524!',")
        print("    'database': 'giggenius'")
        print("}")
    else:
        print("\ndb_config = {")
        print("    'host': 'gig-genius.io',")
        print("    'user': 'root',")
        print("    'password': 'Happiness1524!',")
        print("    'database': 'giggenius',")
        print("    'port': 3306")
        print("}")
else:
    print("❌ Failed to connect to the remote database.")
    print("\nPossible issues and solutions:")
    print("1. The MySQL server might not be accessible from your current location")
    print("2. The credentials might be incorrect")
    print("3. The database might not exist")
    print("4. A firewall might be blocking the connection")
    
    print("\nTo connect to phpMyAdmin directly:")
    print("1. Open https://gig-genius.io/phpmyadmin/ in your browser")
    print("2. Log in with the username and password you have")
    print("3. Once logged in, you can manage your database through the web interface")
    print("4. You can also check the correct credentials in the phpMyAdmin interface")
