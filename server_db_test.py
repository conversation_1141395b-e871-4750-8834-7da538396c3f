#!/usr/bin/env python3
"""
Server Database Connection Test Script

This script tests the database connection on the server.
Upload this to your server and run it to verify the database connection.
"""

import mysql.connector
import os
import sys

# Try to load environment variables if python-dotenv is installed
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Loaded environment variables from .env file")
except ImportError:
    print("⚠️ python-dotenv not installed, using default environment variables")

# Get database configuration from environment variables or use defaults
db_config = {
    'host': os.environ.get('DB_HOST', 'localhost'),
    'user': os.environ.get('DB_USER', 'root'),
    'password': os.environ.get('DB_PASSWORD', 'Happiness1524!'),
    'database': os.environ.get('DB_NAME', 'giggenius')
}

def test_connection():
    """Test database connection with environment configuration"""
    print("\nTesting connection with server configuration...")
    print(f"Configuration: {db_config}")
    
    try:
        connection = mysql.connector.connect(**db_config)
        
        if connection.is_connected():
            db_info = connection.get_server_info()
            cursor = connection.cursor()
            cursor.execute("SELECT DATABASE();")
            db_name = cursor.fetchone()[0]
            
            print(f"✅ Connected to MySQL Server version {db_info}")
            print(f"✅ Connected to database: {db_name}")
            
            # Test a simple query to verify full functionality
            try:
                cursor.execute("SHOW TABLES;")
                tables = cursor.fetchall()
                print(f"✅ Database contains {len(tables)} tables")
                if tables:
                    print("Tables in database:")
                    for table in tables:
                        print(f"  - {table[0]}")
            except mysql.connector.Error as err:
                print(f"❌ Query error: {err}")
            
            cursor.close()
            connection.close()
            print("✅ Connection closed successfully")
            return True
            
    except mysql.connector.Error as err:
        print(f"❌ Error: {err}")
        if hasattr(mysql.connector, 'errorcode'):
            if err.errno == mysql.connector.errorcode.ER_ACCESS_DENIED_ERROR:
                print("   This may be due to incorrect username or password")
            elif err.errno == mysql.connector.errorcode.ER_BAD_DB_ERROR:
                print("   Database does not exist")
            else:
                print(f"   Error code: {err.errno}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

# Print environment information
print("=== Server Database Connection Test ===")
print("\nEnvironment Variables:")
for key in ['DB_HOST', 'DB_USER', 'DB_NAME']:
    value = os.environ.get(key)
    if value:
        print(f"  {key}: {value}")
    else:
        print(f"  {key}: Not set (will use default)")

print(f"  DB_PASSWORD: {'Set' if os.environ.get('DB_PASSWORD') else 'Not set (will use default)'}")

# Test the connection
if test_connection():
    print("\n✅ Database connection successful!")
    print("Your Flask application should be able to connect to the database.")
else:
    print("\n❌ Database connection failed.")
    print("Please check your database configuration and credentials.")
    sys.exit(1)
