# XAMPP MySQL Setup Guide for GigGenius

This guide will help you set up your MySQL database for the GigGenius application using XAMPP.

## 1. Start XAMPP Services

1. Open XAMPP Control Panel (C:\\xampp\\xampp-control.exe)
2. Start the Apache and MySQL services by clicking the "Start" buttons next to them
3. Make sure both services show a green status

## 2. Access phpMyAdmin

1. Open your web browser and go to: http://localhost/phpmyadmin/
2. You should be automatically logged in as the root user
   - If prompted for credentials, use:
     - Username: root
     - Password: (leave empty)

## 3. Create the GigGenius Database

1. In phpMyAdmin, click on "New" in the left sidebar
2. Enter "giggenius" (all lowercase) as the database name
3. Select "utf8mb4_general_ci" as the collation
4. Click "Create"

## 4. Create Required Tables

### Genius Table

1. Select the "giggenius" database from the left sidebar
2. Click on the "SQL" tab
3. Copy and paste the following SQL:

```sql
CREATE TABLE IF NOT EXISTS genius (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VA<PERSON>HAR(50) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    profile_photo VARCHAR(255),
    expertise_level VARCHAR(50),
    country VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

4. Click "Go" to execute the query

### Clients Table

1. Still in the "SQL" tab, copy and paste the following SQL:

```sql
CREATE TABLE IF NOT EXISTS clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    work_email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    profile_photo VARCHAR(255),
    position VARCHAR(100),
    country VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

2. Click "Go" to execute the query

## 5. Update Your Application Configuration

Update your app.py file to use the XAMPP MySQL configuration:

```python
# Database configuration
db_config = {
    'host': 'localhost',
    'user': 'root',  # XAMPP default user
    'password': '',  # XAMPP default password (empty)
    'database': 'giggenius'  # The database we created
}
```

## 6. Test the Connection

Run the test_db_connection.py script to verify that the connection works:

```
python test_db_connection.py
```

You should see a successful connection message.

## 7. Run Your Application

Start your Flask application:

```
python app.py
```

## Troubleshooting

### Connection Issues

If you're having trouble connecting to the database:

1. Make sure XAMPP's MySQL service is running
2. Check that you're using the correct credentials (root with empty password)
3. Verify that the giggenius database exists
4. Check MySQL logs at: C:\\xampp\\mysql\\data\\mysql_error.log

### Database Not Found

If you get a "Unknown database 'giggenius'" error:

1. Open phpMyAdmin
2. Check if the giggenius database exists in the left sidebar
3. If not, create it as described in step 3

### Tables Not Found

If your application can't find the tables:

1. Open phpMyAdmin
2. Select the giggenius database
3. Check if the tables exist in the left sidebar
4. If not, create them as described in step 4

### Authentication Issues

If you get "Access denied" errors:

1. Make sure you're using the default XAMPP credentials (root with empty password)
2. If you've set a password for the root user, use that instead
3. You can reset the root password by following XAMPP documentation
