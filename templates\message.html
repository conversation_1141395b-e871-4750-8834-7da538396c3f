<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="user-id" content="{{ session.get('user_id', '') }}">
    <meta name="user-first-name" content="{{ session.get('first_name', '') }}">
    <meta name="user-last-name" content="{{ session.get('last_name', '') }}">
    <title>GigGenius Messages</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/message.css') }}" rel="stylesheet">

    <script src="https://cdn.socket.io/4.5.4/socket.io.min.js"></script>
    <script>
        // Add this script to configure Socket.IO for both local and production
        document.addEventListener('DOMContentLoaded', function() {
            // Get the current hostname
            const hostname = window.location.hostname;
            const protocol = window.location.protocol;
            
            // Set up Socket.IO connection with proper configuration
            window.socketConfig = {
                path: '/socket.io',
                transports: ['websocket', 'polling'],
                secure: protocol === 'https:',
                reconnection: true,
                reconnectionAttempts: 5,
                reconnectionDelay: 1000
            };
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/emoji-picker-element@1/index.js" type="module"></script>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-comments me-2"></i>
                GigGenius
            </a>
            <div class="navbar-nav ms-auto" id="navbarNav">
                <div class="profile-container">
                    <img src="{{ url_for('get_profile_photo', user_id=session.get('user_id', 0)) }}"
                         alt="{{ session.get('name', 'Profile') }}"
                         class="profile-avatar"
                         onerror="this.src=`https://ui-avatars.com/api/?name={{ session.get('first_name', ' ')[0] }}{{ session.get('last_name', ' ')[0] }}&background=2563eb&color=fff&size=128`"
                         onclick="toggleProfileDropdown(event)">
                    <div class="profile-dropdown" id="profileDropdown">
                        <!-- Header Section -->
                        <div class="dropdown-header">
                            <div class="user-profile-info">
                                <img src="{{ url_for('get_profile_photo', user_id=session.get('user_id', 0)) }}"
                                     alt="{{ session.get('name', 'Profile') }}"
                                     class="dropdown-avatar"
                                     onerror="this.src=`https://ui-avatars.com/api/?name={{ session.get('first_name', ' ')[0] }}{{ session.get('last_name', ' ')[0] }}&background=2563eb&color=fff&size=128`">
                                <div class="user-details">
                                    <h4>{{ session.get('first_name', '') }} {{ session.get('last_name', '') }}</h4>
                                    <span class="user-email">{{ session.get('email', '') }}</span>
                                    <span class="user-role">{{ session.get('user_type', '').title() }} Account</span>
                                </div>
                            </div>
                        </div>

                        <!-- Account Section -->
                        <div class="dropdown-section">
                            <div class="section-title">Account</div>
                            <a href="{{ url_for('profile') }}" class="menu-item">
                                <i class="fas fa-user"></i>
                                <span>Profile Settings</span>
                            </a>
                            <a href="#" class="menu-item">
                                <i class="fas fa-wallet"></i>
                                <span>Billing & Payments</span>
                            </a>
                        </div>

                        <!-- Preferences Section -->
                        <div class="dropdown-section">
                            <div class="section-title">Preferences</div>
                            <a href="#" class="menu-item">
                                <i class="fas fa-bell"></i>
                                <span>Notifications</span>
                                <span class="badge">2</span>
                            </a>
                            <a href="#" class="menu-item">
                                <i class="fas fa-shield-alt"></i>
                                <span>Privacy & Security</span>
                            </a>
                            <a href="#" class="menu-item">
                                <i class="fas fa-moon"></i>
                                <span>Dark Mode</span>
                                <label class="switch">
                                    <input type="checkbox" id="darkModeToggle">
                                    <span class="slider round"></span>
                                </label>
                            </a>
                        </div>

                        <!-- Help Section -->
                        <div class="dropdown-section">
                            <div class="section-title">Support</div>
                            <a href="#" class="menu-item">
                                <i class="fas fa-question-circle"></i>
                                <span>Help Center</span>
                            </a>
                            <a href="#" class="menu-item">
                                <i class="fas fa-comment-alt"></i>
                                <span>Contact Support</span>
                            </a>
                        </div>

                        <div class="divider"></div>

                        <!-- Logout Section -->
                        <div class="dropdown-section">
                            <a href="{{ url_for('logout') }}" class="menu-item logout">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>Sign Out</span>
                            </a>
                        </div>

                        <!-- Footer Section -->
                        <div class="dropdown-footer">
                            <span class="version">GigGenius v1.0.0</span>
                            <a href="#" class="terms">Terms</a>
                            <span class="dot">•</span>
                            <a href="#" class="privacy">Privacy</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="chat-container">
        <div class="contacts-list">
            <div class="contacts-header">
                <h2>Messages</h2>
                <div class="search-box">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="form-control" placeholder="Search contacts..." id="contactSearch" oninput="filterContacts(this.value)">
                </div>
            </div>
            <div class="contacts-body">
                {% if contacts %}
                    {% for contact in contacts %}
                    <div class="contact-item"
                         data-user-id="{{ contact.id }}"
                         data-user-name="{{ contact.name }}"
                         data-last-message="{{ contact.last_message }}"
                         data-last-message-sender-id="{{ contact.last_message_sender_id }}"
                         data-last-activity-time="{{ contact.last_activity_time }}"
                         data-last-message-type="{{ contact.last_message_type }}"
                         data-last-file-name="{{ contact.last_file_name }}"
                         onclick="switchContact(this)">
                        <div class="contact-avatar-wrapper">
                            <img src="{{ contact.profile_photo_url }}" class="contact-avatar" alt="{{ contact.first_name }}" data-default-avatar="{{ url_for('static', filename='img/default-avatar.png') }}" onerror="this.src=this.getAttribute('data-default-avatar')">
                        </div>
                        <div class="contact-info">
                            <div class="contact-name">{{ contact.first_name }} {{ contact.last_name }}</div>
                            <div class="contact-preview">
                                <div class="preview-text">
                                    {% if 'reacted' in contact.last_message and 'to your message' in contact.last_message %}
                                        {{ contact.last_message }}
                                    {% elif contact.last_message_type == 'image' %}
                                        {% if contact.last_message_sender_id == current_user_id %}
                                            You: Sent an image{% if contact.last_file_name %}: {{ contact.last_file_name }}{% endif %}
                                        {% else %}
                                            {{ contact.first_name }}: Sent an image{% if contact.last_file_name %}: {{ contact.last_file_name }}{% endif %}
                                        {% endif %}
                                    {% elif contact.last_message_type and contact.last_message_type != 'text' %}
                                        {% if contact.last_message_sender_id == current_user_id %}
                                            You: Sent a file{% if contact.last_file_name %}: {{ contact.last_file_name }}{% endif %}
                                        {% else %}
                                            {{ contact.first_name }}: Sent a file{% if contact.last_file_name %}: {{ contact.last_file_name }}{% endif %}
                                        {% endif %}
                                    {% elif contact.last_message %}
                                        {% if contact.last_message_sender_id == current_user_id %}
                                            You: {{ contact.last_message }}
                                        {% else %}
                                            {{ contact.first_name }}: {{ contact.last_message }}
                                        {% endif %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="no-contacts">
                        <p>No conversations yet</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <div class="chat-area">
            <div class="chat-header">
                <img src="" id="currentChatAvatar" class="chat-header-avatar" alt="Contact Avatar">
                <div class="chat-header-info">
                    <h3 id="currentChatName"></h3>
                    <span id="currentChatStatus"></span>
                </div>
                <div class="chat-header-actions">
                    <button onclick="toggleProfilePanel()" title="View Profile">
                        <i class="fas fa-user"></i>
                    </button>
                    <button title="Video Call">
                        <i class="fas fa-video"></i>
                    </button>
                    <button title="More Options">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>

            <div class="chat-messages">
                <!-- Messages will be dynamically added here -->
            </div>




            <div id="typing" class="typing-indicator" style="display: none; padding: 5px 15px;">
                <small class="text-muted"><i></i></small>
            </div>

            <div class="message-input">
                <div class="files-display-area" style="display: none;">
                    <div class="files-header">
                        <span>Selected Files</span>
                        <button class="clear-all-files" onclick="clearAllFiles()">
                            Clear All
                        </button>
                    </div>
                    <div class="selected-files-list"></div>
                </div>
                <div class="chat-input-container">
                    <div class="chat-input-wrapper">
                        <div class="chat-input-buttons">
                            <div class="input-button file-upload-button">
                                <input type="file"
                                       id="file-upload"
                                       style="display: none;"
                                       onchange="showSelectedFile(this)"
                                       accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.xls,.xlsx,.txt">
                                <label for="file-upload">
                                    <i class="fas fa-paperclip"></i>
                                </label>
                            </div>
                            <div class="input-button emoji-picker-container">
                                <div class="emoji-trigger"><i class="far fa-smile"></i></div>
                                <emoji-picker class="light"></emoji-picker>
                            </div>
                        </div>
                        <div class="chat-input-area">
                            <textarea class="chat-input"
                                placeholder="Type a message..."
                                onkeydown="handleKeyPress(event)"
                                oninput="autoResize(this)"></textarea>
                            <div class="chat-input-actions">
                                <div class="action-button">Press Enter to send</div>
                                <div class="action-button">Shift + Enter for new line</div>
                            </div>
                        </div>
                        <div class="input-button send-button" onclick="sendMessage(document.querySelector('.chat-input').value, document.getElementById('file-upload'))">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- Profile Panel Overlay -->
    <div class="panel-overlay" id="panelOverlay" onclick="toggleProfilePanel()"></div>

    <!-- Profile Panel -->
    <div class="profile-panel" id="profilePanel">
        <div class="profile-panel-header">
            <h5 class="mb-0">Profile Information</h5>
            <button class="profile-panel-close" onclick="toggleProfilePanel()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="profile-panel-content">
            <div class="profile-info-section">
                <div class="profile-photo-wrapper">
                    <img src="" id="profilePanelAvatar" class="profile-panel-avatar" alt="Profile Photo">
                </div>
                <h2 class="profile-name-large" id="profilePanelName"></h2>
                <div class="profile-status-large">
                    <i class="fas fa-circle text-success" style="font-size: 0.5rem;"></i>
                    <span>Online</span>
                </div>
            </div>
            <div class="profile-details">
                <div class="profile-menu">
                    <!-- View Profile menu item -->
                    <div class="profile-menu-item" onclick="viewFullProfile()">
                        <div class="menu-icon">
                            <div class="icon-wrapper">
                                <i class="fas fa-user"></i>
                            </div>
                        </div>
                        <div class="menu-content">
                            <div class="menu-text">View Profile</div>
                            <div class="menu-subtext">See full profile details</div>
                        </div>
                        <div class="menu-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>

                    <!-- Files menu item -->
                    <div class="profile-menu-item" onclick="viewFiles()">
                        <div class="menu-icon">
                            <div class="icon-wrapper">
                                <i class="fas fa-file"></i>
                            </div>
                        </div>
                        <div class="menu-content">
                            <div class="menu-text">Files</div>
                            <div class="menu-subtext">View shared files</div>
                        </div>
                        <div class="menu-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>

                    <!-- Search Conversation menu item -->
                    <div class="profile-menu-item" onclick="searchConversation()">
                        <div class="menu-icon">
                            <div class="icon-wrapper">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>
                        <div class="menu-content">
                            <div class="menu-text">Search Conversation</div>
                            <div class="menu-subtext">Find messages and media</div>
                        </div>
                        <div class="menu-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Full Profile Panel -->
    <div class="full-profile-panel" id="fullProfilePanel">
        <div class="profile-panel-header">
            <button class="back-button" onclick="closeFullProfile()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h5 class="mb-0">Profile Information</h5>
            <button class="profile-panel-close" onclick="closeFullProfile()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="full-profile-content">
            <div class="profile-info-section">
                <div class="profile-photo-wrapper">
                    <img src="" id="fullProfileAvatar" class="profile-panel-avatar" alt="Profile Photo">
                </div>
                <h2 class="profile-name-large" id="fullProfileName"></h2>
                <div class="profile-status-large">
                    <i class="fas fa-circle text-success" style="font-size: 0.5rem;"></i>
                    <span>Online</span>
                </div>
            </div>
            <div class="profile-info-details">
                <div class="info-group">
                    <h3>Contact Information</h3>
                    <div class="info-item">
                        <span class="info-label">Email</span>
                        <span class="info-value" id="fullProfileEmail"></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Mobile</span>
                        <span class="info-value" id="fullProfileMobile"></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Country</span>
                        <span class="info-value" id="fullProfileCountry"></span>
                    </div>
                </div>





    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.socket.io/4.5.4/socket.io.min.js"></script>
    <script src="{{ url_for('static', filename='js/messages.js') }}"></script>

    <!-- Unsend Message Modal -->
    <div class="modal fade" id="unsendModal" tabindex="-1" aria-labelledby="unsendModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="unsendModalLabel">Unsend Message</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to unsend this message? This action cannot be undone.</p>
                    <p class="text-muted small">Note: The recipient may have already seen this message.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" onclick="confirmUnsend()">Unsend</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Viewer Modal -->
    <div class="modal fade" id="imageViewerModal" tabindex="-1" aria-labelledby="imageViewerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageViewerModalLabel">Image Preview</h5>
                    <div class="image-controls">
                        <button type="button" class="btn btn-sm btn-outline-secondary me-2" id="zoomInBtn">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary me-2" id="zoomOutBtn">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary me-2" id="downloadImageBtn">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-0">
                    <div class="image-container">
                        <img src="" id="modalImage" class="img-fluid" alt="Image Preview">
                    </div>
                </div>
                <div class="modal-footer">
                    <span id="imageFileName" class="me-auto text-muted"></span>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <style>
        #imageViewerModal .modal-dialog {
            max-width: 90%;
            margin: 1.75rem auto;
        }

        #imageViewerModal .modal-content {
            background-color: #1a1a1a;
            color: #fff;
        }

        #imageViewerModal .modal-header {
            border-bottom: 1px solid #333;
        }

        #imageViewerModal .modal-footer {
            border-top: 1px solid #333;
        }

        #imageViewerModal .image-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 300px;
            max-height: 80vh;
            overflow: auto;
            background-color: #0a0a0a;
        }

        #modalImage {
            max-width: 100%;
            max-height: 80vh;
            object-fit: contain;
            transition: transform 0.3s ease;
        }

        .image-controls {
            display: flex;
            align-items: center;
        }

        @media (max-width: 768px) {
            #imageViewerModal .modal-dialog {
                max-width: 100%;
                margin: 0;
                height: 100%;
            }

            #imageViewerModal .modal-content {
                height: 100vh;
                border-radius: 0;
            }

            #imageViewerModal .image-container {
                height: calc(100vh - 120px);
                max-height: none;
            }
        }
    </style>
</body>
</html>










