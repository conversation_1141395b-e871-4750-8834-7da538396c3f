[1mdiff --git a/templates/client_registration.html b/templates/client_registration.html[m
[1mindex 85d90e7..f82a8f1 100644[m
[1m--- a/templates/client_registration.html[m
[1m+++ b/templates/client_registration.html[m
[36m@@ -759,7 +759,7 @@[m
             margin: 0;[m
         }[m
         #validation-errors li, #validation-errors-step2 li{[m
[31m-            color: red; [m
[32m+[m[32m            color: red;[m
             display: flex;[m
             justify-content: left;[m
             align-items: center;[m
[36m@@ -1135,10 +1135,10 @@[m
                     <!-- Add Introduction field here -->[m
                     <div class="form-group full-width">[m
                         <label for="introduction">Introduction <span class="required">*</span></label>[m
[31m-                        <textarea [m
[31m-                            id="introduction" [m
[31m-                            name="introduction" [m
[31m-                            maxlength="300" [m
[32m+[m[32m                        <textarea[m
[32m+[m[32m                            id="introduction"[m
[32m+[m[32m                            name="introduction"[m
[32m+[m[32m                            maxlength="300"[m
                             required[m
                             placeholder="Brief introduction about yourself and your expertise"[m
                         ></textarea>[m
[36m@@ -1148,9 +1148,9 @@[m
                     <div class="form-group">[m
                         <label>Business Registration <span class="required">*</span></label>[m
                         <div class="id-upload-box" onclick="document.getElementById('businessReg').click()">[m
[31m-                            <input type="file" [m
[31m-                                id="businessReg" [m
[31m-                                name="businessReg" [m
[32m+[m[32m                            <input type="file"[m
[32m+[m[32m                                id="businessReg"[m
[32m+[m[32m                                name="businessReg"[m
                                 accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"[m
                                 required[m
                                 hidden>[m
[36m@@ -1168,7 +1168,7 @@[m
                             I want to receive email updates about <span style="color: var(--primary-pink);">GigGenius</span> services and latest opportunities.[m
                         </label>[m
                     </div>[m
[31m-                    [m
[32m+[m
                     <div class="checkbox-group full-width">[m
                         <input type="checkbox" id="termsAgreement" name="terms_agreement" required>[m
                         <label for="termsAgreement">[m
[36m@@ -1179,7 +1179,7 @@[m
 [m
                 <!-- Error messages container for step 2 -->[m
                 <div id="validation-errors-step2" class="error-container" style="display: none;"></div>[m
[31m-                [m
[32m+[m
                 <div class="button-container">[m
                     <button type="button" onclick="previousStep()" class="btn btn-outline">Previous</button>[m
                     <button type="button" onclick="validateStep2()" class="btn btn-primary">Submit</button>[m
[36m@@ -1238,7 +1238,7 @@[m
             introTextarea.addEventListener('input', function() {[m
                 const remaining = this.value.length;[m
                 introCounter.textContent = `${remaining}/300`;[m
[31m-                [m
[32m+[m
                 if (remaining >= 300) {[m
                     introCounter.style.color = 'red';[m
                 } else {[m
[36m@@ -1272,11 +1272,11 @@[m
             document.querySelectorAll('input').forEach(input => {[m
                 input.addEventListener('input', clearInputError);[m
             });[m
[31m-            [m
[32m+[m
             document.querySelectorAll('select').forEach(select => {[m
                 select.addEventListener('change', clearInputError);[m
             });[m
[31m-            [m
[32m+[m
             document.getElementById('termsAgreement').addEventListener('change', clearInputError);[m
         }[m
 [m
[36m@@ -1289,7 +1289,7 @@[m
             const countrySelect = document.getElementById('country');[m
             const countryCodeSpan = document.getElementById('selectedCountryCode');[m
             countryCodeSpan.textContent = countryCodes['Philippines'];[m
[31m-            [m
[32m+[m
             countrySelect.addEventListener('change', function() {[m
                 updateCountryCode(this.value);[m
             });[m
[36m@@ -1301,7 +1301,7 @@[m
             document.getElementById('email').addEventListener('input', function() {[m
                 const email = this.value;[m
                 const errorElement = document.getElementById('email-error');[m
[31m-                [m
[32m+[m
                 // Show error message immediately if there's no @ symbol[m
                 if (!email.includes('@')) {[m
                     errorElement.style.display = 'block';[m
[36m@@ -1309,7 +1309,7 @@[m
                     this.classList.add('input-error');[m
                     return;[m
                 }[m
[31m-                [m
[32m+[m
                 // More detailed validation if @ is present[m
                 const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;[m
                 if (!emailRegex.test(email)) {[m
[36m@@ -1327,7 +1327,7 @@[m
             document.getElementById('password').addEventListener('input', function() {[m
                 const password = this.value;[m
                 const errorElement = document.getElementById('password-error');[m
[31m-                [m
[32m+[m
                 // If empty, hide error[m
                 if (password.trim() === '') {[m
                     errorElement.style.display = 'none';[m
[36m@@ -1345,14 +1345,14 @@[m
                 if (!minLength || !hasUpperCase || !hasLowerCase || !hasNumber || !hasSpecial) {[m
                     errorElement.style.display = 'block';[m
                     this.classList.add('input-error');[m
[31m-                    [m
[32m+[m
                     let errorMessage = 'Password must:';[m
                     if (!minLength) errorMessage += '<br>• Be at least 8 characters';[m
                     if (!hasUpperCase) errorMessage += '<br>• Include an uppercase letter (A-Z)';[m
                     if (!hasLowerCase) errorMessage += '<br>• Include a lowercase letter (a-z)';[m
                     if (!hasNumber) errorMessage += '<br>• Include a number (0-9)';[m
                     if (!hasSpecial) errorMessage += '<br>• Include a special character (!@#$%^&*)';[m
[31m-                    [m
[32m+[m
                     errorElement.innerHTML = errorMessage;[m
                 } else {[m
                     errorElement.style.display = 'none';[m
[36m@@ -1428,27 +1428,27 @@[m
         function handleBusinessRegUpload(e) {[m
             const file = e.target.files[0];[m
             if (!file) return;[m
[31m-            [m
[32m+[m
             const allowedTypes = [[m
[31m-                'application/pdf', 'application/msword', [m
[32m+[m[32m                'application/pdf', 'application/msword',[m
                 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',[m
                 'application/vnd.ms-excel',[m
                 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',[m
                 'image/jpeg', 'image/png', 'image/jpg'[m
             ];[m
[31m-            [m
[32m+[m
             if (!validateFileType(file, allowedTypes)) {[m
                 showError('businessReg-error', 'Please upload a valid document (PDF, Word, Excel, or Image file only)');[m
                 e.target.value = '';[m
                 return;[m
             }[m
[31m-            [m
[32m+[m
             if (!validateFileSize(file)) {[m
                 showError('businessReg-error', 'Business registration file must be less than 5MB');[m
    