import mysql.connector
import sys

# Database configuration with GigGenius user
db_config = {
    'host': 'localhost',
    'user': 'GigGenius',
    'password': 'Happiness1524!',
    'database': 'GigGenius'
}

def test_connection():
    try:
        print("Attempting to connect to MySQL database with GigGenius user...")
        conn = mysql.connector.connect(**db_config)
        
        if conn.is_connected():
            print("✅ SUCCESS! Connected to GigGenius database")
            db_info = conn.get_server_info()
            print(f"MySQL Server version: {db_info}")
            
            cursor = conn.cursor()
            cursor.execute("SELECT DATABASE();")
            db_name = cursor.fetchone()[0]
            print(f"Connected to database: {db_name}")
            
            # Check for tables
            cursor.execute("SHOW TABLES;")
            tables = cursor.fetchall()
            
            if tables:
                print("\nTables in the database:")
                for table in tables:
                    print(f"- {table[0]}")
            else:
                print("\nNo tables found in the database yet.")
                print("This is normal if you just created the database.")
                
            cursor.close()
            conn.close()
            
            print("\nYour database connection is working correctly!")
            print("The app.py file should be configured with these credentials.")
            return True
            
    except mysql.connector.Error as e:
        print(f"❌ Error connecting to MySQL database: {e}")
        
        # Provide troubleshooting tips
        if "Access denied" in str(e):
            print("\nIt looks like there's an authentication issue.")
            print("Let's try to fix the privileges for the GigGenius user.")
            print("\nPlease follow these steps in phpMyAdmin:")
            print("1. Click on the 'GigGenius' user in the list")
            print("2. Click on 'Edit privileges'")
            print("3. Click on 'Database-specific privileges'")
            print("4. Select 'GigGenius' from the dropdown and click 'Go'")
            print("5. On the next page, click 'Check all' to grant all privileges")
            print("6. Click 'Go' to save the changes")
        
        return False

if __name__ == "__main__":
    success = test_connection()
    sys.exit(0 if success else 1)
