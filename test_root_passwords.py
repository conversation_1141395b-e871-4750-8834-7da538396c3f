import mysql.connector
import sys

# List of possible passwords to try
passwords = [
    "",  # Empty password
    "root",  # Common default
    "password",  # Common default
    "admin",  # Common default
    "mysql",  # Common default
    "Happiness1524!",  # Your specified password
    "happiness1524!",  # Lowercase version
    "Happiness1524",  # Without exclamation mark
    "1234",  # Simple password
    "12345",  # Simple password
    "123456"  # Simple password
]

def test_passwords():
    for password in passwords:
        try:
            print(f"\nTrying password: '{password}'")
            conn = mysql.connector.connect(
                host="localhost",
                user="root",
                password=password
            )
            
            if conn.is_connected():
                print(f"✅ SUCCESS! Connected to MySQL with password: '{password}'")
                db_info = conn.get_server_info()
                print(f"MySQL Server version: {db_info}")
                
                # Check if giggenius database exists
                cursor = conn.cursor()
                cursor.execute("SHOW DATABASES LIKE 'giggenius';")
                result = cursor.fetchone()
                
                if result:
                    print("✅ 'giggenius' database exists.")
                else:
                    print("❌ 'giggenius' database does not exist.")
                    
                cursor.close()
                conn.close()
                
                print("\nUse these connection parameters in your app.py:")
                print("    'host': 'localhost',")
                print("    'user': 'root',")
                print(f"    'password': '{password}',")
                print("    'database': 'giggenius'")
                
                return True
                
        except mysql.connector.Error as e:
            print(f"❌ Failed: {e}")
    
    print("\n❌ All password attempts failed.")
    return False

if __name__ == "__main__":
    success = test_passwords()
    sys.exit(0 if success else 1)
