#!/usr/bin/env python3
"""
Fix PWD approval status enum to include 'not_applicable' option
This script updates the database schema to support existing users
"""

import mysql.connector
import sys
from datetime import datetime

# Database configuration
db_config = {
    'host': '**************',
    'user': 'giggenius_user',
    'password': 'Happiness1524!',
    'database': 'giggenius',
    'connect_timeout': 10,
    'use_pure': True,
    'autocommit': True
}

def check_current_enum():
    """Check current enum values for pwd_approval_status"""
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor(dictionary=True)
        
        tables = ['register_genius', 'approve_genius', 'register_client', 'approve_client']
        
        print("🔍 Checking current enum values...")
        print("=" * 50)
        
        for table in tables:
            try:
                query = f"""
                    SELECT COLUMN_TYPE 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = 'giggenius' 
                    AND TABLE_NAME = '{table}' 
                    AND COLUMN_NAME = 'pwd_approval_status'
                """
                cursor.execute(query)
                result = cursor.fetchone()
                
                if result:
                    print(f"📊 {table}: {result['COLUMN_TYPE']}")
                else:
                    print(f"⚠️  {table}: pwd_approval_status column not found")
                    
            except Exception as e:
                print(f"❌ Error checking {table}: {e}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking enum: {e}")

def fix_enum_values():
    """Fix enum values to include 'not_applicable'"""
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()
        
        print("\n🔧 Fixing enum values...")
        print("=" * 40)
        
        # SQL commands to update enum values
        enum_updates = [
            # Update register_genius
            """
            ALTER TABLE register_genius 
            MODIFY COLUMN pwd_approval_status 
            ENUM('pending', 'approved', 'rejected', 'not_applicable') 
            DEFAULT 'not_applicable'
            """,
            
            # Update approve_genius
            """
            ALTER TABLE approve_genius 
            MODIFY COLUMN pwd_approval_status 
            ENUM('pending', 'approved', 'rejected', 'not_applicable') 
            DEFAULT 'not_applicable'
            """,
            
            # Update register_client
            """
            ALTER TABLE register_client 
            MODIFY COLUMN pwd_approval_status 
            ENUM('pending', 'approved', 'rejected', 'not_applicable') 
            DEFAULT 'not_applicable'
            """,
            
            # Update approve_client
            """
            ALTER TABLE approve_client 
            MODIFY COLUMN pwd_approval_status 
            ENUM('pending', 'approved', 'rejected', 'not_applicable') 
            DEFAULT 'not_applicable'
            """
        ]
        
        tables = ['register_genius', 'approve_genius', 'register_client', 'approve_client']
        
        for i, (table, sql) in enumerate(zip(tables, enum_updates)):
            try:
                print(f"📝 Updating {table} enum...")
                cursor.execute(sql)
                print(f"✅ {table} enum updated successfully")
                
            except mysql.connector.Error as err:
                print(f"❌ Error updating {table}: {err}")
                continue
        
        cursor.close()
        conn.close()
        
        print("\n✅ Enum values updated successfully!")
        
    except Exception as e:
        print(f"❌ Error fixing enum: {e}")
        return False
    
    return True

def update_existing_users():
    """Update existing users to 'not_applicable' status"""
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()
        
        print("\n🔄 Updating existing users...")
        print("=" * 40)
        
        # Update commands for existing users
        update_commands = [
            # Update existing users in register_genius
            """
            UPDATE register_genius 
            SET pwd_approval_status = 'not_applicable',
                is_pwd = 0,
                commission_rate = 10.00
            WHERE (pwd_approval_status = 'approved' OR pwd_approval_status IS NULL)
            AND (is_pwd = 0 OR is_pwd IS NULL)
            """,
            
            # Update existing users in approve_genius
            """
            UPDATE approve_genius 
            SET pwd_approval_status = 'not_applicable',
                is_pwd = 0,
                commission_rate = 10.00
            WHERE (pwd_approval_status = 'approved' OR pwd_approval_status IS NULL)
            AND (is_pwd = 0 OR is_pwd IS NULL)
            """,
            
            # Update existing users in register_client
            """
            UPDATE register_client 
            SET pwd_approval_status = 'not_applicable',
                is_pwd = 0,
                commission_rate = 10.00
            WHERE (pwd_approval_status = 'approved' OR pwd_approval_status IS NULL)
            AND (is_pwd = 0 OR is_pwd IS NULL)
            """,
            
            # Update existing users in approve_client
            """
            UPDATE approve_client 
            SET pwd_approval_status = 'not_applicable',
                is_pwd = 0,
                commission_rate = 10.00
            WHERE (pwd_approval_status = 'approved' OR pwd_approval_status IS NULL)
            AND (is_pwd = 0 OR is_pwd IS NULL)
            """
        ]
        
        tables = ['register_genius', 'approve_genius', 'register_client', 'approve_client']
        
        for table, sql in zip(tables, update_commands):
            try:
                print(f"📝 Updating existing users in {table}...")
                cursor.execute(sql)
                updated_count = cursor.rowcount
                print(f"✅ Updated {updated_count} users in {table}")
                
            except mysql.connector.Error as err:
                print(f"❌ Error updating {table}: {err}")
                continue
        
        cursor.close()
        conn.close()
        
        print("\n✅ Existing users updated successfully!")
        
    except Exception as e:
        print(f"❌ Error updating users: {e}")
        return False
    
    return True

def verify_results():
    """Verify the migration results"""
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor(dictionary=True)
        
        print("\n📊 Verification Results:")
        print("=" * 40)
        
        tables = ['register_genius', 'approve_genius', 'register_client', 'approve_client']
        
        for table in tables:
            try:
                # Get status distribution
                query = f"""
                    SELECT 
                        pwd_approval_status,
                        COUNT(*) as count,
                        AVG(commission_rate) as avg_commission
                    FROM {table}
                    GROUP BY pwd_approval_status
                """
                cursor.execute(query)
                results = cursor.fetchall()
                
                print(f"\n📋 {table}:")
                for result in results:
                    status = result['pwd_approval_status'] or 'NULL'
                    count = result['count']
                    avg_commission = result['avg_commission'] or 0
                    print(f"   {status}: {count} users (avg commission: {avg_commission:.2f}%)")
                    
            except Exception as e:
                print(f"❌ Error verifying {table}: {e}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error in verification: {e}")

if __name__ == "__main__":
    print("🚀 PWD Enum Fix and Migration Tool")
    print("=" * 50)
    
    # Check current status
    check_current_enum()
    
    # Ask for confirmation
    response = input("\nDo you want to fix the enum and update existing users? (y/N): ")
    
    if response.lower() in ['y', 'yes']:
        print("\n🔧 Starting fix process...")
        
        # Step 1: Fix enum values
        if fix_enum_values():
            print("✅ Enum values fixed!")
            
            # Step 2: Update existing users
            if update_existing_users():
                print("✅ Existing users updated!")
                
                # Step 3: Verify results
                verify_results()
                
                print(f"\n🎉 Migration completed successfully!")
                print(f"📅 Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            else:
                print("❌ Failed to update existing users")
        else:
            print("❌ Failed to fix enum values")
    else:
        print("❌ Operation cancelled")
