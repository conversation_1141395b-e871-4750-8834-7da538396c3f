import mysql.connector
import sys

def test_mysql_connection():
    # First try to connect without specifying a database
    try:
        print("Attempting to connect to MySQL server without specifying database...")
        conn = mysql.connector.connect(
            host='localhost',
            user='root',
            password='Happiness1524!'
        )
        
        if conn.is_connected():
            print("✅ Successfully connected to MySQL server!")
            db_info = conn.get_server_info()
            print(f"MySQL Server version: {db_info}")
            
            # Check if giggenius database exists
            cursor = conn.cursor()
            cursor.execute("SHOW DATABASES LIKE 'giggenius';")
            result = cursor.fetchone()
            
            if result:
                print("✅ 'giggenius' database exists.")
                
                # Now try to connect to the giggenius database
                try:
                    cursor.execute("USE giggenius;")
                    print("✅ Successfully connected to 'giggenius' database.")
                    
                    # Check tables
                    cursor.execute("SHOW TABLES;")
                    tables = cursor.fetchall()
                    if tables:
                        print("\nTables in the database:")
                        for table in tables:
                            print(f"- {table[0]}")
                    else:
                        print("\nNo tables found in the 'giggenius' database.")
                        
                except mysql.connector.Error as e:
                    print(f"❌ Error accessing 'giggenius' database: {e}")
            else:
                print("❌ 'giggenius' database does not exist.")
                
            cursor.close()
            conn.close()
            return True
            
    except mysql.connector.Error as e:
        print(f"❌ Error connecting to MySQL server: {e}")
        
        # If access denied, try with empty password
        if "Access denied" in str(e):
            print("\nAttempting connection with empty password...")
            try:
                conn = mysql.connector.connect(
                    host='localhost',
                    user='root',
                    password=''
                )
                if conn.is_connected():
                    print("✅ Successfully connected with empty password!")
                    conn.close()
                    print("\nSolution: Update your password in app.py to an empty string.")
                    return True
            except mysql.connector.Error as e2:
                print(f"❌ Empty password also failed: {e2}")
        
        return False

if __name__ == "__main__":
    success = test_mysql_connection()
    sys.exit(0 if success else 1)
