import mysql.connector
import sys

# Try to connect without specifying a database
db_config = {
    'host': 'localhost',
    'user': 'GigGenius',
    'password': 'Happiness1524!'
    # No database specified
}

def test_connection():
    try:
        print("Attempting to connect to MySQL without specifying a database...")
        conn = mysql.connector.connect(**db_config)
        
        if conn.is_connected():
            print("✅ Successfully connected to MySQL!")
            
            # Get server information
            db_info = conn.get_server_info()
            print(f"MySQL Server version: {db_info}")
            
            # Get cursor and list available databases
            cursor = conn.cursor()
            cursor.execute("SHOW DATABASES;")
            dbs = cursor.fetchall()
            
            print("\nAvailable databases:")
            for db in dbs:
                print(f"- {db[0]}")
                
            cursor.close()
            conn.close()
            return True
            
    except mysql.connector.Error as e:
        print(f"❌ Error connecting to MySQL: {e}")
        return False

if __name__ == "__main__":
    success = test_connection()
    sys.exit(0 if success else 1)
