-- Migration script to add PWD approval fields to the database
-- This script adds PWD approval workflow functionality

-- Add PWD approval fields to register_genius table
ALTER TABLE register_genius 
ADD COLUMN pwd_approval_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending' COMMENT 'PWD verification status',
ADD COLUMN pwd_approved_by INT NULL COMMENT 'Admin ID who approved PWD status',
ADD COLUMN pwd_approved_at DATETIME NULL COMMENT 'When PWD status was approved',
ADD COLUMN pwd_rejection_reason TEXT NULL COMMENT 'Reason for PWD rejection (if applicable)';

-- Add PWD approval fields to approve_genius table (for approved users)
ALTER TABLE approve_genius 
ADD COLUMN pwd_approval_status ENUM('pending', 'approved', 'rejected') DEFAULT 'approved' COMMENT 'PWD verification status',
ADD COLUMN pwd_approved_by INT NULL COMMENT 'Admin ID who approved PWD status',
ADD COLUMN pwd_approved_at DATETIME NULL COMMENT 'When PWD status was approved',
ADD COLUMN pwd_rejection_reason TEXT NULL COMMENT 'Reason for PWD rejection (if applicable)';

-- Add PWD approval fields to register_client table (clients can also be PWD)
ALTER TABLE register_client 
ADD COLUMN pwd_approval_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending' COMMENT 'PWD verification status',
ADD COLUMN pwd_approved_by INT NULL COMMENT 'Admin ID who approved PWD status',
ADD COLUMN pwd_approved_at DATETIME NULL COMMENT 'When PWD status was approved',
ADD COLUMN pwd_rejection_reason TEXT NULL COMMENT 'Reason for PWD rejection (if applicable)';

-- Add PWD approval fields to approve_client table (for approved clients)
ALTER TABLE approve_client 
ADD COLUMN pwd_approval_status ENUM('pending', 'approved', 'rejected') DEFAULT 'approved' COMMENT 'PWD verification status',
ADD COLUMN pwd_approved_by INT NULL COMMENT 'Admin ID who approved PWD status',
ADD COLUMN pwd_approved_at DATETIME NULL COMMENT 'When PWD status was approved',
ADD COLUMN pwd_rejection_reason TEXT NULL COMMENT 'Reason for PWD rejection (if applicable)';

-- Create indexes for better performance
CREATE INDEX idx_register_genius_pwd_approval ON register_genius(pwd_approval_status);
CREATE INDEX idx_approve_genius_pwd_approval ON approve_genius(pwd_approval_status);
CREATE INDEX idx_register_client_pwd_approval ON register_client(pwd_approval_status);
CREATE INDEX idx_approve_client_pwd_approval ON approve_client(pwd_approval_status);

-- Update existing PWD users to have approved status (for existing data)
UPDATE register_genius SET pwd_approval_status = 'approved' WHERE is_pwd = 1;
UPDATE approve_genius SET pwd_approval_status = 'approved' WHERE is_pwd = 1;
UPDATE register_client SET pwd_approval_status = 'approved' WHERE is_pwd = 1;
UPDATE approve_client SET pwd_approval_status = 'approved' WHERE is_pwd = 1;

-- Update non-PWD users to have approved status (they don't need PWD verification)
UPDATE register_genius SET pwd_approval_status = 'approved' WHERE is_pwd = 0;
UPDATE approve_genius SET pwd_approval_status = 'approved' WHERE is_pwd = 0;
UPDATE register_client SET pwd_approval_status = 'approved' WHERE is_pwd = 0;
UPDATE approve_client SET pwd_approval_status = 'approved' WHERE is_pwd = 0;

-- Show the updated table structures
DESCRIBE register_genius;
DESCRIBE approve_genius;
DESCRIBE register_client;
DESCRIBE approve_client;
