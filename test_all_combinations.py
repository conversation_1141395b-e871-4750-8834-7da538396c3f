import mysql.connector
import sys

# Different possible combinations to try
hosts = ['localhost', '127.0.0.1']
users = ['root', 'GigGenius', 'giggenius', 'phpmyadmin']
passwords = ['Happiness1524!', '']  # Empty string for no password
databases = ['giggenius', 'GigGenius', '']  # Empty string to try connecting without specifying a database

def test_connection(host, user, password, database):
    config = {
        'host': host,
        'user': user,
        'password': password
    }
    
    # Only add database to config if it's not empty
    if database:
        config['database'] = database
    
    try:
        print(f"\nTrying: Host={host}, User={user}, Password={'*****' if password else 'None'}, Database={database if database else 'None'}")
        conn = mysql.connector.connect(**config)
        
        if conn.is_connected():
            print(f"✅ SUCCESS! Connected with: Host={host}, User={user}, Database={database if database else 'None'}")
            
            # Get server information
            db_info = conn.get_server_info()
            print(f"MySQL Server version: {db_info}")
            
            # Get cursor and execute a simple query
            cursor = conn.cursor()
            
            if database:
                cursor.execute("SELECT DATABASE();")
                db_name = cursor.fetchone()[0]
                print(f"Connected to database: {db_name}")
            
            # List available databases
            cursor.execute("SHOW DATABASES;")
            dbs = cursor.fetchall()
            
            print("\nAvailable databases:")
            for db in dbs:
                print(f"- {db[0]}")
                
            cursor.close()
            conn.close()
            return True
    except mysql.connector.Error as e:
        print(f"❌ Failed: {e}")
        return False

def main():
    success_count = 0
    total_attempts = len(hosts) * len(users) * len(passwords) * len(databases)
    
    print(f"Testing {total_attempts} different combinations...")
    
    for host in hosts:
        for user in users:
            for password in passwords:
                for database in databases:
                    if test_connection(host, user, password, database):
                        success_count += 1
    
    print(f"\nResults: {success_count} successful connections out of {total_attempts} attempts.")
    
    if success_count > 0:
        return 0
    else:
        return 1

if __name__ == "__main__":
    sys.exit(main())
