import subprocess
import socket
import sys
import os

def check_mysql_service():
    print("=== Checking MySQL Service ===")
    
    # Check if MySQL service is running (Windows)
    try:
        print("\nChecking if MySQL service is running...")
        result = subprocess.run(["sc", "query", "MySQL"], capture_output=True, text=True)
        
        if "RUNNING" in result.stdout:
            print("✅ MySQL service is running")
        else:
            print("❌ MySQL service is not running")
            print(result.stdout)
    except Exception as e:
        print(f"Error checking MySQL service: {e}")
    
    # Check if port 3306 is open
    try:
        print("\nChecking if port 3306 is open...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex(('127.0.0.1', 3306))
        sock.close()
        
        if result == 0:
            print("✅ Port 3306 is open (MySQL is accepting connections)")
        else:
            print("❌ Port 3306 is closed (MySQL is not accepting connections)")
    except Exception as e:
        print(f"Error checking port: {e}")
    
    # Try to ping MySQL
    try:
        print("\nTrying to ping MySQL server...")
        result = subprocess.run(["ping", "-n", "1", "localhost"], capture_output=True, text=True)
        print(result.stdout)
    except Exception as e:
        print(f"Error pinging MySQL: {e}")
    
    print("\n=== Suggestions ===")
    print("1. Make sure MySQL service is running")
    print("2. Check if the MySQL password is correct")
    print("3. Try connecting with MySQL Workbench or another tool")
    print("4. Check if the MySQL user has the correct privileges")
    print("5. Try restarting the MySQL service")

if __name__ == "__main__":
    check_mysql_service()
