-- Migration script to add PWD (Person with Disability) fields to the database
-- This script adds PWD support with commission rate functionality

-- Add PWD fields to register_genius table
ALTER TABLE register_genius 
ADD COLUMN is_pwd TINYINT(1) DEFAULT 0 COMMENT 'Whether user is a Person with Disability',
ADD COLUMN pwd_condition TEXT NULL COMMENT 'Description of PWD condition (internal use only)',
ADD COLUMN pwd_proof VARCHAR(255) NULL COMMENT 'File path for PWD proof document',
ADD COLUMN commission_rate DECIMAL(5,2) DEFAULT 10.00 COMMENT 'Commission rate percentage (10.00 for regular, 5.00 for PWD)';

-- Add PWD fields to approve_genius table (for approved users)
ALTER TABLE approve_genius 
ADD COLUMN is_pwd TINYINT(1) DEFAULT 0 COMMENT 'Whether user is a Person with Disability',
ADD COLUMN pwd_condition TEXT NULL COMMENT 'Description of PWD condition (internal use only)',
ADD COLUMN pwd_proof VARCHAR(255) NULL COMMENT 'File path for PWD proof document',
ADD COLUMN commission_rate DECIMAL(5,2) DEFAULT 10.00 COMMENT 'Commission rate percentage (10.00 for regular, 5.00 for PWD)';

-- Add PWD fields to register_client table (clients can also be PWD)
ALTER TABLE register_client 
ADD COLUMN is_pwd TINYINT(1) DEFAULT 0 COMMENT 'Whether user is a Person with Disability',
ADD COLUMN pwd_condition TEXT NULL COMMENT 'Description of PWD condition (internal use only)',
ADD COLUMN pwd_proof VARCHAR(255) NULL COMMENT 'File path for PWD proof document',
ADD COLUMN commission_rate DECIMAL(5,2) DEFAULT 10.00 COMMENT 'Commission rate percentage (10.00 for regular, 5.00 for PWD)';

-- Add PWD fields to approve_client table (for approved clients)
ALTER TABLE approve_client 
ADD COLUMN is_pwd TINYINT(1) DEFAULT 0 COMMENT 'Whether user is a Person with Disability',
ADD COLUMN pwd_condition TEXT NULL COMMENT 'Description of PWD condition (internal use only)',
ADD COLUMN pwd_proof VARCHAR(255) NULL COMMENT 'File path for PWD proof document',
ADD COLUMN commission_rate DECIMAL(5,2) DEFAULT 10.00 COMMENT 'Commission rate percentage (10.00 for regular, 5.00 for PWD)';

-- Create indexes for better performance
CREATE INDEX idx_register_genius_is_pwd ON register_genius(is_pwd);
CREATE INDEX idx_approve_genius_is_pwd ON approve_genius(is_pwd);
CREATE INDEX idx_register_client_is_pwd ON register_client(is_pwd);
CREATE INDEX idx_approve_client_is_pwd ON approve_client(is_pwd);

-- Update existing records to have default commission rates
UPDATE register_genius SET commission_rate = 10.00 WHERE commission_rate IS NULL;
UPDATE approve_genius SET commission_rate = 10.00 WHERE commission_rate IS NULL;
UPDATE register_client SET commission_rate = 10.00 WHERE commission_rate IS NULL;
UPDATE approve_client SET commission_rate = 10.00 WHERE commission_rate IS NULL;

-- Show the updated table structures
DESCRIBE register_genius;
DESCRIBE approve_genius;
DESCRIBE register_client;
DESCRIBE approve_client;
