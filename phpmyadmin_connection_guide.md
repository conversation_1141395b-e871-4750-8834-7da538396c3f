# Connecting to phpMyAdmin and Configuring Flask Database Connection

This guide will help you connect to the phpMyAdmin interface at https://gig-genius.io/phpmyadmin/ and update your Flask application to use the remote database.

## Accessing phpMyAdmin

1. **Open your web browser** and navigate to:
   ```
   https://gig-genius.io/phpmyadmin/
   ```

2. **Log in with your MySQL credentials**:
   - Username: `root` (or the username provided by your hosting provider)
   - Password: `Happiness1524!` (or the password provided by your hosting provider)

3. **After logging in**:
   - You'll see a list of databases on the left sidebar
   - Look for the `giggenius` database and click on it to explore its tables
   - You can verify the database structure, tables, and data

## Verifying Remote Database Connection

Run the `verify_remote_db.py` script to test the connection to the remote database:

```
python verify_remote_db.py
```

This script will:
1. Check if the gig-genius.io website is accessible
2. Check if the MySQL port is open
3. Attempt to connect to the remote database
4. Provide recommendations for updating your Flask application

## Updating Your Flask Application

If the verification script confirms that the remote connection works, update your `app.py` file with the correct database configuration:

```python
db_config = {
    'host': 'gig-genius.io',
    'user': 'root',
    'password': 'Happiness1524!',
    'database': 'giggenius'
}
```

## Troubleshooting Connection Issues

If you encounter connection issues:

1. **Check your internet connection**
   - Make sure you can access the website in your browser

2. **Verify credentials**
   - Double-check the username and password
   - Try logging into phpMyAdmin directly to confirm credentials

3. **Check for firewall restrictions**
   - Some networks block remote database connections
   - Try connecting from a different network if possible

4. **Contact your hosting provider**
   - If you still can't connect, your hosting provider may have specific requirements
   - They might need to whitelist your IP address for remote database access

## Using phpMyAdmin for Database Management

Once connected to phpMyAdmin, you can:

1. **Browse database tables**
   - View, edit, and delete records
   - Modify table structures

2. **Run SQL queries**
   - Click on the "SQL" tab to execute custom queries
   - Export and import data

3. **Manage users and permissions**
   - Create new database users
   - Set access permissions

4. **Monitor database performance**
   - View server status
   - Check query statistics
