import os
import webbrowser
import json
import requests
from flask import Flask, request, jsonify, render_template, session, url_for, redirect, render_template_string, flash
import mysql.connector
from mysql.connector import Error
import base64
from werkzeug.utils import secure_filename
import secrets

app = Flask(__name__)
app.secret_key = 'your-strong-fixed-secret-key'

# Database configuration
db_config = {
    'host': '**************',
    'user': 'giggenius_user',
    'password': 'Happiness1524!',
    'database': 'giggenius'
}

# LinkedIn OAuth Configuration
LINKEDIN_CLIENT_ID = '86kdtbcjiuuusw'  # Your LinkedIn Client ID
LINKEDIN_CLIENT_SECRET = 'XJWjGimj5ojosHZS'  # Your LinkedIn Client Secret

# Determine if we're running in production or development
def is_production():
    # Check if running on production domain
    host = request.host if request else None
    if not host:
        return False

    # Check for any variation of the production domain
    production_domains = ['www.gig-genius.io', 'gig-genius.io']
    return any(domain in host for domain in production_domains)

# Set the redirect URI dynamically based on the environment
def get_linkedin_redirect_uri():
    if is_production():
        return 'https://www.gig-genius.io/api/1.1/oauth_redirect'
    else:
        return 'http://localhost:5000/api/1.1/oauth_redirect'

# Function to create database connection
def get_db_connection():
    try:
        conn = mysql.connector.connect(**db_config)
        return conn
    except Error as e:
        print(f"Error connecting to database: {e}")
        return None

# LANDING PAGE ROUTE
@app.route('/')
def landing_page():
    return render_template('landing_page.html')

# PROFILE PAGE ROUTE
@app.route('/profile')
def profile():
    if 'user_id' not in session:
        return redirect(url_for('landing_page'))

    user_type = session.get('user_type')

    if user_type == 'genius':
        try:
            conn = mysql.connector.connect(**db_config)
            cursor = conn.cursor(dictionary=True)

            cursor.execute("""
                SELECT
                    id,
                    profile_photo,
                    first_name,
                    last_name,
                    email,
                    birthday,
                    country,
                    mobile,
                    position,
                    expertise,
                    hourly_rate,
                    availability,
                    tax_id,
                    introduction,
                    professional_sum
                FROM approve_genius
                WHERE id = %s
            """, (session['user_id'],))
            genius_data = cursor.fetchone()

            if genius_data:
                # Convert binary profile photo data to base64 for display in HTML
                profile_picture_url = url_for('static', filename='img/default_profile.png')
                if genius_data['profile_photo']:
                    profile_photo_base64 = base64.b64encode(genius_data['profile_photo']).decode()
                    profile_picture_url = f"data:image/jpeg;base64,{profile_photo_base64}"

                genius = {
                    'profile_picture_url': profile_picture_url,
                    'first_name': genius_data.get('first_name', ''),
                    'last_name': genius_data.get('last_name', ''),
                    'email': genius_data.get('email', ''),
                    'birthday': genius_data.get('birthday', ''),
                    'country': genius_data.get('country', ''),
                    'mobile': genius_data.get('mobile', ''),
                    'position': genius_data.get('position', ''),
                    'expertise': genius_data.get('expertise', ''),
                    'hourly_rate': genius_data.get('hourly_rate', ''),
                    'availability': genius_data.get('availability', ''),
                    'tax_id': genius_data.get('tax_id', ''),
                    'introduction': genius_data.get('introduction', ''),
                    'professional_sum': genius_data.get('professional_sum', '')
                }

                return render_template('genius_profile.html', genius=genius)

        except mysql.connector.Error as err:
            print(f"Database Error: {err}")
            return redirect(url_for('genius_page'))

        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()

    elif user_type == 'client':
        try:
            conn = mysql.connector.connect(**db_config)
            cursor = conn.cursor(dictionary=True)

            cursor.execute("""
                SELECT
                    id,
                    profile_photo,
                    first_name,
                    last_name,
                    work_email,
                    birthday,
                    country,
                    mobile,
                    position,
                    business_name,
                    business_address,
                    business_email,
                    industry,
                    business_website,
                    employee_count,
                    introduction
                FROM approve_client
                WHERE id = %s
            """, (session['user_id'],))
            client_data = cursor.fetchone()

            if client_data:
                # Convert binary profile photo data to base64 for display in HTML
                profile_picture_url = url_for('static', filename='img/default_profile.png')
                if client_data['profile_photo']:
                    profile_photo_base64 = base64.b64encode(client_data['profile_photo']).decode()
                    profile_picture_url = f"data:image/jpeg;base64,{profile_photo_base64}"

                client = {
                    'profile_picture_url': profile_picture_url,
                    'first_name': client_data.get('first_name', ''),
                    'last_name': client_data.get('last_name', ''),
                    'work_email': client_data.get('work_email', ''),
                    'birthday': client_data.get('birthday', ''),
                    'country': client_data.get('country', ''),
                    'mobile': client_data.get('mobile', ''),
                    'position': client_data.get('position', ''),
                    'business_name': client_data.get('business_name', ''),
                    'business_address': client_data.get('business_address', ''),
                    'business_email': client_data.get('business_email', ''),
                    'industry': client_data.get('industry', ''),
                    'business_website': client_data.get('business_website', ''),
                    'employee_count': client_data.get('employee_count', ''),
                    'introduction': client_data.get('introduction', '')
                }

                return render_template('client_profile.html', client=client)

        except mysql.connector.Error as err:
            print(f"Database Error: {err}")
            return redirect(url_for('client_page'))

        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()

    # If user type is not recognized or no data found
    return redirect(url_for('landing_page'))

# LOGIN ROUTE
@app.route('/login', methods=['POST'])
def login():
    if request.method == 'POST':
        try:
            conn = get_db_connection()
            if conn is None:
                return jsonify({
                    'success': False,
                    'error': 'Database connection failed'
                })

            cursor = conn.cursor(dictionary=True)
            email = request.form['email']
            password = request.form['password']

            # First check admin table
            cursor.execute("""
                SELECT id, username, password
                FROM admin
                WHERE username = %s AND password = %s
            """, (email, password))

            admin = cursor.fetchone()

            if admin:
                session.clear()
                session['user_id'] = admin['id']
                session['user_type'] = 'admin'
                session['username'] = admin['username']
                session.permanent = True

                cursor.close()
                conn.close()

                return jsonify({
                    'success': True,
                    'redirect': url_for('admin_page')
                })

            # If not admin, check approve_genius table
            cursor.execute("""
                SELECT id, email, password, first_name, last_name
                FROM approve_genius
                WHERE email = %s AND password = %s
            """, (email, password))

            genius = cursor.fetchone()

            if genius:
                session.clear()
                session['user_id'] = genius['id']
                session['user_type'] = 'genius'
                session['email'] = genius['email']
                session['name'] = f"{genius['first_name']} {genius['last_name']}"
                session.permanent = True

                cursor.close()
                conn.close()

                return jsonify({
                    'success': True,
                    'redirect': url_for('genius_page')
                })

            # If not genius, check approve_client table
            cursor.execute("""
                SELECT id, work_email, password, first_name, last_name
                FROM approve_client
                WHERE work_email = %s AND password = %s
            """, (email, password))

            client = cursor.fetchone()

            if client:
                session.clear()
                session['user_id'] = client['id']
                session['user_type'] = 'client'
                session['email'] = client['work_email']
                session['name'] = f"{client['first_name']} {client['last_name']}"
                session.permanent = True

                cursor.close()
                conn.close()

                return jsonify({
                    'success': True,
                    'redirect': url_for('client_page')
                })

            # If no match found
            cursor.close()
            conn.close()

            return jsonify({
                'success': False,
                'error': 'Invalid email or password'
            })

        except Exception as e:
            print(f"Login error: {str(e)}")
            return jsonify({
                'success': False,
                'error': 'An error occurred during login'
            })

# GENIUS REGISTRATION ROUTE
@app.route('/register_genius', methods=['GET', 'POST'])
def register_genius():
    if request.method == 'POST':
        try:
            conn = get_db_connection()
            if conn is None:
                return jsonify({
                    'success': False,
                    'error': 'Database connection failed'
                })

            cursor = conn.cursor()

            # Handle file uploads with better error checking and debugging
            profile_photo = None
            id_front = None
            id_back = None

            # Debug print
            print("Files received:", list(request.files.keys()))

            if 'profilePhoto' in request.files:
                file = request.files['profilePhoto']
                if file.filename != '':
                    profile_photo = file.read()
                    print("Profile photo size:", len(profile_photo))

            if 'idFront' in request.files:
                file = request.files['idFront']
                if file.filename != '':
                    id_front = file.read()
                    print("ID Front size:", len(id_front))

            if 'idBack' in request.files:
                file = request.files['idBack']
                if file.filename != '':
                    id_back = file.read()
                    print("ID Back size:", len(id_back))

            # Validate that ID photos are present
            if not id_front or not id_back:
                return jsonify({
                    'success': False,
                    'error': 'Both front and back ID photos are required'
                }), 400

            # Validate required fields
            required_fields = ['firstName', 'lastName', 'email', 'password', 'birthday',
                             'country', 'mobile', 'position', 'expertise', 'hourly_rate',
                             'availability', 'tax_id', 'introduction', 'professional_sum']

            missing_fields = [field for field in required_fields
                            if field not in request.form or not request.form[field].strip()]

            if missing_fields:
                return jsonify({
                    'success': False,
                    'error': f'Missing required fields: {", ".join(missing_fields)}'
                }), 400

            # Convert checkbox values to tinyint with default 0
            email_updates = 1 if request.form.get('email_updates') == '1' else 0
            terms_agreement = 1 if request.form.get('terms_agreement') == '1' else 0

            # SQL query
            sql = """
            INSERT INTO register_genius (
                profile_photo, first_name, last_name, email, password,
                birthday, country, mobile, position, expertise,
                hourly_rate, availability, tax_id, introduction, professional_sum,
                id_front, id_back, email_updates, terms_agreement
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
            """

            # Prepare data tuple
            data = (
                profile_photo,
                request.form['firstName'].strip(),
                request.form['lastName'].strip(),
                request.form['email'].strip(),
                request.form['password'],
                request.form['birthday'],
                request.form['country'],
                request.form['mobile'].strip(),
                request.form['position'],
                request.form['expertise'],
                float(request.form['hourly_rate']),
                request.form['availability'],
                request.form['tax_id'].strip(),
                request.form['introduction'].strip(),
                request.form['professional_sum'].strip(),
                id_front,
                id_back,
                email_updates,
                terms_agreement
            )

            # Debug print the data tuple (excluding binary data)
            print("Data to be inserted:", [
                "binary_data" if isinstance(x, bytes) else x
                for x in data
            ])

            cursor.execute(sql, data)
            conn.commit()

            return jsonify({
                'success': True,
                'message': 'Registration successful'
            })

        except mysql.connector.Error as err:
            print(f"Database Error: {err}")
            if conn:
                conn.rollback()
            return jsonify({
                'success': False,
                'error': f'Database error: {str(err)}'
            }), 500

        except Exception as e:
            print(f"Unexpected error: {str(e)}")
            if conn:
                conn.rollback()
            return jsonify({
                'success': False,
                'error': f'Unexpected error: {str(e)}'
            }), 500

        finally:
            if 'cursor' in locals():
                cursor.close()
            if conn:
                conn.close()

    return render_template('genius_registration.html')

@app.route('/genius_registration', methods=['GET', 'POST'])
def genius_registration():
    return render_template('genius_registration.html')

# CLIENT REGISTRATION ROUTE
@app.route('/register_client', methods=['GET', 'POST'])
def register_client():
    if request.method == 'POST':
        try:
            conn = get_db_connection()
            if conn is None:
                return jsonify({
                    'success': False,
                    'error': 'Database connection failed'
                })

            cursor = conn.cursor()

            # Handle file uploads
            profile_photo = None
            business_logo = None
            business_reg_doc = None

            print("Files received:", list(request.files.keys()))

            if 'profilePhoto' in request.files:
                file = request.files['profilePhoto']
                if file.filename != '':
                    profile_photo = file.read()
                    print("Profile photo size:", len(profile_photo))

            if 'businessLogo' in request.files:
                file = request.files['businessLogo']
                if file.filename != '':
                    business_logo = file.read()
                    print("Business logo size:", len(business_logo))

            if 'businessReg' in request.files:
                file = request.files['businessReg']
                if file.filename != '':
                    business_reg_doc = file.read()
                    print("Business registration doc size:", len(business_reg_doc))

            # Validate required fields
            required_fields = [
                'firstName', 'lastName', 'email', 'password', 'birthday',
                'country', 'mobile', 'position', 'businessName',
                'businessAddress', 'businessEmail', 'industry',
                'employeeCount'
            ]

            missing_fields = [field for field in required_fields if not request.form.get(field)]
            if missing_fields:
                return jsonify({
                    'success': False,
                    'error': f'Missing required fields: {", ".join(missing_fields)}'
                }), 400

            # Convert checkbox values to tinyint
            email_updates = 1 if request.form.get('email_updates') == '1' else 0
            terms_agreement = 1 if request.form.get('terms_agreement') == '1' else 0

            # Check if this is a LinkedIn registration
            linkedin_id = request.form.get('linkedin_id', None)

            # SQL query
            sql = """
            INSERT INTO register_client (
                profile_photo, first_name, last_name, work_email, password,
                birthday, country, mobile, position, business_logo,
                business_name, business_address, business_email, industry,
                business_website, employee_count, introduction, business_registration_doc,
                email_updates, terms_agreement, status, linkedin_id
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
            """

            # Prepare data tuple
            data = (
                profile_photo,
                request.form['firstName'].strip(),
                request.form['lastName'].strip(),
                request.form['email'].strip(),
                request.form['password'],
                request.form['birthday'],
                request.form['country'],
                request.form['mobile'].strip(),
                request.form['position'].strip(),
                business_logo,
                request.form['businessName'].strip(),
                request.form['businessAddress'].strip(),
                request.form['businessEmail'].strip(),
                request.form['industry'],
                request.form.get('businessWebsite', '').strip(),
                request.form['employeeCount'],
                request.form['introduction'].strip(),
                business_reg_doc,
                email_updates,
                terms_agreement,
                'pending',
                linkedin_id
            )

            # Debug print the data tuple (excluding binary data)
            print("Data to be inserted:", [
                "binary_data" if isinstance(x, bytes) else x
                for x in data
            ])

            cursor.execute(sql, data)
            conn.commit()

            # Clear LinkedIn data from session if it exists
            if 'linkedin_data' in session:
                session.pop('linkedin_data', None)

            return jsonify({
                'success': True,
                'message': 'Registration successful'
            })

        except mysql.connector.Error as err:
            print(f"Database Error: {err}")
            if conn:
                conn.rollback()
            return jsonify({
                'success': False,
                'error': f'Database error: {str(err)}'
            }), 500

        except Exception as e:
            print(f"Unexpected error: {str(e)}")
            if conn:
                conn.rollback()
            return jsonify({
                'success': False,
                'error': f'Unexpected error: {str(e)}'
            }), 500

        finally:
            if 'cursor' in locals():
                cursor.close()
            if conn:
                conn.close()

    # Check if there's LinkedIn data in the session
    linkedin_data = session.get('linkedin_data', None)

    # If coming from LinkedIn OAuth flow, pre-fill the form
    if linkedin_data and request.args.get('source') == 'linkedin':
        return render_template('client_registration.html',
                              linkedin_data=linkedin_data,
                              source='linkedin')

    return render_template('client_registration.html')

@app.route('/client_registration', methods=['GET', 'POST'])
def client_registration():
    # Check if there's LinkedIn data in the session
    linkedin_data = session.get('linkedin_data', None)

    # If coming from LinkedIn OAuth flow, pre-fill the form
    if linkedin_data and request.args.get('source') == 'linkedin':
        return render_template('client_registration.html',
                              linkedin_data=linkedin_data,
                              source='linkedin')

    return render_template('client_registration.html')

# ADMIN PAGE ROUTE
@app.route('/admin_page')
def admin_page():
    if 'user_id' not in session or session.get('user_type') != 'admin':
        return redirect(url_for('landing_page'))

    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor(dictionary=True)

    # Get admin profile data
    cursor.execute("SELECT profile FROM admin WHERE id = %s", (session['user_id'],))
    admin_data = cursor.fetchone()

    # Convert profile blob to base64 if it exists
    admin = {}
    if admin_data and admin_data['profile']:
        profile_base64 = base64.b64encode(admin_data['profile']).decode()
        admin['profile_picture_url'] = f"data:image/jpeg;base64,{profile_base64}"
    else:
        admin['profile_picture_url'] = url_for('static', filename='img/default_profile.png')

    # Get other data...
    cursor.execute("SELECT * FROM register_genius")
    register_geniuses = cursor.fetchall()

    cursor.execute("SELECT * FROM approve_genius")
    approve_geniuses = cursor.fetchall()

    cursor.execute("SELECT * FROM register_client")
    register_clients = cursor.fetchall()
    for client in register_clients:
        if client['profile_photo']:
            client['profile_photo'] = f"data:image/jpeg;base64,{base64.b64encode(client['profile_photo']).decode()}"
        else:
            client['profile_photo'] = url_for('static', filename='img/default_profile.png')

    cursor.execute("SELECT * FROM approve_client")
    approve_clients = cursor.fetchall()

    cursor.close()
    conn.close()

    return render_template('admin_page.html',
                         admin=admin,
                         register_geniuses=register_geniuses,
                         approve_geniuses=approve_geniuses,
                         register_clients=register_clients,
                         approve_clients=approve_clients)

@app.route('/get_genius_details/<int:id>')
def get_genius_details(id):
    if 'user_id' not in session or session.get('user_type') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor(dictionary=True)

    cursor.execute("SELECT * FROM register_genius WHERE id = %s", (id,))
    genius = cursor.fetchone()

    cursor.close()
    conn.close()

    if genius:
        # Convert binary data to base64 for images
        if genius['profile_photo']:
            genius['profile_photo'] = base64.b64encode(genius['profile_photo']).decode()
        if genius['id_front']:
            genius['id_front'] = base64.b64encode(genius['id_front']).decode()
        if genius['id_back']:
            genius['id_back'] = base64.b64encode(genius['id_back']).decode()

        return jsonify(genius)

    return jsonify({'error': 'Genius not found'}), 404

@app.route('/update_genius_status', methods=['POST'])
def update_genius_status():
    if 'user_id' not in session or session.get('user_type') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    data = request.json
    genius_id = data.get('genius_id')
    status = data.get('status')

    if not genius_id or not status:
        return jsonify({'error': 'Missing genius_id or status'}), 400

    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor(dictionary=True)

    try:
        if status == 'approved':
            # Get genius data
            cursor.execute("SELECT * FROM register_genius WHERE id = %s", (genius_id,))
            genius = cursor.fetchone()

            if not genius:
                return jsonify({'error': 'Genius not found'}), 404

            # Insert into approve_genius
            cursor.execute("""
                INSERT INTO approve_genius (
                    profile_photo, first_name, last_name, email, password,
                    birthday, country, mobile, position, expertise,
                    hourly_rate, availability, tax_id, introduction, professional_sum,
                    id_front, id_back, email_updates, terms_agreement
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            """, (
                genius['profile_photo'], genius['first_name'], genius['last_name'],
                genius['email'], genius['password'], genius['birthday'],
                genius['country'], genius['mobile'], genius['position'],
                genius['expertise'], genius['hourly_rate'], genius['availability'],
                genius['tax_id'], genius['introduction'], genius['professional_sum'],
                genius['id_front'], genius['id_back'], genius['email_updates'],
                genius['terms_agreement']
            ))

            # Delete from register_genius
            cursor.execute("DELETE FROM register_genius WHERE id = %s", (genius_id,))

        else:
            # Update status for declined
            cursor.execute("UPDATE register_genius SET status = %s WHERE id = %s",
                         (status, genius_id))

        conn.commit()
        return jsonify({'success': True})

    except mysql.connector.Error as err:
        print(f"Database Error: {err}")
        conn.rollback()
        return jsonify({'error': str(err)}), 500

    except Exception as e:
        print(f"Error: {e}")
        conn.rollback()
        return jsonify({'error': str(e)}), 500

    finally:
        cursor.close()
        conn.close()

@app.route('/update_client_status', methods=['POST'])
def update_client_status():
    if 'user_id' not in session or session.get('user_type') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    data = request.json
    client_id = data.get('client_id')
    status = data.get('status')

    if not client_id or not status:
        return jsonify({'error': 'Missing client_id or status'}), 400

    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor(dictionary=True)

    try:
        if status == 'approved':
            # Get client data from register_client
            cursor.execute("""
                SELECT
                    profile_photo,
                    first_name,
                    last_name,
                    work_email,
                    password,
                    birthday,
                    country,
                    mobile,
                    position,
                    business_logo,
                    business_name,
                    business_address,
                    business_email,
                    industry,
                    business_website,
                    employee_count,
                    introduction,  # Add this line
                    business_registration_doc,
                    email_updates,
                    terms_agreement,
                    created_at
                FROM register_client
                WHERE id = %s
            """, (client_id,))

            client = cursor.fetchone()

            if not client:
                return jsonify({'error': 'Client not found'}), 404

            # Insert into approve_client table
            cursor.execute("""
                INSERT INTO approve_client (
                    profile_photo,
                    first_name,
                    last_name,
                    work_email,
                    password,
                    birthday,
                    country,
                    mobile,
                    position,
                    business_logo,
                    business_name,
                    business_address,
                    business_email,
                    industry,
                    business_website,
                    employee_count,
                    introduction,
                    business_registration_doc,
                    email_updates,
                    terms_agreement,
                    created_at,
                    linkedin_id
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            """, (
                client['profile_photo'],
                client['first_name'],
                client['last_name'],
                client['work_email'],
                client['password'],
                client['birthday'],
                client['country'],
                client['mobile'],
                client['position'],
                client['business_logo'],
                client['business_name'],
                client['business_address'],
                client['business_email'],
                client['industry'],
                client['business_website'],
                client['employee_count'],
                client['introduction'],
                client['business_registration_doc'],
                client['email_updates'],
                client['terms_agreement'],
                client['created_at'],
                client.get('linkedin_id', None)
            ))

            # Delete from register_client after successful insertion
            cursor.execute("DELETE FROM register_client WHERE id = %s", (client_id,))

        else:
            # Update status for declined
            cursor.execute("UPDATE register_client SET status = %s WHERE id = %s",
                         (status, client_id))

        conn.commit()
        return jsonify({'success': True})

    except mysql.connector.Error as err:
        print(f"Database Error: {err}")
        conn.rollback()
        return jsonify({'error': str(err)}), 500

    except Exception as e:
        print(f"Error: {e}")
        conn.rollback()
        return jsonify({'error': str(e)}), 500

    finally:
        cursor.close()
        conn.close()

@app.route('/get_client_details/<int:client_id>')
def get_client_details(client_id):
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor(dictionary=True)

        cursor.execute("""
            SELECT
                id,
                first_name,
                last_name,
                work_email,
                DATE_FORMAT(birthday, '%Y-%m-%d') as birthday,
                country,
                mobile,
                position,
                business_name,
                business_address,
                business_email,
                industry,
                business_website as website,
                employee_count,
                introduction,
                status,
                DATE_FORMAT(created_at, '%Y-%m-%d %H:%i') as created_at,
                linkedin_id
            FROM register_client
            WHERE id = %s
        """, (client_id,))

        client = cursor.fetchone()

        if client:
            # Handle binary data separately
            cursor.execute("SELECT profile_photo, business_logo, business_registration_doc FROM register_client WHERE id = %s", (client_id,))
            binary_data = cursor.fetchone()

            # Convert binary data to base64 if exists
            if binary_data['profile_photo']:
                client['profile_photo'] = f"data:image/jpeg;base64,{base64.b64encode(binary_data['profile_photo']).decode()}"

            if binary_data['business_logo']:
                client['business_logo'] = f"data:image/jpeg;base64,{base64.b64encode(binary_data['business_logo']).decode()}"

            if binary_data['business_registration_doc']:
                # Specifically format PDF data
                client['business_registration_doc'] = f"data:application/pdf;base64,{base64.b64encode(binary_data['business_registration_doc']).decode()}"

            return jsonify(client)

        return jsonify({'error': 'Client not found'}), 404

    except mysql.connector.Error as err:
        print(f"Database Error: {err}")
        return jsonify({'error': 'Database error occurred'}), 500

    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()


# GENIUS PAGE ROUTE
@app.route('/genius_page')
def genius_page():
    if 'user_id' not in session or session.get('user_type') != 'genius':
        return redirect(url_for('landing_page'))

    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor(dictionary=True)

        # Get the genius's data from approve_genius table using the user_id from session
        cursor.execute("""
            SELECT
                id,
                profile_photo,
                first_name,
                last_name,
                email,
                birthday,
                country,
                mobile,
                position,
                expertise,
                hourly_rate,
                availability,
                tax_id,
                introduction,
                professional_sum,
                created_at
            FROM approve_genius
            WHERE id = %s
        """, (session['user_id'],))
        genius_data = cursor.fetchone()

        if genius_data:
            # Convert binary profile photo data to base64 for display in HTML
            profile_picture_url = url_for('static', filename='img/default_profile.png')
            if genius_data['profile_photo']:
                profile_photo_base64 = base64.b64encode(genius_data['profile_photo']).decode()
                profile_picture_url = f"data:image/jpeg;base64,{profile_photo_base64}"

            genius = {
                'profile_picture_url': profile_picture_url,
                'first_name': genius_data.get('first_name', ''),
                'last_name': genius_data.get('last_name', ''),
                'position': genius_data.get('position', ''),
                'country': genius_data.get('country', ''),
                'expertise': genius_data.get('expertise', ''),
                'hourly_rate': genius_data.get('hourly_rate', ''),
                'availability': genius_data.get('availability', ''),
                'introduction': genius_data.get('introduction', '')
            }

            # Fetch job submissions for the genius dashboard
            cursor.execute("""
                SELECT
                    j.id,
                    j.client_id,
                    j.title,
                    j.description,
                    j.project_size,
                    j.project_description,
                    j.duration,
                    j.experience_level,
                    j.hiring_preference,
                    j.budget_type,
                    j.budget_amount,
                    j.category,
                    j.specialty,
                    j.skills,
                    j.job_type,
                    j.created_at
                FROM job_submissions j
                ORDER BY j.created_at DESC
            """)
            job_posts = cursor.fetchall()

            # Process job submissions to add client profile photos and names
            for job in job_posts:
                # Format the date here in Python instead of in SQL
                if 'created_at' in job and job['created_at']:
                    from datetime import datetime
                    if isinstance(job['created_at'], datetime):
                        job['created_at_formatted'] = job['created_at'].strftime('%b %d, %Y')
                    else:
                        # If it's already a string, just use it as is
                        job['created_at_formatted'] = job['created_at']

                if job['client_id']:
                    cursor.execute("""
                        SELECT profile_photo, first_name, last_name, business_name
                        FROM approve_client
                        WHERE id = %s
                    """, (job['client_id'],))
                    client = cursor.fetchone()
                    if client:
                        if client['profile_photo']:
                            job['profile_photo_url'] = f"data:image/jpeg;base64,{base64.b64encode(client['profile_photo']).decode()}"
                        job['first_name'] = client['first_name']
                        job['last_name'] = client['last_name']
                        job['business_name'] = client['business_name']

            return render_template('genius_page.html', genius=genius, job_posts=job_posts)

        return redirect(url_for('landing_page'))

    except mysql.connector.Error as err:
        print(f"Database Error: {err}")
        return redirect(url_for('landing_page'))

    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()


# CLIENT PAGE ROUTE
@app.route('/client_page')
def client_page():
    if 'user_id' not in session or session.get('user_type') != 'client':
        return redirect(url_for('landing_page'))

    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor(dictionary=True)

        # Get the client's data from approve_client table using the user_id from session
        cursor.execute("""
            SELECT
                id,
                profile_photo,
                first_name,
                last_name,
                work_email,
                birthday,
                country,
                mobile,
                position,
                business_logo,
                business_name,
                business_address,
                business_email,
                industry,
                business_website,
                employee_count,
                introduction,
                business_registration_doc,
                email_updates,
                terms_agreement,
                created_at
            FROM approve_client
            WHERE id = %s
        """, (session['user_id'],))
        client_data = cursor.fetchone()

        if client_data and client_data['profile_photo']:
            # Convert binary profile photo data to base64 for display in HTML
            profile_photo_base64 = base64.b64encode(client_data['profile_photo']).decode()
            profile_picture_url = f"data:image/jpeg;base64,{profile_photo_base64}"

            client = {
                'profile_picture_url': profile_picture_url,
                'first_name': client_data.get('first_name', ''),
                'last_name': client_data.get('last_name', ''),
                'company_name': client_data.get('company_name', ''),
                'work_email': client_data.get('work_email', '')
            }

            return render_template('client_page.html', client=client)

        return redirect(url_for('landing_page'))

    except mysql.connector.Error as err:
        print(f"Database Error: {err}")
        return redirect(url_for('landing_page'))

    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()


# FOOTER LINKS ROUTE
@app.route('/why_giggenius')
def why_giggenius():
    return render_template('why_giggenius.html')

@app.route('/find_geniuses')
def find_geniuses():
    return render_template('find_geniuses.html')

@app.route('/find_gigs')
def find_gigs():
    return render_template('find_gigs.html')

@app.route('/how_to_hire')
def how_to_hire():
    return render_template('f_how_to_hire.html')

@app.route('/accounting_services')
def accounting_services():
    return render_template('f_accounting_services.html')

@app.route('/events')
def events():
    return render_template('f_events.html')

@app.route('/ph_business_loan')
def ph_business_loan():
    return render_template('f_ph_business_loan.html')

@app.route('/how_it_works')
def how_it_works():
    return render_template('f_how_it_works.html')

@app.route('/why_cant_apply')
def why_cant_apply():
    return render_template('f_why_cant_apply.html')

@app.route('/direct_contracts')
def direct_contracts():
    return render_template('f_direct_contracts.html')

@app.route('/find_mentors')
def find_mentors():
    return render_template('f_find_mentors.html')

@app.route('/ph_health_insurance')
def ph_health_insurance():
    return render_template('f_ph_health_insurance.html')

@app.route('/ph_life_insurance')
def ph_life_insurance():
    return render_template('f_ph_life_insurance.html')

@app.route('/help_and_support')
def help_and_support():
    return render_template('f_help_and_support.html')

@app.route('/news_and_events')
def news_and_events():
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Get all published news/events
        cursor.execute("""
            SELECT * FROM news_events
            WHERE is_published = 1
            ORDER BY created_at DESC
        """)
        news_events = cursor.fetchall()

        # Debug print
        print(f"Found {len(news_events)} news/events")

        return render_template('f_news_and_events.html', news_events=news_events)

    except mysql.connector.Error as err:
        print(f"Database Error in news_and_events route: {err}")
        return render_template('f_news_and_events.html', news_events=[])

    finally:
        if 'cursor' in locals():
            cursor.close()
        if conn:
            conn.close()

@app.route('/affiliate_program')
def affiliate_program():
    return render_template('f_affiliate_program.html')

@app.route('/about_us')
def about_us():
    return render_template('f_about_us.html')

@app.route('/contact_us')
def contact_us():
    return render_template('f_contact_us.html')

@app.route('/charity_projects')
def charity_projects():
    return render_template('f_charity_projects.html')

@app.route('/terms_of_service')
def terms_of_service():
    return render_template('f_terms_of_service.html')

@app.route('/privacy_policy')
def privacy_policy():
    return render_template('f_privacy_policy.html')

@app.route('/user_agreement')
def user_agreement():
    return render_template('f_user_agreement.html')

@app.route('/affiliate_dashboard')
def affiliate_dashboard():
    # Check if user is logged in as affiliate
    if 'affiliate_id' not in session:
        return redirect(url_for('landing_page'))

    # Get affiliate data from database
    affiliate_id = session.get('affiliate_id')

    conn = get_db_connection()
    if conn is None:
        return "Database connection failed", 500

    cursor = conn.cursor(dictionary=True)

    # Get affiliate data with all needed fields
    cursor.execute("""
        SELECT
            id, first_name, last_name, email, phone, referral_code,
            status, commission_rate_freelancer, commission_rate_client,
            total_earnings, profile_photo
        FROM affiliates
        WHERE id = %s
    """, (affiliate_id,))

    affiliate = cursor.fetchone()

    if not affiliate:
        session.clear()
        return redirect(url_for('landing_page'))

    # Add name field for template
    affiliate['name'] = f"{affiliate['first_name']} {affiliate['last_name']}"

    # Set default profile photo if none exists
    if not affiliate.get('profile_photo'):
        affiliate['profile_photo'] = url_for('static', filename='img/default-profile.jpg')

    # Get referrals data (placeholder - implement actual query)
    referrals = []  # You'll need to implement this query

    # Add current date/time for the template
    from datetime import datetime
    now = datetime.now()

    # Add total_referrals field
    affiliate['total_referrals'] = len(referrals)

    cursor.close()
    conn.close()

    # Pass all required variables to the template
    return render_template('affiliate_dashboard.html',
                          affiliate=affiliate,
                          referrals=referrals,
                          now=now,
                          request=request)


# LOGOUT ROUTE
@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('landing_page'))

@app.route('/check_email', methods=['POST'])
def check_email():
    try:
        email = request.json.get('email')
        registration_type = request.json.get('type', 'genius')

        if not email:
            return jsonify({'error': 'Email is required'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        if registration_type == 'client':
            # Check in both register_client and approve_client tables
            cursor.execute("""
                SELECT work_email FROM register_client WHERE work_email = %s
                UNION
                SELECT work_email FROM approve_client WHERE work_email = %s
            """, (email, email))
        else:
            # Check in both register_genius and approve_genius tables
            cursor.execute("""
                SELECT email FROM register_genius WHERE email = %s
                UNION
                SELECT email FROM approve_genius WHERE email = %s
            """, (email, email))

        result = cursor.fetchone()

        cursor.close()
        conn.close()

        return jsonify({'exists': bool(result)})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/submit_contact', methods=['POST'])
def submit_contact():
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({
                'success': False,
                'error': 'Database connection failed'
            })

        cursor = conn.cursor()

        # Get form data
        name = request.form.get('name')
        email = request.form.get('email')
        subject = request.form.get('subject')
        message = request.form.get('message')

        # Get additional info
        ip_address = request.remote_addr
        user_agent = request.headers.get('User-Agent')

        # Insert into database with proper error handling
        try:
            sql = """
            INSERT INTO contact_us_messages
            (full_name, email, subject, message, ip_address, user_agent, created_at)
            VALUES (%s, %s, %s, %s, %s, %s, NOW())
            """

            cursor.execute(sql, (name, email, subject, message, ip_address, user_agent))
            conn.commit()

            return jsonify({
                'success': True,
                'message': 'Message sent successfully'
            })

        except mysql.connector.Error as err:
            print(f"Database Error: {err}")
            if conn:
                conn.rollback()
            return jsonify({
                'success': False,
                'error': 'Failed to save message'
            }), 500

    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'An unexpected error occurred'
        }), 500

    finally:
        if 'cursor' in locals():
            cursor.close()
        if conn:
            conn.close()

# Update the affiliate_register route
@app.route('/affiliate_register', methods=['POST'])
def affiliate_register():
    try:
        # Get form data
        first_name = request.form.get('firstName', '').strip()
        last_name = request.form.get('lastName', '').strip()
        phone = request.form.get('phone', '').strip()
        email = request.form.get('email', '').strip()
        password = request.form.get('password', '').strip()

        # Validate required fields
        if not all([first_name, last_name, phone, email, password]):
            return jsonify({
                'success': False,
                'error': 'All fields are required'
            }), 400

        # Generate unique referral code
        referral_code = generate_referral_code(first_name, last_name)

        # Insert into database
        conn = get_db_connection()
        if conn is None:
            return jsonify({
                'success': False,
                'error': 'Database connection failed'
            }), 500

        cursor = conn.cursor()

        try:
            # Check if email already exists
            cursor.execute("SELECT email FROM affiliates WHERE email = %s", (email,))
            if cursor.fetchone():
                return jsonify({
                    'success': False,
                    'error': 'Email already registered'
                }), 400

            # Insert new affiliate
            sql = """
            INSERT INTO affiliates (
                first_name, last_name, phone, email, password,
                referral_code, status, commission_rate_freelancer,
                commission_rate_client, total_earnings
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            cursor.execute(sql, (
                first_name,
                last_name,
                phone,
                email,
                password,
                referral_code,
                'pending',
                0.50,  # Default commission for freelancers
                1.00,  # Default commission for clients
                0.00   # Initial total earnings
            ))

            conn.commit()
            return jsonify({
                'success': True,
                'message': 'Registration successful! Your application is under review.'
            })

        except mysql.connector.Error as err:
            conn.rollback()
            print(f"Database Error: {err}")
            return jsonify({
                'success': False,
                'error': 'Database error occurred'
            }), 500

        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Registration failed'
        }), 500

# Update the affiliate_login route to check status
@app.route('/affiliate_login', methods=['POST'])
def affiliate_login():
    try:
        email = request.form.get('email')
        password = request.form.get('password')

        if not email or not password:
            return jsonify({
                'success': False,
                'error': 'Email and password are required'
            }), 400

        conn = get_db_connection()
        if conn is None:
            return jsonify({
                'success': False,
                'error': 'Database connection failed'
            }), 500

        cursor = conn.cursor(dictionary=True)

        try:
            cursor.execute("""
                SELECT id, first_name, last_name, email, status, referral_code
                FROM affiliates
                WHERE email = %s AND password = %s
            """, (email, password))

            affiliate = cursor.fetchone()

            if not affiliate:
                return jsonify({
                    'success': False,
                    'error': 'Invalid email or password'
                }), 401

            if affiliate['status'] != 'approved':
                return jsonify({
                    'success': False,
                    'error': 'Your account is pending approval'
                }), 403

            # Create session
            session['affiliate_id'] = affiliate['id']
            session['affiliate_name'] = f"{affiliate['first_name']} {affiliate['last_name']}"
            session['affiliate_email'] = affiliate['email']
            session['affiliate_code'] = affiliate['referral_code']

            return jsonify({
                'success': True,
                'redirect': '/affiliate_dashboard'
            })

        except Exception as e:
            print(f"Login error: {str(e)}")
            return jsonify({
                'success': False,
                'error': 'An error occurred during login'
            }), 500

        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        print(f"Error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'An unexpected error occurred'
        }), 500

# Add route to update affiliate status
@app.route('/update_affiliate_status', methods=['POST'])
def update_affiliate_status():
    if 'user_id' not in session or session.get('user_type') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    data = request.json
    affiliate_id = data.get('affiliate_id')
    status = data.get('status')

    if not affiliate_id or not status:
        return jsonify({'error': 'Missing affiliate_id or status'}), 400

    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)

    try:
        # Update the status in the affiliates table
        cursor.execute("UPDATE affiliates SET status = %s WHERE id = %s",
                     (status, affiliate_id))

        conn.commit()
        return jsonify({'success': True})

    except mysql.connector.Error as err:
        print(f"Database Error: {err}")
        conn.rollback()
        return jsonify({'error': str(err)}), 500

    except Exception as e:
        print(f"Error: {e}")
        conn.rollback()
        return jsonify({'error': str(e)}), 500

    finally:
        cursor.close()
        conn.close()

# Add route to get affiliate details
@app.route('/get_affiliate_details/<int:id>')
def get_affiliate_details(id):
    if 'user_id' not in session or session.get('user_type') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)

    try:
        cursor.execute("""
            SELECT
                id,
                first_name,
                last_name,
                email,
                phone,
                referral_code,
                status,
                commission_rate_freelancer,
                commission_rate_client,
                total_earnings,
                DATE_FORMAT(created_at, '%Y-%m-%d %H:%i') as created_at
            FROM affiliates
            WHERE id = %s
        """, (id,))

        affiliate = cursor.fetchone()

        if affiliate:
            # Handle binary data separately if needed
            cursor.execute("SELECT profile_photo FROM affiliates WHERE id = %s", (id,))
            binary_data = cursor.fetchone()

            # Convert binary data to base64 if exists
            if binary_data and binary_data.get('profile_photo'):
                affiliate['profile_photo'] = f"data:image/jpeg;base64,{base64.b64encode(binary_data['profile_photo']).decode()}"

            return jsonify(affiliate)

        return jsonify({'error': 'Affiliate not found'}), 404

    except mysql.connector.Error as err:
        print(f"Database Error: {err}")
        return jsonify({'error': 'Database error occurred'}), 500

    finally:
        cursor.close()
        conn.close()

def generate_referral_code(first_name, last_name):
    """Generate a unique referral code based on name and timestamp"""
    import random
    import string
    import time

    # Get first 2 letters of first name and last name (or less if names are shorter)
    first_prefix = first_name[:2].upper() if len(first_name) >= 2 else first_name.upper()
    last_prefix = last_name[:2].upper() if len(last_name) >= 2 else last_name.upper()

    # Add timestamp and random characters
    timestamp = str(int(time.time()))[-4:]
    random_chars = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))

    # Combine to create referral code
    referral_code = f"{first_prefix}{last_prefix}{timestamp}{random_chars}"

    return referral_code

# News and Events functions
@app.route('/admin/news/create', methods=['POST'])
def create_news():
    if request.method == 'POST':
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Get form data
            title = request.form.get('title')
            content = request.form.get('content')
            image = request.files.get('image')
            event_date = request.form.get('event_date')
            category = request.form.get('category')

            # Handle image upload if provided
            image_path = None
            if image and image.filename:
                filename = secure_filename(image.filename)
                image_path = f'static/uploads/news/{filename}'
                image.save(os.path.join(app.root_path, image_path))

            # Insert into database
            sql = """
            INSERT INTO news_events
            (title, content, image_path, event_date, author_id, category)
            VALUES (%s, %s, %s, %s, %s, %s)
            """

            author_id = session.get('user_id', None)
            cursor.execute(sql, (title, content, image_path, event_date, author_id, category))
            conn.commit()

            return jsonify({
                'success': True,
                'message': 'News/event created successfully'
            })

        except mysql.connector.Error as err:
            if conn:
                conn.rollback()
            print(f"Database Error: {err}")
            return jsonify({
                'success': False,
                'error': f'Database error: {str(err)}'
            }), 500

        finally:
            if 'cursor' in locals():
                cursor.close()
            if conn:
                conn.close()

    return render_template('admin_news_create.html')

@app.route('/admin/news/delete/<int:news_id>', methods=['POST'])
def delete_news(news_id):
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Delete the news/event
        cursor.execute("DELETE FROM news_events WHERE id = %s", (news_id,))
        conn.commit()

        return jsonify({
            'success': True,
            'message': 'News/event deleted successfully'
        })

    except mysql.connector.Error as err:
        if conn:
            conn.rollback()
        return jsonify({
            'success': False,
            'error': f'Database error: {str(err)}'
        }), 500

    finally:
        if 'cursor' in locals():
            cursor.close()
        if conn:
            conn.close()

@app.route('/create_post', methods=['POST'])
def create_post():
    if request.method == 'POST':
        conn = None
        cursor = None
        try:
            # Debug print to see what's being received
            print("Form data received:", request.form)
            print("Files received:", list(request.files.keys()))

            # Validate required fields
            title = request.form.get('title')
            content = request.form.get('content')
            category = request.form.get('category')
            audience = request.form.get('audience')

            if not title or not content:
                return jsonify({
                    'success': False,
                    'error': 'Title and content are required'
                }), 400

            print(f"Processing post: {title}, {category}, {audience}")

            # Get database connection
            conn = get_db_connection()
            if conn is None:
                print("Database connection failed")
                return jsonify({
                    'success': False,
                    'error': 'Database connection failed'
                }), 500

            cursor = conn.cursor()

            # Handle image upload
            image_path = None
            if 'image' in request.files:
                image = request.files['image']
                if image.filename != '':
                    try:
                        filename = secure_filename(image.filename)
                        # Make sure the upload directory exists
                        upload_dir = os.path.join(app.root_path, 'static/uploads/posts')
                        if not os.path.exists(upload_dir):
                            os.makedirs(upload_dir)

                        image_path = f'static/uploads/posts/{filename}'
                        full_path = os.path.join(app.root_path, image_path)
                        image.save(full_path)
                        print(f"Image saved to {full_path}")
                    except Exception as img_err:
                        print(f"Image upload error: {str(img_err)}")
                        return jsonify({
                            'success': False,
                            'error': f'Image upload failed: {str(img_err)}'
                        }), 500

            # Get author info from session if available
            author_id = session.get('user_id', None)
            print(f"Author ID: {author_id}")

            # Insert into database
            sql = """
            INSERT INTO news_events
            (title, content, image_path, category, audience, author_id, is_published)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """

            data = (title, content, image_path, category, audience, author_id, 1)
            print(f"SQL data: {data}")

            cursor.execute(sql, data)
            conn.commit()

            return jsonify({
                'success': True,
                'message': 'Post created successfully'
            })

        except mysql.connector.Error as err:
            print(f"Database Error: {err}")
            if conn:
                conn.rollback()
            return jsonify({
                'success': False,
                'error': f'Database error: {str(err)}'
            }), 500

        except Exception as e:
            print(f"Unexpected error: {str(e)}")
            if conn:
                conn.rollback()
            return jsonify({
                'success': False,
                'error': f'Unexpected error: {str(e)}'
            }), 500

        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

    return jsonify({
        'success': False,
        'error': 'Invalid request method'
    }), 405

# Add this function to check if the news_events table exists
def ensure_news_events_table():
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if table exists
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = DATABASE()
            AND table_name = 'news_events'
        """)

        if cursor.fetchone()[0] == 0:
            # Table doesn't exist, create it
            cursor.execute("""
                CREATE TABLE news_events (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    title VARCHAR(255) NOT NULL,
                    content TEXT NOT NULL,
                    image_path VARCHAR(255),
                    event_date DATETIME,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    is_published TINYINT(1) DEFAULT 1,
                    author_id INT,
                    category VARCHAR(50),
                    audience VARCHAR(50)
                )
            """)
            conn.commit()
            print("Created news_events table")
        else:
            # Check if audience column exists
            cursor.execute("""
                SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_schema = DATABASE()
                AND table_name = 'news_events'
                AND column_name = 'audience'
            """)

            if cursor.fetchone()[0] == 0:
                # Add audience column
                cursor.execute("""
                    ALTER TABLE news_events
                    ADD COLUMN audience VARCHAR(50) AFTER category
                """)
                conn.commit()
                print("Added audience column to news_events table")

        print("news_events table is ready")

    except mysql.connector.Error as err:
        print(f"Database Error during table check: {err}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if conn:
            conn.close()

# Add LinkedIn ID columns to client tables
@app.route('/add_linkedin_columns', methods=['GET'])
def add_linkedin_columns():
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'Database connection failed'}), 500

        cursor = conn.cursor()

        # Add linkedin_id column to register_client table if it doesn't exist
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.columns
            WHERE table_schema = DATABASE()
            AND table_name = 'register_client'
            AND column_name = 'linkedin_id'
        """)

        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                ALTER TABLE register_client
                ADD COLUMN linkedin_id VARCHAR(255) AFTER status
            """)
            print("Added linkedin_id column to register_client table")

        # Add linkedin_id column to approve_client table if it doesn't exist
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.columns
            WHERE table_schema = DATABASE()
            AND table_name = 'approve_client'
            AND column_name = 'linkedin_id'
        """)

        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                ALTER TABLE approve_client
                ADD COLUMN linkedin_id VARCHAR(255) AFTER status
            """)
            print("Added linkedin_id column to approve_client table")

        conn.commit()
        return jsonify({'message': 'LinkedIn ID columns added successfully'})

    except mysql.connector.Error as err:
        print(f"Database Error: {err}")
        return jsonify({'error': f'Database error: {str(err)}'}), 500

    finally:
        if 'cursor' in locals():
            cursor.close()
        if conn:
            conn.close()

# VIEW JOB DETAILS ROUTE
@app.route('/view_job/<int:job_id>')
def view_job(job_id):
    if 'user_id' not in session:
        return redirect(url_for('landing_page'))

    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor(dictionary=True)

        # Get job details
        cursor.execute("""
            SELECT
                j.*,
                DATE_FORMAT(j.created_at, '%b %d, %Y') as created_at_formatted
            FROM job_submissions j
            WHERE j.id = %s
        """, (job_id,))

        job = cursor.fetchone()

        if not job:
            return redirect(url_for('genius_page'))

        # Get client details
        if job['client_id']:
            cursor.execute("""
                SELECT
                    id,
                    profile_photo,
                    first_name,
                    last_name,
                    business_name,
                    country,
                    industry
                FROM approve_client
                WHERE id = %s
            """, (job['client_id'],))

            client = cursor.fetchone()

            if client and client['profile_photo']:
                client['profile_photo_url'] = f"data:image/jpeg;base64,{base64.b64encode(client['profile_photo']).decode()}"
            else:
                client['profile_photo_url'] = url_for('static', filename='img/default_profile.png')
        else:
            client = None

        # Get current user type
        user_type = session.get('user_type')

        return render_template('view_job.html', job=job, client=client, user_type=user_type)

    except mysql.connector.Error as err:
        print(f"Database Error: {err}")
        return redirect(url_for('genius_page'))

    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

# MARKETPLACE ROUTE
@app.route('/marketplace')
def marketplace():
    return render_template('marketplace.html')

# PAYROLL SERVICES ROUTE
@app.route('/payroll_services')
def payroll_services():
    return render_template('payroll_services.html')

# SERVICE CATALOG ROUTE
@app.route('/service_catalog')
def service_catalog():
    return render_template('service_catalog.html')

# BUSINESS NETWORKING ROUTE
@app.route('/business_networking')
def business_networking():
    return render_template('business_networking.html')

# LINKEDIN OAUTH ROUTES
@app.route('/auth/linkedin')
def auth_linkedin():
    """Initiate the LinkedIn OAuth flow"""
    # Generate a random state parameter to prevent CSRF attacks
    state = secrets.token_hex(16)
    session['linkedin_oauth_state'] = state

    # Get the appropriate redirect URI based on environment
    redirect_uri = get_linkedin_redirect_uri()

    # Print debug information
    print(f"LinkedIn OAuth initiated with state: {state}")
    print(f"LinkedIn Client ID: {LINKEDIN_CLIENT_ID}")
    print(f"LinkedIn Redirect URI: {redirect_uri}")
    print(f"Is Production: {is_production()}")

    # Construct the authorization URL with only the authorized scopes
    auth_url = f"https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id={LINKEDIN_CLIENT_ID}&redirect_uri={redirect_uri}&state={state}&scope=openid%20profile%20email%20w_member_social"

    print(f"Redirecting to: {auth_url}")
    return redirect(auth_url)

@app.route('/api/1.1/oauth_redirect')
def linkedin_callback():
    """Handle the LinkedIn OAuth callback"""
    # Check for error parameters
    error = request.args.get('error')
    error_description = request.args.get('error_description')

    if error:
        print(f"LinkedIn OAuth error: {error}, Description: {error_description}")
        flash(f"LinkedIn login failed: {error_description}", "error")
        return redirect(url_for('landing_page'))

    # Verify state parameter to prevent CSRF attacks
    if 'linkedin_oauth_state' not in session or request.args.get('state') != session.get('linkedin_oauth_state'):
        print("Invalid state parameter")
        flash("LinkedIn login failed: Security verification failed", "error")
        return redirect(url_for('landing_page'))

    # Clear the state from session
    session.pop('linkedin_oauth_state', None)

    # Get the authorization code
    code = request.args.get('code')
    if not code:
        print("Authorization code not provided")
        flash("LinkedIn login failed: Missing authorization code", "error")
        return redirect(url_for('landing_page'))

    # Exchange the authorization code for an access token
    token_url = 'https://www.linkedin.com/oauth/v2/accessToken'

    # Get the appropriate redirect URI based on environment
    redirect_uri = get_linkedin_redirect_uri()

    token_payload = {
        'grant_type': 'authorization_code',
        'code': code,
        'redirect_uri': redirect_uri,
        'client_id': LINKEDIN_CLIENT_ID,
        'client_secret': LINKEDIN_CLIENT_SECRET
    }

    print(f"Using redirect URI for token exchange: {redirect_uri}")

    print(f"Exchanging code for token with payload: {token_payload}")
    token_response = requests.post(token_url, data=token_payload)

    if token_response.status_code != 200:
        print(f"Token exchange error: {token_response.status_code}, {token_response.text}")
        return jsonify({'error': 'Failed to obtain access token'}), 400

    token_data = token_response.json()
    print(f"Token data: {token_data}")
    access_token = token_data.get('access_token')

    # Get user information from the ID token
    try:
        # For OpenID Connect, we can get user info from the userinfo endpoint
        userinfo_url = 'https://api.linkedin.com/v2/userinfo'
        headers = {
            'Authorization': f'Bearer {access_token}'
        }

        print(f"Fetching user info with token: {access_token}")
        userinfo_response = requests.get(userinfo_url, headers=headers)

        if userinfo_response.status_code != 200:
            print(f"User info fetch error: {userinfo_response.status_code}, {userinfo_response.text}")

            # Fallback to the /me endpoint
            profile_url = 'https://api.linkedin.com/v2/me'
            headers['X-RestLi-Protocol-Version'] = '2.0.0'

            profile_response = requests.get(profile_url, headers=headers)

            if profile_response.status_code != 200:
                print(f"Profile fetch error: {profile_response.status_code}, {profile_response.text}")
                return jsonify({'error': 'Failed to fetch profile data'}), 400

            profile_data = profile_response.json()
            print(f"Profile data: {profile_data}")

            # Try to get email separately
            email_url = 'https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))'
            email_response = requests.get(email_url, headers=headers)

            if email_response.status_code == 200:
                email_data = email_response.json()
                print(f"Email data: {email_data}")
                try:
                    email = email_data.get('elements', [{}])[0].get('handle~', {}).get('emailAddress', '')
                except Exception as e:
                    print(f"Error extracting email: {str(e)}")
                    email = ""
            else:
                print(f"Email fetch error: {email_response.status_code}, {email_response.text}")
                email = ""

            # Extract name from profile data
            first_name = profile_data.get('localizedFirstName', '')
            last_name = profile_data.get('localizedLastName', '')
        else:
            # Successfully got user info from userinfo endpoint
            userinfo_data = userinfo_response.json()
            print(f"User info data: {userinfo_data}")

            # Extract data from userinfo response
            email = userinfo_data.get('email', '')
            name_parts = userinfo_data.get('name', '').split()
            first_name = name_parts[0] if name_parts else ''
            last_name = ' '.join(name_parts[1:]) if len(name_parts) > 1 else ''

            # Use given_name and family_name if available
            first_name = userinfo_data.get('given_name', first_name)
            last_name = userinfo_data.get('family_name', last_name)

            # Set profile_data for consistency with the rest of the code
            profile_data = userinfo_data
    except Exception as e:
        print(f"Error processing user data: {str(e)}")
        return jsonify({'error': f'Error processing user data: {str(e)}'}), 500

    # Process user data - first_name and last_name are already set in the try block above
    linkedin_id = profile_data.get('id', '')

    # Check if user exists in database
    conn = get_db_connection()
    if conn is None:
        return jsonify({'error': 'Database connection failed'}), 500

    cursor = conn.cursor(dictionary=True)

    try:
        # First check if user exists in approve_genius table
        cursor.execute("""
            SELECT id, email, first_name, last_name
            FROM approve_genius
            WHERE email = %s
        """, (email,))

        genius = cursor.fetchone()

        if genius:
            # User exists as a genius, log them in
            session.clear()
            session['user_id'] = genius['id']
            session['user_type'] = 'genius'
            session['email'] = genius['email']
            session['name'] = f"{genius['first_name']} {genius['last_name']}"
            session.permanent = True

            return redirect(url_for('genius_page'))

        # If not in approve_genius, check approve_client table
        cursor.execute("""
            SELECT id, work_email, first_name, last_name
            FROM approve_client
            WHERE work_email = %s
        """, (email,))

        client = cursor.fetchone()

        if client:
            # User exists as a client, log them in
            session.clear()
            session['user_id'] = client['id']
            session['user_type'] = 'client'
            session['email'] = client['work_email']
            session['name'] = f"{client['first_name']} {client['last_name']}"
            session.permanent = True

            return redirect(url_for('client_page'))

        # If user doesn't exist, store their LinkedIn data in session and redirect to registration
        session['linkedin_data'] = {
            'email': email,
            'first_name': first_name,
            'last_name': last_name,
            'linkedin_id': linkedin_id
        }

        # Redirect to registration page with a query parameter to indicate LinkedIn signup
        return redirect(url_for('client_registration', source='linkedin'))

    except mysql.connector.Error as err:
        print(f"Database Error: {err}")
        return jsonify({'error': 'Database error occurred'}), 500

    finally:
        cursor.close()
        conn.close()

# Call this function to ensure the LinkedIn columns exist
ensure_news_events_table()

# Create a function to ensure all required tables and columns exist
def ensure_database_structure():
    # Add LinkedIn columns to client tables
    try:
        conn = get_db_connection()
        if conn is None:
            print("Database connection failed when checking LinkedIn columns")
            return

        cursor = conn.cursor()

        # Add linkedin_id column to register_client table if it doesn't exist
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.columns
            WHERE table_schema = DATABASE()
            AND table_name = 'register_client'
            AND column_name = 'linkedin_id'
        """)

        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                ALTER TABLE register_client
                ADD COLUMN linkedin_id VARCHAR(255)
            """)
            print("Added linkedin_id column to register_client table")

        # Add linkedin_id column to approve_client table if it doesn't exist
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.columns
            WHERE table_schema = DATABASE()
            AND table_name = 'approve_client'
            AND column_name = 'linkedin_id'
        """)

        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                ALTER TABLE approve_client
                ADD COLUMN linkedin_id VARCHAR(255)
            """)
            print("Added linkedin_id column to approve_client table")

        conn.commit()
        print("Database structure check completed successfully")

    except mysql.connector.Error as err:
        print(f"Database Error during structure check: {err}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if conn:
            conn.close()

# Call the function to ensure database structure
ensure_database_structure()

if __name__ == '__main__':
    app.run(debug=True)