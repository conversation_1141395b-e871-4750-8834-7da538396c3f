-- Drop the user if it exists
DROP USER IF EXISTS 'giguser'@'localhost';
DROP USER IF EXISTS 'giguser'@'%';

-- Create the user with the mysql_native_password authentication method
CREATE USER 'giguser'@'localhost' IDENTIFIED WITH mysql_native_password BY 'Happiness1524!';
CREATE USER 'giguser'@'%' IDENTIFIED WITH mysql_native_password BY 'Happiness1524!';

-- Grant privileges
GRANT ALL PRIVILEGES ON giggenius.* TO 'giguser'@'localhost';
GRANT ALL PRIVILEGES ON giggenius.* TO 'giguser'@'%';

-- Flush privileges
FLUSH PRIVILEGES;

-- Verify the users
SELECT user, host, plugin FROM mysql.user WHERE user = 'giguser';

-- Show grants
SHOW GRANTS FOR 'giguser'@'localhost';
SHOW GRANTS FOR 'giguser'@'%';
