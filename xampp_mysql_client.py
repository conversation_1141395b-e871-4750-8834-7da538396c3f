import subprocess
import sys
import os

# Path to XAMPP's MySQL client
mysql_client = "C:\\xampp\\mysql\\bin\\mysql.exe"

def try_connection(user, password, description):
    print(f"\n=== Trying {description} ===")
    print(f"User: {user}, Password: {'*****' if password else '(empty)'}")
    
    # Build command
    cmd = [mysql_client, f"-u{user}"]
    if password:
        cmd.append(f"-p{password}")
    
    cmd.extend(["-e", "SELECT 'Connection successful!' AS Result;"])
    
    try:
        print("Executing command:", " ".join(cmd))
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ SUCCESS!")
            print(result.stdout)
            
            # Try to show databases
            print("\nListing databases...")
            db_cmd = [mysql_client, f"-u{user}"]
            if password:
                db_cmd.append(f"-p{password}")
            
            db_cmd.extend(["-e", "SHOW DATABASES;"])
            
            db_result = subprocess.run(db_cmd, capture_output=True, text=True)
            if db_result.returncode == 0:
                print(db_result.stdout)
                
                # Check if giggenius database exists
                if "giggenius" in db_result.stdout.lower():
                    print("\n✅ 'giggenius' database exists!")
                else:
                    print("\n❌ 'giggenius' database does not exist")
                    
                    # Ask if we should create the database
                    create_db = input("Would you like to create the giggenius database? (y/n): ")
                    if create_db.lower() == 'y':
                        create_cmd = [mysql_client, f"-u{user}"]
                        if password:
                            create_cmd.append(f"-p{password}")
                        
                        create_cmd.extend(["-e", "CREATE DATABASE giggenius;"])
                        
                        create_result = subprocess.run(create_cmd, capture_output=True, text=True)
                        if create_result.returncode == 0:
                            print("✅ 'giggenius' database created successfully!")
                        else:
                            print(f"❌ Error creating database: {create_result.stderr}")
            else:
                print(f"❌ Error listing databases: {db_result.stderr}")
            
            return True
        else:
            print(f"❌ FAILED: {result.stderr}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    return False

# Check if XAMPP's MySQL client exists
if not os.path.exists(mysql_client):
    print(f"❌ MySQL client not found at: {mysql_client}")
    print("Please make sure XAMPP is installed correctly.")
    sys.exit(1)

# Try different user/password combinations
credentials = [
    {"user": "root", "password": "", "description": "root with empty password (XAMPP default)"},
    {"user": "root", "password": "Happiness1524!", "description": "root with Happiness1524!"},
    {"user": "root", "password": "root", "description": "root with 'root' password"},
    {"user": "root", "password": "password", "description": "root with 'password'"},
    {"user": "root", "password": "admin", "description": "root with 'admin'"},
    {"user": "giguser", "password": "Happiness1524!", "description": "giguser with Happiness1524!"}
]

success = False
for cred in credentials:
    if try_connection(cred["user"], cred["password"], cred["description"]):
        print(f"\n✅ SUCCESS with {cred['description']}")
        print("Use these credentials in your application configuration.")
        success = True
        break

if not success:
    print("\n❌ All connection attempts failed.")
    print("\nSuggestions:")
    print("1. Check if MySQL is running in XAMPP Control Panel")
    print("2. Try accessing phpMyAdmin to verify credentials")
    print("3. Check MySQL error logs for authentication issues")
    print("4. Consider resetting the root password")

sys.exit(0 if success else 1)
