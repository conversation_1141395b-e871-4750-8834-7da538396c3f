-- Drop the user if it exists to start fresh
DROP USER IF EXISTS 'GigGenius'@'localhost';

-- Create the user with the correct password
CREATE USER 'GigGenius'@'localhost' IDENTIFIED BY 'Happiness1524!';

-- Grant all privileges on the giggenius database to the user
GRANT ALL PRIVILEGES ON giggenius.* TO 'GigGenius'@'localhost';

-- Flush privileges to apply changes
FLUSH PRIVILEGES;

-- Show the user to verify
SELECT User, Host FROM mysql.user WHERE User = 'GigGenius';

-- Show the grants for the user to verify
SHOW GRANTS FOR 'GigGenius'@'localhost';
