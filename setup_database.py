import mysql.connector
import sys

# Database configuration
db_config = {
    'host': 'localhost',
    'user': 'giguser',
    'password': 'Happiness1524!',
    'database': 'giggenius'
}

# SQL to create the genius table
create_genius_table = """
CREATE TABLE IF NOT EXISTS genius (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VA<PERSON>HAR(50) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    profile_photo VARCHAR(255),
    expertise_level VARCHAR(50),
    country VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
"""

# SQL to create the clients table
create_clients_table = """
CREATE TABLE IF NOT EXISTS clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    work_email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    profile_photo VARCHAR(255),
    position VARCHAR(100),
    country VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
"""

def setup_database():
    try:
        print("Attempting to connect to MySQL database...")
        conn = mysql.connector.connect(**db_config)
        
        if conn.is_connected():
            print("✅ Successfully connected to MySQL database!")
            
            cursor = conn.cursor()
            
            # Create tables
            print("\nCreating tables if they don't exist...")
            
            print("Creating genius table...")
            cursor.execute(create_genius_table)
            print("✅ Genius table created or already exists")
            
            print("Creating clients table...")
            cursor.execute(create_clients_table)
            print("✅ Clients table created or already exists")
            
            # Check tables
            print("\nVerifying tables...")
            cursor.execute("SHOW TABLES;")
            tables = cursor.fetchall()
            
            print("Tables in the database:")
            for table in tables:
                print(f"- {table[0]}")
                
                # Show table structure
                print(f"\nStructure of {table[0]} table:")
                cursor.execute(f"DESCRIBE {table[0]};")
                columns = cursor.fetchall()
                for column in columns:
                    print(f"  - {column[0]}: {column[1]}")
            
            cursor.close()
            conn.close()
            print("\nDatabase setup completed successfully!")
            return True
            
    except mysql.connector.Error as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = setup_database()
    sys.exit(0 if success else 1)
