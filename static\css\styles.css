body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0 20px;
}

.logo h2 {
    margin: 0;
    color: #fff;
}

.profile-container {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #fff;
}

.profile-photo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.profile-placeholder {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #555;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

header {
    background-color: #333;
    color: #fff;
    padding: 1em;
    text-align: center;
}

header nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: space-between;
}

header nav ul li {
    margin-right: 20px;
}

header nav a {
    color: #fff;
    text-decoration: none;
}

main {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2em;
}

.hero {
    background-image: linear-gradient(to bottom, #333, #555);
    color: #fff;
    padding: 2em;
    text-align: center;
}

.hero h1 {
    font-size: 36px;
    margin-bottom: 10px;
}

.features {
    background-color: #f7f7f7;
    padding: 2em;
}

.features ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.features li {
    margin-bottom: 10px;
}

.call-to-action {
    background-color: #333;
    color: #fff;
    padding: 2em;
    text-align: center;
}

.call-to-action button {
    background-color: #555;
    color: #fff;
    border: none;
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
}

footer {
    background-color: #333;
    color: #fff;
    padding: 1em;
    text-align: center;
    clear: both;
}
