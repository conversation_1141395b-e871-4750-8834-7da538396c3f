import mysql.connector
import sys
import socket
import subprocess
import platform

def check_mysql_running():
    """Check if MySQL server is running by attempting to connect to port 3306"""
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('127.0.0.1', 3306))
    sock.close()
    return result == 0

def get_mysql_service_status():
    """Get MySQL service status using system commands"""
    system = platform.system()
    if system == "Windows":
        try:
            # Check MySQL service status on Windows
            result = subprocess.run(['sc', 'query', 'mysql'], capture_output=True, text=True)
            return result.stdout
        except Exception as e:
            return f"Error checking MySQL service: {e}"
    elif system == "Linux":
        try:
            # Check MySQL service status on Linux
            result = subprocess.run(['systemctl', 'status', 'mysql'], capture_output=True, text=True)
            return result.stdout
        except Exception as e:
            return f"Error checking MySQL service: {e}"
    else:
        return "Unsupported operating system for service check"

# Common MySQL configurations to try
configurations = [
    {
        'name': 'app.py (empty password)',
        'config': {
            'host': 'localhost',
            'user': 'root',
            'password': '',
            'database': 'giggenius'
        }
    },
    {
        'name': 'provided (with password)',
        'config': {
            'host': 'localhost',
            'user': 'root',
            'password': 'Happiness1524!',
            'database': 'giggenius'
        }
    },
    {
        'name': 'common default',
        'config': {
            'host': 'localhost',
            'user': 'root',
            'password': 'root',
            'database': 'giggenius'
        }
    },
    {
        'name': 'without database',
        'config': {
            'host': 'localhost',
            'user': 'root',
            'password': '',
        }
    },
    {
        'name': 'without database (with password)',
        'config': {
            'host': 'localhost',
            'user': 'root',
            'password': 'Happiness1524!',
        }
    },
    {
        'name': 'localhost IP',
        'config': {
            'host': '127.0.0.1',
            'user': 'root',
            'password': '',
            'database': 'giggenius'
        }
    },
    {
        'name': 'localhost IP (with password)',
        'config': {
            'host': '127.0.0.1',
            'user': 'root',
            'password': 'Happiness1524!',
            'database': 'giggenius'
        }
    }
]

def test_connection(config, name):
    """Test database connection with given configuration"""
    print(f"\nTesting connection with {name} configuration...")
    print(f"Configuration: {config}")

    try:
        connection = mysql.connector.connect(**config)

        if connection.is_connected():
            db_info = connection.get_server_info()
            cursor = connection.cursor()

            # Check if we're connected to a specific database
            if 'database' in config:
                cursor.execute("SELECT DATABASE();")
                db_name = cursor.fetchone()[0]
                print(f"✅ Connected to MySQL Server version {db_info}")
                print(f"✅ Connected to database: {db_name}")

                # Test a simple query to verify full functionality
                try:
                    cursor.execute("SHOW TABLES;")
                    tables = cursor.fetchall()
                    print(f"✅ Database contains {len(tables)} tables")
                    if tables:
                        print("Tables in database:")
                        for table in tables:
                            print(f"  - {table[0]}")
                except mysql.connector.Error as err:
                    print(f"❌ Query error: {err}")
            else:
                print(f"✅ Connected to MySQL Server version {db_info} (no specific database)")

                # List available databases
                try:
                    cursor.execute("SHOW DATABASES;")
                    databases = cursor.fetchall()
                    print(f"✅ Server has {len(databases)} databases")
                    print("Available databases:")
                    for db in databases:
                        print(f"  - {db[0]}")
                        if db[0].lower() == 'giggenius':
                            print(f"    ✅ Found 'giggenius' database!")
                except mysql.connector.Error as err:
                    print(f"❌ Query error: {err}")

            cursor.close()
            connection.close()
            print("✅ Connection closed successfully")
            return True

    except mysql.connector.Error as err:
        print(f"❌ Error: {err}")
        if hasattr(mysql.connector, 'errorcode'):
            if err.errno == mysql.connector.errorcode.ER_ACCESS_DENIED_ERROR:
                print("   This may be due to incorrect username or password")
            elif err.errno == mysql.connector.errorcode.ER_BAD_DB_ERROR:
                print("   Database does not exist")
            else:
                print(f"   Error code: {err.errno}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

# Main execution
print("=== MySQL Database Connection Verification ===")

# First check if MySQL is running
print("\nChecking if MySQL server is running...")
if check_mysql_running():
    print("✅ MySQL server appears to be running (port 3306 is open)")
else:
    print("❌ MySQL server does not appear to be running (port 3306 is closed)")
    print("\nAttempting to get MySQL service status:")
    print(get_mysql_service_status())
    print("\n⚠️ Please make sure MySQL server is installed and running before continuing.")
    print("   On Windows: Start MySQL service from Services or use 'net start mysql'")
    print("   On Linux: Use 'sudo systemctl start mysql' or 'sudo service mysql start'")
    user_input = input("\nDo you want to continue testing connections anyway? (y/n): ")
    if user_input.lower() != 'y':
        sys.exit(1)

# Try all configurations
successful_configs = []
for config_info in configurations:
    if test_connection(config_info['config'], config_info['name']):
        successful_configs.append(config_info['name'])

# Summary
print("\n=== Summary ===")
if successful_configs:
    print(f"✅ Successfully connected with {len(successful_configs)} configuration(s):")
    for config_name in successful_configs:
        print(f"   - {config_name}")

    # Provide recommendations for app.py
    if 'app.py (empty password)' in successful_configs:
        print("\n✅ Your current app.py configuration works! No changes needed.")
    elif 'provided (with password)' in successful_configs:
        print("\n⚠️ Your app.py configuration needs to be updated with the correct password.")
        print("   Update the db_config in app.py to use 'Happiness1524!' as the password.")
    else:
        print("\n⚠️ Neither of your original configurations worked, but we found working alternatives.")
        print("   Please update your app.py with one of the working configurations.")
else:
    print("❌ All connection attempts failed. Please check your MySQL server and credentials.")
    print("\nPossible issues and solutions:")
    print("1. MySQL server is not running - Start the MySQL service")
    print("2. Incorrect credentials - Verify username and password")
    print("3. Database 'giggenius' doesn't exist - Create the database first")
    print("4. Network/firewall issues - Check if port 3306 is blocked")
    print("\nTo create the 'giggenius' database, connect to MySQL and run:")
    print("   CREATE DATABASE giggenius;")
