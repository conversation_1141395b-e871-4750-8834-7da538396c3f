import socket
import subprocess
import sys
import os

def check_port():
    print("=== Checking what's running on port 3306 ===")
    
    # Check if port 3306 is open
    try:
        print("\nChecking if port 3306 is open...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex(('127.0.0.1', 3306))
        sock.close()
        
        if result == 0:
            print("✅ Port 3306 is open (something is accepting connections)")
        else:
            print("❌ Port 3306 is closed (nothing is listening on this port)")
            return
    except Exception as e:
        print(f"Error checking port: {e}")
        return
    
    # Check what process is using port 3306
    try:
        print("\nChecking what process is using port 3306...")
        result = subprocess.run(["netstat", "-ano", "|", "findstr", ":3306"], 
                               shell=True, capture_output=True, text=True)
        
        if result.stdout:
            print("Process information:")
            print(result.stdout)
            
            # Extract PID
            lines = result.stdout.strip().split('\n')
            if lines:
                parts = lines[0].split()
                if len(parts) >= 5:
                    pid = parts[4]
                    print(f"Process ID (PID): {pid}")
                    
                    # Get process name
                    try:
                        process_info = subprocess.run(["tasklist", "/FI", f"PID eq {pid}"], 
                                                    capture_output=True, text=True)
                        print("\nProcess details:")
                        print(process_info.stdout)
                    except Exception as e:
                        print(f"Error getting process details: {e}")
        else:
            print("No process information found for port 3306")
    except Exception as e:
        print(f"Error checking process: {e}")
    
    # Try to connect to MySQL with socket
    try:
        print("\nTrying to connect to MySQL with a raw socket...")
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(2)
        s.connect(('127.0.0.1', 3306))
        
        # Receive the MySQL greeting
        data = s.recv(1024)
        print(f"Received {len(data)} bytes from the server")
        print(f"Raw data: {data}")
        
        # Try to decode the data
        try:
            decoded = data.decode('utf-8', errors='replace')
            print(f"Decoded data: {decoded}")
        except Exception as e:
            print(f"Error decoding data: {e}")
        
        s.close()
    except Exception as e:
        print(f"Error connecting with socket: {e}")
    
    print("\n=== Suggestions ===")
    print("1. Check if you're using XAMPP, WAMP, or a similar package")
    print("2. Look for MySQL configuration files (my.ini or my.cnf)")
    print("3. Try connecting with the MySQL client that came with your installation")
    print("4. Check the MySQL logs for authentication errors")

if __name__ == "__main__":
    check_port()
