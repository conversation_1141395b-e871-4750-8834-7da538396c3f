:root {
    --primary-color: #2563eb;
    --secondary-color: #f3f4f6;
    --border-color: #e5e7eb;
    --text-primary: #111827;
    --text-secondary: #6b7280;
}

/* First, let's set <PERSON>pins as the default font for everything */
* {
    font-family: 'Poppins', sans-serif;
}

/* Specific elements that need font-family explicitly set */
.message-text,
.message-time,
.option-item,
.chat-input,
.contact-name,
.contact-preview,
.chat-header-name,
.chat-header-status,
.file-name,
.file-size,
.reaction,
.reaction-count,
.search-box input,
.typing-indicator,
.message-meta,
button,
input,
textarea {
    font-family: 'Poppins', sans-serif;
}

/* Adjust font weights for better readability with Poppins */
.message-text {
    font-weight: 400;
    font-size: 0.95rem;
}

.message-time {
    font-weight: 400;
    font-size: 0.75rem;
}

.contact-name {
    font-weight: 500;
}

.chat-header-name {
    font-weight: 500;
}

.option-item {
    font-weight: 400;
}

body, html {
    height: 100%;
    overflow: hidden;
    background-color: #f9fafb;
    color: var(--text-primary);
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
}

input, button {
    font-family: 'Poppins', sans-serif;
}

.btn {
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
}

/* Navbar styling */
.navbar {
    background-color: white !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    padding: 0.75rem 1.5rem;
}

.navbar-brand {
    font-weight: 600;
    color: var(--primary-color) !important;
}

.chat-container {
    height: calc(100vh - 64px);
    display: flex;
    padding: 1rem;
    gap: 1rem;
    transition: padding-right 0.3s ease;
}

.chat-container.panel-active {
    padding-right: 400px;
}

.contacts-list {
    width: 320px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
}

.contact-item {
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.2s;
    cursor: pointer;
    border-bottom: 1px solid var(--border-color);
}

.contact-item:last-child {
    border-bottom: none;
}

.contact-item:hover {
    background-color: var(--secondary-color);
}

.contact-item.active {
    background-color: #eff6ff;
    border-left: 3px solid var(--primary-color);
}

.contact-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    flex-shrink: 0;
}

.contact-info {
    flex: 1;
    min-width: 0;
    overflow: hidden;
}

.contact-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
}

.contact-preview {
    color: var(--text-secondary);
    font-size: 0.875rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Add this for unread messages */
.contact-item.unread .contact-name,
.contact-item.unread .preview-text {
    font-weight: 700;
}

/* Styling for unread messages in contact list */
.contact-item.unread .contact-name {
    font-weight: 700;
    color: #000000;
}

.contact-item.unread .contact-preview {
    font-weight: 600;
    color: #000000;
}

/* Styling for unread messages and reactions */
.contact-item.unread .preview-text {
    font-weight: 700 !important;
    color: #000000 !important;
}

/* Specific styling for reaction messages */
.contact-item.unread .preview-text:has(div) {
    font-weight: 700 !important;
    color: #000000 !important;
}

/* Normal state */
.preview-text {
    color: var(--text-secondary);
    font-size: 0.875rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Hover effect for unread items */
.contact-item.unread:hover {
    background-color: rgba(37, 99, 235, 0.1);
}

/* Normal state (when read) */
.contact-name {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.contact-preview {
    color: var(--text-secondary);
    font-size: 0.875rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-area {
    flex: 1;
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-width: 0;
    transition: width 0.3s ease;
}

.contacts-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.contacts-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.search-box {
    position: relative;
    margin-top: 0.5rem;
}

.search-box .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    z-index: 1;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border-radius: 25px;
    border: 1px solid var(--border-color);
    background-color: var(--secondary-color);
    color: var(--text-primary);
    transition: all 0.2s;
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}

.search-box input::placeholder {
    color: var(--text-secondary);
}

.contacts-search {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.contacts-search input {
    border-radius: 25px;
    padding-left: 2.5rem;
    background: var(--secondary-color);
    border: none;
}

.contacts-body {
    overflow-y: auto;
    flex: 1;
}

.chat-header {
    padding: 0.5rem 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
}

.chat-header-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
}

.chat-header-info {
    flex: 1;
}

.chat-header-name {
    font-size: 0.7rem;  /* Adjusted to a balanced size */
    font-weight: 600;
    margin: 0;
    color: var(--text-primary);
}

.chat-header-status {
    font-size: 0.65rem;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 3px;
}

.chat-header-status i {
    font-size: 0.5rem;
}

.chat-header-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.chat-header-actions button {
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: 0.375rem;
    border-radius: 0.375rem;
    transition: all 0.2s;
}

.chat-header-actions button:hover {
    background-color: var(--secondary-color);
    color: var(--text-primary);
}

/* Chat Messages Container */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    background: linear-gradient(135deg, #f6f9fc 0%, #eef2f7 100%);
    position: relative;
    height: auto;
    min-height: 0;
}

/* Message container with avatar */
.message-container {
    position: relative;
    display: inline-block;
    max-width: 100%;
}

.message-content {
    display: inline-block;
    padding: 8px 12px;
    border-radius: 12px;
    position: relative;
}

.message-react-btn-wrapper {
    position: absolute;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
    pointer-events: none;
}

.message-sent .message-react-btn-wrapper {
    left: -40px;
}

.message-received .message-react-btn-wrapper {
    right: -40px;
}

/* Reaction Button Base Styles */
.message-react-btn {
    position: absolute;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: white;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Position for sent messages */
.message-sent .message-react-btn {
    left: -40px;
    top: 50%;
    transform: translateY(-50%);
}

/* Position for received messages */
.message-received .message-react-btn {
    right: -40px;
    top: 50%;
    transform: translateY(-50%);
}

.message:hover .message-react-btn {
    opacity: 1;
}

.message-react-btn:hover {
    background: #f3f4f6;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}









.message-sent {
    justify-content: flex-start; /* This keeps alignment consistent */
    margin-left: auto; /* Keeps messages on right side */
    width: fit-content; /* Important: prevents expanding too much */
    max-width: 80%; /* Limits maximum width */
}

.message-received {
    justify-content: flex-start;
    margin-right: auto; /* Force alignment to left */
}

.message-avatar {
    position: absolute;
    width: 32px;
    height: 32px;
    top: 50%;
    transform: translateY(-50%);
}

.message-sent .message-avatar {
    right: 0;
}

.message-received .message-avatar {
    left: 0;
}

.message-content {
    display: inline-block;
    max-width: 80%;
    min-width: 60px; /* Minimum width for very short messages */
    padding: 8px 12px;
    border-radius: 16px;
    margin: 0 12px !important;
    position: relative;
}

.message-sent .message-content {
    background: #2563eb; /* Sent message color */
    color: white;
    padding: 10px 15px; /* Consistent padding */
    border-radius: 16px; /* Rounded corners */
    margin-right: 12px; /* Space between messages */
    display: inline-block;
    float: right; /* Align to the right */
    clear: both; /* Clear floats */
    width: fit-content; /* Adjusted for content */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Added shadow */
}

.message-received .message-content {
    background: #f3f4f6; /* Received message color */
    padding: 10px 15px; /* Consistent padding */
    border-radius: 16px; /* Rounded corners */
    margin-left: 12px; /* Space between messages */
    display: inline-block;
    float: left; /* Align to the left */
    clear: both; /* Clear floats */
    width: fit-content; /* Adjusted for content */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Added shadow */
}
.message-sent .message-content,
.message-received .message-content {
    max-width: 100%; /* Limit the maximum width */
    overflow: hidden; /* Hide overflow text */
    text-overflow: ellipsis; /* Add ellipsis for overflow text */
    white-space: normal; /* Allow wrapping */
    overflow-wrap: break-word; /* Allow long words to break */
    word-break: break-word; /* Allow words to break */
}
.message-sent .message-text
.message-received .message-text {
    overflow-wrap: break-word; /* Allow proper word wrapping */
    white-space: normal; /* Allow wrapping */
    min-width: 100px; /* Set a minimum width for the message */
    max-width: 100%; /* Allow full width within the bubble */
}
/* KAPAG LONG MESSAGE NA */
.message-sent .message-text.wrapped {
    white-space: pre-line; /* RESPECT LINE BREAKS */
    text-wrap: pretty; /* GAWING MAGANDA BREAKS */
}

.message-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.message-content {
    background: #f3f4f6;
    border-radius: 16px;
    padding: 12px 16px;
    position: relative;
}

.message-sent .message-content {
    background: #2563eb;
}

.message-text {
    color: #1f2937; /* Text color */
    margin: 0; /* Remove default margin */
    font-size: 0.95rem; /* Font size */
    line-height: 1.4; /* Line height */
    white-space: normal; /* Allow wrapping */
    word-break: break-all; /* Allow breaking long words */
    max-width: 600px; /* Max width for text */
    font-family: 'Poppins', sans-serif; /* Font family */
    font-weight: 400; /* Font weight */
    overflow-wrap: break-word; /* Proper word breaking */
}
.message-sent .message-text {
    color: #ffffff;
}

.message-meta {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 4px;
    font-family: 'Poppins', sans-serif;
}

.message-time {
    font-size: 0.75rem;
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
}

/* For sent messages (blue bubble) */
.message-sent .message-time {
    color: rgba(255, 255, 255, 0.9);
}

/* For received messages */
.message-received .message-time {
    color: #6b7280;
}

/* Options and actions */
.message-actions {
    display: none;
    align-items: center;
    gap: 4px;
    position: absolute;
    top: -24px;
    right: 0;
    background: white;
    border-radius: 20px;
    padding: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    font-family: 'Poppins', sans-serif;
}

.message:hover .message-actions {
    display: flex;
}

.action-btn {
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    color: #6b7280;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.action-btn:hover {
    background: #f3f4f6;
    color: #2563eb;
}

.options-menu {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    padding: 4px 0;
    min-width: 140px;
}

.options-btn:focus + .options-menu,
.options-menu:hover {
    display: block;
}

.option-item {
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.9rem;
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
}

.option-item:hover {
    background: #f3f4f6;
}

.option-item i {
    font-size: 0.85rem;
    width: 16px;
}

.text-danger {
    color: #dc2626;
}

.text-danger:hover {
    background: #fef2f2;
}

.message-image {
    max-width: 300px;
    max-height: 300px;
    border-radius: 8px;
    cursor: pointer;
}

.message-image img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.3s ease;
}

.message-image:hover img {
    transform: scale(1.02);
}

.message-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(0deg, rgba(0,0,0,0.1) 0%, transparent 100%);
    pointer-events: none;
}



/* Modern Animations */
@keyframes messageSlide {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Message Groups with Modern Spacing */
.message-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.message-group .message:not(:last-child) .message-bubble {
    border-bottom-right-radius: 18px;
    border-bottom-left-radius: 18px;
}

/* Hover Effects */
.message:hover .message-bubble {
    transform: translateY(-1px);
}

.sent:hover .message-bubble {
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.25);
}

.received:hover .message-bubble {
    box-shadow: 0 6px 20px rgba(148, 163, 184, 0.15);
}

/* Modern Scrollbar */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.3);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: rgba(148, 163, 184, 0.5);
}

/* Message Reactions */
.message-reactions {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 4px;
}

.message-reaction {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.1);
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s;
}

.message-reaction:hover {
    transform: scale(1.05);
    background: rgba(0, 0, 0, 0.05);
}

.message-sent .message-reaction {
    background: rgba(37, 99, 235, 0.1);
    border-color: rgba(37, 99, 235, 0.2);
}

/* Reaction Menu */
.reaction-menu {
    display: none;
    position: absolute;
    bottom: 100%;
    left: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 8px;
    z-index: 1000;
    margin-bottom: 8px;
    border: 1px solid var(--border-color);
}

.quick-reactions {
    display: flex;
    gap: 12px;
    padding: 4px;
}

.plus-reaction-button {
    background: none;
    border: none;
    padding: 5px;
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.plus-reaction-button:hover {
    background-color: #f0f2f5;
}

.extended-emoji-list {
    max-height: 200px;
    overflow-y: auto;
    padding: 8px;
}

.emoji-category {
    margin-bottom: 10px;
}

.category-title {
    font-size: 12px;
    color: #65676B;
    margin-bottom: 5px;
    padding: 0 4px;
}

.emoji-option {
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    display: inline-flex;
    transition: background-color 0.2s;
}

.emoji-option:hover {
    background-color: #f0f2f5;
}

/* Message React Button */
.message-react-btn {
    opacity: 0;
    transition: opacity 0.2s ease;
    background: none;
    border: none;
    padding: 6px;
    cursor: pointer;
    color: var(--text-secondary);
    border-radius: 50%;
    position: absolute;
    right: -30px;
    top: 50%;
    transform: translateY(-50%);
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.message-sent .message-react-btn {
    right: auto;
    left: -30px;
}

.message:hover .message-react-btn {
    opacity: 1;
}

.message-react-btn:hover {
    background: var(--secondary-color);
    color: var(--primary-color);
}

/* Message container adjustments */
.message {
    position: relative;
    margin: 8px 0;
    display: flex;
    flex-direction: column;
    max-width: 70%;
}

.message-content-wrapper {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .message-react-btn {
        opacity: 1;
        width: 24px;
        height: 24px;
        right: -25px;
    }

    .message-sent .message-react-btn {
        left: -25px;
    }

    .quick-reactions span {
        font-size: 18px;
        width: 32px;
        height: 32px;
    }

    .reaction {
        padding: 3px 8px;
        font-size: 12px;
    }

    .reaction-count {
        font-size: 11px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .reaction {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.1);
    }

    .message-sent .reaction {
        background: rgba(37, 99, 235, 0.2);
        border-color: rgba(37, 99, 235, 0.3);
    }

    .reaction-menu {
        background: #1f2937;
        border-color: rgba(255, 255, 255, 0.1);
    }

    .quick-reactions span:hover {
        background: rgba(255, 255, 255, 0.1);
    }
}

/* Responsive design for reactions */
@media (max-width: 768px) {
    .reaction-button {
        width: 24px;
        height: 24px;
    }

    .reaction-option {
        font-size: 18px;
        padding: 4px;
    }

    .reaction {
        padding: 3px 8px;
        font-size: 13px;
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .typing-indicator {
        padding: 10px 12px;
        margin: 6px 12px;
    }
    
    .dots span {
        width: 6px;
        height: 6px;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .message {
        max-width: 85%;
    }

    .chat-messages {
        padding: 16px;
    }

    .message-image {
        max-width: 250px;
    }
}

/* New avatar profile for messages */
.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 8px;
}

.message-sent {
    margin-left: auto;
    flex-direction: row-reverse;
}

.message-received {
    margin-right: auto;
}

.message-content {
    padding: 12px 16px;
    border-radius: 16px;
    background: #f0f2f5;
    position: relative;
}

.message-sent .message-content {
    background: #0084ff;
    color: white;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.8;
    margin-top: 0.25rem;
    display: block;
}
.message-status {
    margin: 0 8px;
    font-size: 12px;
    color: #666;
}

.message-sent .message-status {
    color: #0084ff;
}

.error-message-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.error-message {
    background: #fee2e2;
    color: #dc2626;
    padding: 12px 20px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.reaction-bar {
    position: absolute;
    top: -28px;
    right: 0;
    background: white;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    padding: 2px 4px;
    display: none;
    z-index: 100;
    gap: 2px;
}

.message:hover .reaction-bar {
    display: flex;
}


.reaction-buttons {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 4px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s, visibility 0.2s;
}

.reaction-btn, .reply-btn, .more-btn {
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: 16px;
    color: rgba(136, 136, 136, 0.7);
    transition: color 0.3s ease, background-color 0.3s ease;
    padding: 6px;
    border-radius: 50%;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    display: flex;
    white-space: nowrap;
    overflow: hidden; /* Prevents scrollbars */
}

/* Button styling */
.reaction-btn,
.reply-btn,
.delete-btn {
    background: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 50%;
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #666;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.reaction-btn:hover, 
.reply-btn:hover {
    color: #4a90e2;
    background-color: #ffffff !important;
    border-color: #4a90e2 !important;
    transform: scale(1.1);
}

/* Special styling for delete button */
.delete-btn {
    color: #666;
    background: #ffffff !important;
}

.delete-btn:hover {
    color: #dc3545;
    background-color: #ffffff !important;
    border-color: #dc3545 !important;
    transform: scale(1.1);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .reaction-btn,
    .reply-btn,
    .delete-btn {
        background: #ffffff !important;
        color: #666;
        border-color: #e5e7eb !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    }

    .delete-btn:hover {
        color: #dc3545;
        background: #ffffff !important;
        border-color: #dc3545 !important;
    }

    .reaction-btn:hover,
    .reply-btn:hover {
        color: #4a90e2;
        background: #ffffff !important;
        border-color: #4a90e2 !important;
    }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .reaction-btn,
    .reply-btn,
    .delete-btn {
        width: 24px;
        height: 24px;
    }

    .reaction-btn i,
    .reply-btn i,
    .delete-btn i {
        font-size: 12px;
    }
}

.reaction-buttons {
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* This is for the actual emoji picker */
.emoji-picker {
    display: none; /* Hidden initially */
    position: absolute;
    bottom: 30px;
    right: 0; /* CHANGED FROM left: 0 to right: 0 */
    width: 200px; /* More compact width */
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 10000;
    padding: 6px; /* Reduced padding */
    overflow: hidden;
}

.emoji-option {
    font-size: 16px; /* Slightly smaller emojis */
    padding: 4px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
}

/* Ensure the reaction buttons container doesn't clip the emoji picker */
.reaction-buttons {
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 9999; /* High z-index but below emoji-picker */
}

/* Ensure messages don't overlap the picker */
.message {
    position: relative;
    z-index: 1; /* Keep messages below the reaction elements */
}

/* Make sure emoji picker stays on top */
.message .reaction-buttons:hover .emoji-picker {
    display: block;
}

/* Show the reaction buttons on hover */
.message:hover .reaction-buttons {
    opacity: 1;
    visibility: visible;
}
.emoji-option {
    font-size: 16px; /* Slightly smaller emojis */
    padding: 4px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.emoji-option:hover {
    background-color: #f0f2f5;
}


.message-reactions {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 4px;
    max-width: 100%;
}

.reaction {
    padding: 2px 6px;
    border-radius: 12px;
    background: white;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 3px;
    border: 1px solid var(--border-color);
    user-select: none;
    margin: 2px;
    white-space: nowrap;
}

.message-sent .reaction-menu {
    position: absolute; /* Position it relative to the message */
    bottom: 10px; /* Adjust as needed */
    left: 10px; /* Position on the left side for sent messages */
    background: white; /* Background color */
    border-radius: 10px; /* Rounded corners */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* Shadow for depth */
    display: flex; /* Use flexbox for layout */
    gap: 5px; /* Space between buttons */
    padding: 10px; /* Padding inside the menu */
    z-index: 10; /* Bring it to the front */
}

.message-received .reaction-menu {
    position: absolute; /* Position it relative to the message */
    bottom: 10px; /* Adjust as needed */
    right: 10px; /* Position on the right side for received messages */
    background: white; /* Background color */
    border-radius: 10px; /* Rounded corners */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* Shadow for depth */
    display: flex; /* Use flexbox for layout */
    gap: 5px; /* Space between buttons */
    padding: 10px; /* Padding inside the menu */
    z-index: 10; /* Bring it to the front */
}

.reaction:hover {
    transform: scale(1.05);
}

.reaction.active {
    background: var(--primary-color);
    color: white;
    border-color: transparent;
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0 0.5rem;
}

.message-sent .message-time {
    text-align: right;
}

.message-input {
    padding: 16px;
    background: white;
    border-top: 1px solid var(--border-color);
    position: relative;
}

.chat-input-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-width: 950px;
    margin: 0 auto;
}

.chat-input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 8px;
    background: #f0f2f5;
    padding: 8px 16px;
    border-radius: 24px;
    min-height: 44px;
    transition: all 0.2s;
    position: relative;
}

.chat-input-wrapper:focus-within {
    background: white;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    border: 1px solid #e4e6eb;
}

.chat-input-buttons {
    display: flex;
    gap: 4px;
    padding-bottom: 2px;
}

.input-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s;
    color: #65676b;
    background: transparent;
    position: relative;
}

.input-button:hover {
    background: #e4e6eb;
}

.input-button.active {
    color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
}

.chat-input-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 20px;
    max-height: 150px;
}

.chat-input {
    width: 100%;
    border: none;
    background: transparent;
    padding: 8px 0;
    font-size: 0.95rem;
    line-height: 1.4;
    outline: none;
    resize: none;
    min-height: 20px;
    max-height: 150px;
}

.chat-input:empty::before {
    content: attr(placeholder);
    color: #65676b;
}

.chat-input-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 4px;
    padding-top: 4px;
    border-top: 1px solid #e4e6eb;
    display: none;
}

.chat-input-wrapper:focus-within .chat-input-actions {
    display: flex;
}

.action-button {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
    color: #65676b;
    cursor: pointer;
    transition: all 0.2s;
}

.action-button:hover {
    background: #e4e6eb;
}

.send-button {
    color: var(--primary-color);
    width: 36px;
    height: 36px;
}

.send-button:hover {
    background: rgba(37, 99, 235, 0.1);
}

.send-button.active {
    transform: scale(1.1);
}

/* Emoji picker styles remain unchanged */
#emojiPicker {
    position: absolute;
    bottom: calc(100% + 10px);
    left: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    width: 300px;
    display: none;
    z-index: 1000;
    padding: 10px;
}

#emojiPicker.active {
    display: block;
}

.emoji-categories {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
    overflow-x: auto;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
}

.emoji-category {
    padding: 5px 10px;
    border-radius: 15px;
    cursor: pointer;
    white-space: nowrap;
    background: #f0f2f5;
    font-size: 14px;
}

.emoji-category:hover,
.emoji-category.active {
    background: var(--primary-color);
    color: white;
}

.emoji-list {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
    height: 200px;
    overflow-y: auto;
    padding: 5px;
}

.emoji {
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 20px;
    border-radius: 5px;
    transition: all 0.2s;
}

.emoji:hover {
    background: #f0f2f5;
    transform: scale(1.1);
}

/* Extended Emoji Picker Styles */
.extended-emoji-picker {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    width: 320px;
    padding: 12px;
}

.emoji-picker-header {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
    margin-bottom: 8px;
}

.category-tab {
    padding: 6px 12px;
    border: none;
    background: none;
    border-radius: 16px;
    cursor: pointer;
    white-space: nowrap;
    transition: all 0.2s;
}

.category-tab.active {
    background: #e8f0fe;
    color: #1a73e8;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 4px;
    padding: 8px;
    max-height: 200px;
    overflow-y: auto;
}

.emoji-item {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 20px;
    transition: all 0.2s;
}

.emoji-item:hover {
    background: #f0f0f0;
    transform: scale(1.2);
}

/* Animation for picker appearance */
.extended-emoji-picker {
    animation: pickerFadeIn 0.2s ease;
}

@keyframes pickerFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}


/* Profile Panel Styles */
.profile-panel {
    position: fixed;
    right: -400px;
    top: 0;
    width: 400px;
    height: 100vh;
    background: white;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    z-index: 1000;
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.profile-panel.active {
    right: 0;
}

.profile-panel-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.profile-panel-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.close-profile-panel {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-secondary);
    cursor: pointer;
}

.profile-info-section {
    padding: 32px 24px;
    text-align: center;
    background: linear-gradient(to bottom, #f0f7ff, white);
    border-bottom: 1px solid var(--border-color);
}

.profile-photo-wrapper {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 20px;
}

.profile-panel-avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid white;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.profile-name-large {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.profile-status-large {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.profile-menu {
    padding: 16px;
}

.profile-menu-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    margin-bottom: 8px;
}

.profile-menu-item:hover {
    background: #f0f7ff;
}

.icon-wrapper {
    width: 36px;
    height: 36px;
    background: #e5efff;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}

.icon-wrapper i {
    color: var(--primary-color);
    font-size: 1.1rem;
}

.menu-content {
    flex: 1;
}

.menu-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.menu-description {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.profile-info-details {
    padding: 24px;
    overflow-y: auto;
}

.info-group {
    margin-bottom: 24px;
}

.info-group h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
}

.info-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.info-value {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.9rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .profile-panel {
        width: 100%;
        right: -100%;
    }
}

/* Files Section Styles */
.files-section {
    padding: 20px;
    height: 100%;
    overflow-y: auto;
}

.files-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.files-filter {
    display: flex;
    gap: 10px;
}

.filter-btn {
    padding: 8px 16px;
    border: 1px solid #e5e7eb;
    border-radius: 20px;
    background: white;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn.active {
    background: #2563eb;
    color: white;
    border-color: #2563eb;
}

.files-date-group {
    margin-bottom: 30px;
}

.date-header {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: 1px solid #e5e7eb;
}

.files-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
}

.file-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.file-preview {
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.file-preview img.file-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.file-preview i {
    font-size: 2.5rem;
}

.file-info {
    padding: 12px;
}

.file-name {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #666;
}

.loading-files {
    text-align: center;
    padding: 40px;
}

.spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #2563eb;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.no-files {
    text-align: center;
    padding: 40px;
    color: #666;
}

.no-files i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #ccc;
}

/* Search Section Styles */
.search-input-wrapper {
    position: relative;
    margin-top: 16px;
}

.search-input {
    width: 100%;
    padding: 12px 16px 12px 40px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
}

.search-input-wrapper i {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

/* Common States */
.empty-state {
    text-align: center;
    padding: 48px 24px;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.loading-state {
    text-align: center;
    padding: 48px 24px;
    color: var(--text-secondary);
}

.spinner {
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Profile Panel Styles */
.full-profile-panel {
    position: fixed;
    right: 0;
    top: 0;
    height: 100vh;
    width: 450px;
    background: white;
    box-shadow: -4px 0 25px rgba(0, 0, 0, 0.1);
    transform: translateX(100%);
    transition: all 0.3s ease;
    z-index: 1000;
    display: flex;
    flex-direction: column;
}

.full-profile-panel.active {
    transform: translateX(0);
}

.profile-cover-photo {
    height: 150px;
    background: linear-gradient(120deg, #2563eb, #1d4ed8);
    border-radius: 0 0 20px 20px;
    position: relative;
}

.profile-photo-wrapper {
    position: relative;
    margin-top: -60px;
    text-align: center;
}

.profile-panel-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    object-fit: cover;
}

.profile-status-badge {
    position: absolute;
    bottom: 10px;
    right: calc(50% - 45px);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-status-badge.online i {
    color: #10b981;
}

.profile-status-badge.offline i {
    color: #6b7280;
}

.profile-header-info {
    text-align: center;
    padding: 1rem;
}

.profile-tags {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    margin-top: 0.5rem;
}

.profile-tag {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.client-tag {
    background: rgba(37, 99, 235, 0.1);
    color: #2563eb;
}

.genius-tag {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.profile-quick-stats {
    display: flex;
    justify-content: space-around;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
    margin: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    display: block;
}

.stat-label {
    font-size: 0.75rem;
    color: #6b7280;
}

.profile-tabs {
    display: flex;
    padding: 0 1rem;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 1rem;
}

.tab-btn {
    padding: 1rem;
    border: none;
    background: none;
    color: #6b7280;
    font-weight: 500;
    cursor: pointer;
    position: relative;
}

.tab-btn.active {
    color: #2563eb;
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background: #2563eb;
}

.info-group {
    background: #f8fafc;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1rem;
}

.info-group h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #1f2937;
    font-size: 1rem;
    margin-bottom: 1rem;
}

.skills-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.skill-tag {
    background: rgba(37, 99, 235, 0.1);
    color: #2563eb;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
}

.activity-timeline {
    padding: 1rem 0;
}

.timeline-item {
    display: flex;
    gap: 1rem;
    padding: 1rem 0;
    position: relative;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 40px;
    bottom: -10px;
    width: 2px;
    background: #e5e7eb;
}

.timeline-item:last-child::before {
    display: none;
}

.timeline-icon {
    width: 32px;
    height: 32px;
    background: rgba(37, 99, 235, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #2563eb;
    z-index: 1;
}

.timeline-content {
    flex: 1;
}

.timeline-text {
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.timeline-date {
    font-size: 0.75rem;
    color: #6b7280;
}

/* Loading State */
.profile-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #6b7280;
}

.loading-spinner {
    border: 3px solid #f3f4f6;
    border-top: 3px solid #2563eb;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error State */
.profile-error {
    text-align: center;
    padding: 2rem;
    color: #ef4444;
}

.profile-error i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.retry-btn {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    border: none;
    background: #2563eb;
    color: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.retry-btn:hover {
    background: #1d4ed8;
}

/* Blur effect for main content */
.blur-background {
    filter: blur(4px);
    pointer-events: none;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
    .full-profile-panel {
        width: 100%;
    }
    
    .profile-quick-stats {
        padding: 1rem;
    }
    
    .info-group {
        margin: 0.5rem;
        padding: 1rem;
    }
}

/* Profile Panel Enhanced Styles */
.profile-panel {
    background: linear-gradient(165deg, #ffffff, #f8fafc);
    box-shadow: -8px 0 30px rgba(0, 0, 0, 0.08);
    height: 100vh;
    position: fixed;
    right: 0;
    top: 0;
    width: 380px;
    display: flex;
    flex-direction: column;
    transform: translateX(100%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    overflow: hidden;
    margin: 0;
    padding: 0;
    border-left: 1px solid rgba(229, 231, 235, 0.5);
}

.profile-panel.active {
    transform: translateX(0);
}

.profile-panel-header {
    padding: 20px 24px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 2;
}

.profile-info-section {
    padding: 40px 24px;
    text-align: center;
    background: linear-gradient(165deg, #f8fafc, #f1f5f9);
    position: relative;
    overflow: hidden;
}

/* Add decorative background pattern */
.profile-info-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.4) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.4) 0%, transparent 50%);
    opacity: 0.8;
}

.profile-photo-wrapper {
    position: relative;
    width: 130px;
    height: 130px;
    margin: 0 auto 24px;
    z-index: 1;
}

.profile-panel-avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255, 255, 255, 0.9);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.profile-panel-avatar:hover {
    transform: scale(1.02);
}

.profile-name-large {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1a1a1a;
    margin: 16px 0 8px;
    letter-spacing: -0.02em;
}

.profile-status-large {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    font-size: 0.9rem;
    color: #4b5563;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(4px);
}

.profile-panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    background: white;
}

/* Profile Details Styling */
.profile-detail-item {
    background: rgba(249, 250, 251, 0.8);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid rgba(229, 231, 235, 0.5);
}

.profile-detail-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.profile-detail-label {
    font-size: 0.85rem;
    color: #6b7280;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.profile-detail-value {
    font-size: 1rem;
    color: #111827;
    font-weight: 500;
}

/* Custom Scrollbar */
.profile-panel-content::-webkit-scrollbar {
    width: 6px;
}

.profile-panel-content::-webkit-scrollbar-track {
    background: rgba(241, 245, 249, 0.5);
}

.profile-panel-content::-webkit-scrollbar-thumb {
    background-color: #cbd5e1;
    border-radius: 20px;
    border: 2px solid transparent;
    background-clip: content-box;
}

/* Animation Enhancements */
@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.98);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.profile-detail-item {
    animation: fadeInScale 0.3s ease forwards;
}

/* Profile Actions */
.profile-actions {
    display: flex;
    gap: 12px;
    margin-top: 24px;
    padding: 0 12px;
}

.profile-action-btn {
    flex: 1;
    padding: 10px;
    border-radius: 10px;
    border: none;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
}

.profile-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(37, 99, 235, 0.3);
}

.profile-action-btn.secondary {
    background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
    color: #4b5563;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Scrollbar Styling */
.profile-panel-content {
    flex: 1;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

.profile-panel-content::-webkit-scrollbar {
    width: 6px;
}

.profile-panel-content::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.profile-panel-content::-webkit-scrollbar-thumb {
    background-color: #cbd5e1;
    border-radius: 20px;
    border: 2px solid #f1f5f9;
}

/* Animation for panel opening */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.profile-panel.active {
    animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Profile Panel Styles */
.profile-panel {
    background: white;
    box-shadow: -2px 0 20px rgba(0, 0, 0, 0.1);
    height: 100%;
    position: fixed;
    right: 0;
    top: 0;
    width: 400px;
    display: flex;
    flex-direction: column;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 1000;
}

.profile-panel.active {
    transform: translateX(0);
}

.profile-panel-header {
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8fafc;
}

.profile-panel-header h5 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
}

.profile-panel-close {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    background: #f1f5f9;
    color: #64748b;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.profile-panel-close:hover {
    background: #e2e8f0;
    color: #1e293b;
}

.profile-panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px 0;
}

.profile-info-section {
    padding: 0 20px 20px;
    text-align: center;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 20px;
}

.profile-details {
    /* Action buttons (reaction and reply) */
    .action-btn {
        background: var(--message-bg);
        border: none;
        cursor: pointer;
        font-size: 16px;
        color: rgba(136, 136, 136, 0.7);
        transition: color 0.3s ease, background-color 0.3s ease;
        padding: 6px;
        border-radius: 50%;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
        display: flex;
        white-space: nowrap;
        margin: 0 2px;
    }

    .action-btn:hover {
        background-color: var(--hover-bg);
        color: var(--primary-color);
    }

    /* Adjust reaction buttons container */
    .reaction-buttons {
        gap: 4px;
        background: var(--message-bg);
        padding: 4px;
        border-radius: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* Message specific positioning */
    .message-sent .reaction-buttons {
        left: -80px;  /* Adjusted to accommodate both buttons */
    }

    .message-received .reaction-buttons {
        right: -80px;  /* Adjusted to accommodate both buttons */
    }
}

.profile-menu {
    display: flex;
    flex-direction: column;
    gap: 16px;  /* Increased gap between menu items */
}

.profile-menu-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;  /* Increased padding */
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #f8fafc;
}

.profile-menu-item:hover {
    background: #f1f5f9;
    transform: translateY(-1px);
}

.menu-icon {
    margin-right: 16px;
}

.icon-wrapper {
    width: 40px;  /* Increased size */
    height: 40px;  /* Increased size */
    background: white;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;

    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.icon-wrapper i {
    font-size: 1.2rem;  /* Increased icon size */
    color: #1e293b;
}

.profile-actions {
    padding: 20px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 12px;
    background: #f8fafc;
}

.profile-action-btn {
    flex: 1;
    padding: 14px;
    border: none;
    border-radius: 10px;
    background: #f1f5f9;
    color: #1e293b;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.95rem;
}

.profile-action-btn:hover {
    background: #e2e8f0;
    transform: translateY(-1px);
}

.profile-action-btn i {
    margin-right: 8px;
    font-size: 1.1rem;
}

.panel-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999;
}

.panel-overlay.active {
    opacity: 1;
    visibility: visible;
}

@media (max-width: 1200px) {
    .chat-container.panel-active {
        padding-right: 1rem;
    }

    .profile-panel {
        width: 100%;
        max-width: 400px;
    }

    .panel-overlay.active {
        display: block;
    }
}

@media (max-width: 768px) {
    .contacts-list {
        width: 280px;
    }
    
    .contact-preview {
        max-width: 160px;
    }
    
    .profile-panel {
        width: 100%;
        max-width: 100%;
    }
}

/* Custom scrollbar */
/* Message Reactions Styling */
.message-content-wrapper {
    position: relative;
    display: flex;
    align-items: flex-end;
    gap: 8px;
}

.message-actions {
    position: relative;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.message:hover .message-actions {
    opacity: 1;
}

.message-react-btn {
    background: none;
    border: none;
    padding: 4px 8px;
    cursor: pointer;
    color: #666;
    border-radius: 50%;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.message-sent .message-react-btn {
    color: rgba(255, 255, 255, 0.8);
}

.message-react-btn:hover {
    background-color: var(--secondary-color);
}

.message-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 8px 0;
}

.message-react-btn {
    position: relative !important; /* TINANGGAL YUNG ABSOLUTE */
    width: 32px !important;
    height: 32px !important;
    border-radius: 50% !important;
    background: white !important;
    border: 2px solid #e5e7eb !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 18px !important;
    padding: 0 !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    opacity: 1 !important;
    z-index: 1000 !important;
}

/* HOVER EFFECT */
.message-react-btn:hover {
    transform: scale(1.1) !important;
    background: #f3f4f6 !important;
    border-color: var(--primary-color) !important;
}

/* For sent messages, reverse the order */
.message-sent .message-wrapper {
    flex-direction: row-reverse;
}

/* OVERRIDE YUNG EXISTING REACTION BUTTON MO */
.message-react-btn {
    width: 32px !important;
    height: 32px !important;
    border-radius: 50% !important;
    background: white !important;
    border: 2px solid #e5e7eb !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 18px !important;
    padding: 0 !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    opacity: 1 !important; /* PALAGING VISIBLE */
    position: absolute !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    z-index: 1000 !important;
}

/* POSITION FOR SENT MESSAGES */
.message-sent .message-react-btn {
    left: -40px !important;
}

/* POSITION FOR RECEIVED MESSAGES */
.message-received .message-react-btn {
    right: -40px !important;
}

/* REMOVE HOVER OPACITY */
.message:hover .message-react-btn {
    opacity: 1 !important;
}

/* HOVER EFFECT */
.message-react-btn:hover {
    transform: translateY(-50%) scale(1.1) !important;
    background: #f3f4f6 !important;
    border-color: var(--primary-color) !important;
}

.message-bubble {
    background: #f1f1f1;
    padding: 8px;
    border-radius: 12px;
    max-width: 80%;
}

.message-sent .message-bubble {
    background: var(--primary-color);
    color: white;
}

.replied-message {
    cursor: pointer;
    background: rgba(0, 0, 0, 0.04);
    padding: 6px 12px;
    border-radius: 10px;
    width: fit-content;
    max-width: 85%;
    font-size: 0.85em;
    margin-bottom: 5px;
    transition: background-color 0.2s ease;
}

.replied-message:hover {
    background: rgba(0, 0, 0, 0.08);
}

.message {
    scroll-margin: 50px; /* This helps center the message when scrolling */
}

.chat-messages {
    scroll-behavior: smooth;
    overflow-y: auto;
}

.replied-content {
    display: flex;
    align-items: center;
    gap: 6px;
}

.replied-content i {
    font-size: 12px;
    opacity: 0.7;
}

.replied-content span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.reply-content {
    display: flex;
    align-items: center;
    gap: 6px;
}

.reply-text {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #666;
}

.message-sent .reply-text {
    color: rgba(255, 255, 255, 0.8);
}

.reply-text i {
    font-size: 12px;
    opacity: 0.7;
}

.message-text {
    margin-top: 4px;
    word-break: break-word;
}

.reaction-menu {
    position: absolute;
    background: white;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    padding: 8px;
    z-index: 1000;
}

.quick-reactions {
    display: flex;
    gap: 8px;
}

.quick-reactions span {
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: transform 0.2s;
}

.quick-reactions span:hover {
    transform: scale(1.2);
    background: #f3f4f6;
}

.message-sent .message-react-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.reaction-menu {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    padding: 8px;
    margin-bottom: 8px;
    z-index: 1000;
    border: 1px solid var(--border-color);
}

.quick-reactions {
    display: flex;
    gap: 8px;
}

.quick-reactions span {
    cursor: pointer;
    padding: 6px;
    border-radius: 8px;
    transition: all 0.2s ease;
    font-size: 1.2rem;
}

.quick-reactions span:hover {
    background-color: var(--secondary-color);
    transform: scale(1.1);
}

.message-reactions {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 4px;
}

.message-reaction {
    display: inline-flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

.message-sent .message-reaction {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.message-reaction:hover {
    transform: scale(1.05);
    background-color: rgba(255, 255, 255, 0.95);
}

.reaction-count {
    margin-left: 4px;
    font-size: 12px;
    color: #666;
}

.message-sent .reaction-count {
    color: rgba(255, 255, 255, 0.8);
}

/* Adjust message container to accommodate reactions */
.message {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

/* Ensure proper alignment of reaction button */
.message-sent .message-actions {
    margin-right: 4px;
}

.message-received .message-actions {
    margin-left: 4px;
}
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8b2d1;
}

/* Message container with reactions */
.message {
    position: relative;
    margin: 8px 0;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

/* Reaction button styling */
.message-react-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: #f3f4f6;
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    transition: all 0.2s ease;
    opacity: 0;
}

.message:hover .message-react-btn {
    opacity: 1;
}

/* Position for sent messages */
.message-sent .message-react-btn {
    left: -35px;
}

/* Position for received messages */
.message-received .message-react-btn {
    right: -35px;
}

/* Reactions container */
.message-reactions {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 4px;
}

/* Individual reaction */
.reaction {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: #f3f4f6;
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 0.9em;
    cursor: pointer;
    transition: background-color 0.2s;
}

.reaction:hover {
    background: #e5e7eb;
}

.reaction-count {
    font-size: 0.8em;
    color: #6b7280;
}

/* Emoji picker styling */
.emoji-picker {
    position: absolute;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    padding: 8px;
    z-index: 1000;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 4px;
}

.emoji-button {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
    font-size: 1.2em;
}

.emoji-button:hover {
    background-color: #f0f2f5;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .message-react-btn {
        opacity: 0.8;
        width: 28px;
        height: 28px;
    }

    .reaction {
        padding: 2px 6px;
    }

    .emoji-picker {
        padding: 6px;
    }

    .emoji-button {
        padding: 6px;
    }
}


::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.message-status {
    font-size: 0.7rem;
    margin-top: 0.2rem;
    color: var(--text-secondary);
}

.message-status i {
    margin-left: 0.2rem;
}

.attachment-preview {
    margin: 10px 0;
    padding: 8px;
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.preview-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.preview-content img {
    max-height: 100px;
    max-width: 200px;
    border-radius: 8px;
    object-fit: cover;
}

.upload-loading {
    margin-top: 8px;
    padding: 4px 8px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    font-size: 0.9em;
    color: var(--text-secondary);
}

.upload-error {
    margin-top: 8px;
    padding: 4px 8px;
    background: #fee2e2;
    color: #dc2626;
    border-radius: 4px;
    font-size: 0.9em;
}

/* Message reactions display */
.message-reactions {
    display: flex;
    flex-wrap: wrap;
    gap: 2px; /* Reduced from 4px */
    margin-top: 2px; /* Reduced from 4px */
    padding: 0 2px;
}

.reaction {
    background: #f0f2f5;
    border-radius: 12px; /* Reduced from 20px */
    padding: 2px 6px; /* Reduced from 4px 10px */
    font-size: 12px; /* Reduced from 14px */
    display: inline-flex;
    align-items: center;
    gap: 4px; /* Reduced from 6px */
    cursor: pointer;
    transition: background-color 0.2s;
    max-width: 60px; /* Reduced from 100px */
}

.reaction.active {
    background: #dbe7ff;
    color: #0066ff;
}

.reaction-count {
    font-size: 11px; /* Reduced from 13px */
    min-width: 8px; /* Reduced from 12px */
    text-align: center;
}

/* Mobile responsiveness */
@media screen and (max-width: 768px) {
    .reaction-menu {
        padding: 6px 10px;
    }

    .reaction-button {
        width: 42px;      /* Slightly smaller for mobile but still touch-friendly */
        height: 42px;
        font-size: 22px;
    }
}

/* Extra small screens */
@media screen and (max-width: 320px) {
    .reaction-menu-content {
        flex-wrap: wrap;
        max-width: 180px;  /* Increased from 120px */
        justify-content: center;
    }

    .reaction-button {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }
}

/* Message Reactions */
.message-reactions {
    display: flex;
    gap: 4px;
    margin-top: 4px;
}

.reaction {
    background: #f0f2f5;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
}

.reaction.active {
    background: #dbe7ff;
    color: #0066ff;
}

.reaction-count {
    font-size: 11px;
    color: #65676b;
}

@keyframes scaleIn {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

.message-options {
    position: absolute;
    right: 0;
    top: 0;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s;
}

.message:hover .message-options {
    opacity: 1;
    visibility: visible;
}

.message-options-btn {
    padding: 0.3rem;
    border-radius: 50%;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.2s;
}

.message-options-btn:hover {
    background: var(--secondary-color);
    color: var(--text-primary);
}

.message-options-menu {
    position: absolute;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    padding: 0.5rem;
    z-index: 1000;
    min-width: 160px;
    display: none;
    animation: scaleIn 0.2s ease;
}

.message-options-menu.active {
    display: block;
}

.message-option {
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.2s;
    color: var(--text-primary);
}

.message-option:hover {
    background: var(--secondary-color);
}

.message-option i {
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(20px); }
}

.more-reactions-menu {
    position: fixed;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 8px;
    z-index: 1000;
    display: none;
    width: 280px;
}

.more-reactions-menu.active {
    display: block;
}

.reactions-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 8px;
}

.reactions-categories {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
    overflow-x: auto;
    scrollbar-width: thin;
}

.reaction-category {
    padding: 4px 8px;
    border-radius: 12px;
    cursor: pointer;
    white-space: nowrap;
    font-size: 0.9rem;
    transition: all 0.2s;
}

.reaction-category:hover,
.reaction-category.active {
    background: var(--primary-color);
    color: white;
}

.emoji-picker-container {
    position: relative;
}

emoji-picker {
    display: none;
    position: absolute;
    bottom: 100%;
    left: 0;
    margin-bottom: 10px;
    z-index: 1000;
}

.emoji-trigger {
    cursor: pointer;
}

.emoji-picker {
    position: fixed;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    padding: 12px;
    z-index: 1000;
    width: 320px;
    display: none;
}

.emoji-picker.active {
    display: block;
}

.emoji-search {
    width: 100%;
    padding: 8px 12px;
}

.file-attachment-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.2s ease-out;
}

.file-attachment-container {
    background: white;
    border-radius: 12px;
}

.message-highlighted {
    animation: highlight-pulse 3s ease-in-out;
    background-color: rgba(37, 99, 235, 0.1);
    border-radius: 8px;
}

@keyframes highlight-pulse {
    0% {
        background-color: rgba(37, 99, 235, 0.2);
    }
    100% {
        background-color: transparent;
    }
}

.file-preview-modal {
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);
}

.file-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #eee;
}

.file-preview-header h3 {
    margin: 0;
    font-size: 1.2rem;
    color: var(--text-primary);
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 5px;
}

.file-preview-content {
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    max-height: 400px;
    overflow: auto;
}

.image-preview img {
    max-width: 100%;
    max-height: 350px;
}


/* Profile Panel Styles */
.profile-panel {
    position: fixed;
    right: -400px;
    top: 0;
    width: 400px;
    height: 100vh;
    background: white;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    z-index: 1000;
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.profile-panel.active {
    right: 0;
}

.profile-panel-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.profile-panel-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.close-profile-panel {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-secondary);
    cursor: pointer;
}

.profile-info-section {
    padding: 32px 24px;
    text-align: center;
    background: linear-gradient(to bottom, #f0f7ff, white);
    border-bottom: 1px solid var(--border-color);
}

.profile-photo-wrapper {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 20px;
}

.profile-panel-avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid white;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.profile-name-large {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.profile-status-large {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.profile-menu {
    padding: 16px;
}

.profile-menu-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    margin-bottom: 8px;
}

.profile-menu-item:hover {
    background: #f0f7ff;
}

.icon-wrapper {
    width: 36px;
    height: 36px;
    background: #e5efff;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}

.icon-wrapper i {
    color: var(--primary-color);
    font-size: 1.1rem;
}

.menu-content {
    flex: 1;
}

.menu-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.menu-description {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.profile-info-details {
    padding: 24px;
    overflow-y: auto;
}

.info-group {
    margin-bottom: 24px;
}

.info-group h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
}

.info-label {
    color: var(--text-secondary);
    font-size: 0;
    padding: 6px 12px;
    border-radius: 20px;
    cursor: pointer;
    white-space: nowrap;
    font-size: 0.9rem;
    transition: all 0.2s;
    background: #f0f2f5;
}

.emoji-category:hover,
.emoji-category.active {
    background: var(--primary-color);
    color: white;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr); /* Changed from 8 to 6 columns */
    gap: 12px; /* Increased gap */
    height: 300px; /* Increased height */
    overflow-y: auto;
    padding: 8px;
}

.emoji-btn {
    width: 48px;  /* Increased from 32px */
    height: 48px; /* Increased from 32px */
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px; /* Increased from 1.2rem */
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.2s;
    padding: 8px;
}

.emoji-btn:hover {
    background: #f0f2f5;
    transform: scale(1.1);
}

emoji-picker {
    --emoji-size: 28px;   /* Increased emoji size */
    --category-emoji-size: 20px;
    width: 350px;  /* Increased width */
    height: 400px; /* Increased height */
}

emoji-picker::part(emoji) {
    font-size: 28px; /* Force larger emoji size */
}

.profile-container {
    position: relative;
    margin-left: auto;
    padding: 5px;
    z-index: 9999;  /* Increased from 1002 */
    transition: transform 0.3s ease;
}

.profile-container.panel-active {
    transform: translateX(-400px);
}

.profile-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    object-fit: cover;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s;
    position: relative;
    z-index: 9999;  /* Increased from 1001 */
}

.profile-avatar:hover {
    transform: scale(1.05);
}

.profile-dropdown {
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    width: 320px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.profile-dropdown.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.user-profile-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.dropdown-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #f0f0f0;
}

.user-details {
    flex: 1;
}

.user-details h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
}

.user-details span {
    font-size: 13px;
    color: #666;
    display: block;
    margin-top: 2px;
}

.dropdown-menu {
    padding: 8px;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    text-decoration: none;
    color: #333;
    border-radius: 8px;
    transition: all 0.2s ease;
    gap: 12px;
}

.menu-item:hover {
    background-color: #f8f9fa;
    color: var(--primary-color);
}

.menu-item i {
    font-size: 16px;
    width: 20px;
    text-align: center;
    color: #666;
}

.menu-item span {
    flex: 1;
    font-size: 14px;
}

.badge {
    background: var(--primary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.divider {
    height: 1px;
    background: #f0f0f0;
    margin: 8px 0;
}

.logout {
    color: #dc3545;
}

.logout:hover {
    background-color: #fff5f5;
    color: #dc3545;
}
 
.profile-dropdown::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 12px;
    height: 12px;
    background: white;
    transform: rotate(45deg);
    border-left: 1px solid rgba(0,0,0,0.05);
    border-top: 1px solid rgba(0,0,0,0.05);
}



#typing {
    display: none;
    padding: 12px 20px;
    background-color: #fff;
    border-radius: 20px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    animation: fadeIn 0.5s ease-in-out;
    position: relative;
    font-family: 'Inter', sans-serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5;
    color: #333;
  }
  
  #typing i {
    font-style: normal;
    font-size: 16px;
    color: #007bff;
    animation: typing 2s infinite;
    margin-left: 10px;
  }
  
  #typing i:nth-child(1) {
    color: #007bff;
  }
  
  #typing i:nth-child(2) {
    color: #6610f2;
    animation-delay: 0.2s;
  }
  
  #typing i:nth-child(3) {
    color: #6f42c1;
    animation-delay: 0.4s;
  }
  
  @keyframes typing {
    0% {
      content: "";
    }
    25% {
      content: ".";
    }
    50% {
      content: "..";
    }
    75% {
      content: "...";
    }
    100% {
      content: "";
    }
  }
  
  @keyframes fadeIn {
    0% {
      opacity: 0;
      transform: scale(0.5);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  @keyframes dotAnimation {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.2);
    }
    100% {
      transform: scale(1);
    }
  }
  
  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  @keyframes scaleUp {
    0% {
      transform: scale(1);
    }
    100% {
      wtransform: scale(1.5);
    }
  }
  
  @keyframes scaleDown {
    0% {
      transform: scale(1.5);
    }
    100% {
      transform: scale(1);
    }
  }
  
  #typing::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 12px 12px 0 12px;
    border-color: #fff transparent transparent transparent;
    animation: rotate 2s infinite;
  }
  
  #typing:hover {
    background-color: #f7f7f7;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
    animation: scaleUp 0.5s ease-in-out;
  }
  
  #typing:hover::after {
    animation: scaleDown 0.5s ease-in-out;
  }
  
  #typing i {
    animation: dotAnimation 1s infinite;
  }
/* REACTION BUTTON */
.message {
    position: relative;
    display: flex;
    align-items: flex-end;
    margin: 8px 0;
    max-width: 70%;
}

.message-sent {
    margin-left: auto;
    flex-direction: row-reverse;
}

.message-received {
    margin-right: auto;
}

.message-bubble {
    background: var(--primary-color);
    padding: 8px 12px;
    border-radius: 12px;
    position: relative;
}

.message-received .message-bubble {
    background: var(--secondary-color);
    color: var(--text-primary);
}

.message-react-btn {
    position: relative;
    background: none;
    border: none;
    padding: 6px;
    cursor: pointer;
    color: #666;
    border-radius: 50%;
    transition: all 0.2s ease;
    opacity: 0;
    margin: 0 4px;
}

.message:hover .message-react-btn {
    opacity: 1;
}

.message-react-btn:hover {
    background-color: var(--secondary-color);
}

.reaction-menu {
    position: absolute;
    bottom: 100%;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    padding: 8px;
    margin-bottom: 8px;
    z-index: 1000;
    border: 1px solid var(--border-color);
}

.message-sent .reaction-menu {
    right: 0;
}

.message-received .reaction-menu {
    left: 0;
}

.quick-reactions {
    display: flex;
    gap: 8px;
}

.quick-reactions span {
    cursor: pointer;
    padding: 6px;
    border-radius: 8px;
    transition: all 0.2s ease;
    font-size: 1.2rem;
}

.quick-reactions span:hover {
    background-color: var(--secondary-color);
    transform: scale(1.1);
}

.message-reactions {
    position: absolute;
    bottom: -20px;
    left: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.message-sent .message-reactions {
    right: 0;
    left: auto;
}

.reaction {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 2px 8px;
    background-color: white;
    border-radius: 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid var(--border-color);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.reaction:hover {
    background-color: var(--secondary-color);
}

.reaction.active {
    background-color: var(--primary-color);
    color: white;
}

.reaction-count {
    font-size: 11px;
    font-weight: 500;
}/* REACTION BUTTON */
.message {
    position: relative;
    display: flex;
    flex-direction: column;
    margin: 8px 0;
    width: fit-content;
    max-width: 70%;
}

.message-sent {
    margin-left: auto;
    padding-right: 35px; /* Space for avatar on right */
}

.message-received {
    margin-right: auto;
    padding-left: 35px; /* Space for avatar on left */
}

.message-content {
    position: relative;
    padding: 8px 12px;
    min-width: 60px; /* Minimum width for very short messages */
}

.message-reactions {
    position: relative; /* Change from absolute to relative */
    bottom: auto; /* Remove absolute positioning */
    left: auto; /* Remove absolute positioning */
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 4px;
    z-index: 1;
}

.message-content {
    z-index: 2; /* Ensure message stays above reactions */
}

.message-react-btn {
    position: absolute;
    width: 24px;
    height: 24px;
    border: none;
    background: none;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    padding: 4px; /* Added padding for better hover area */
}

/* Reaction button for received messages (left side) */
.message-received .message-react-btn {
    right: -10px; /* Changed from -5px to -10px for more space */
    top: 50%;
    transform: translateY(-50%);
}

/* Reaction button for sent messages (right side) */
.message-sent .message-react-btn {
    left: -10px; /* Changed from -5px to -10px for more space */
    top: 50%;
    transform: translateY(-50%);
}

.message:hover .message-react-btn {
    opacity: 1;
}

.message-react-btn i {
    font-size: 16px;
    color: #666;
}

/* Message bubble styles */
.message-sent .message-content {
    background-color: #007bff;
    color: white;
    border-radius: 15px 15px 0 15px;
}

.message-received .message-content {
    background-color: #f1f1f1;
    color: black;
    border-radius: 15px 15px 15px 0;
}

.more-emoji {
    font-size: 24px !important;
    font-weight: bold;
    color: #666;
}

.full-emoji-picker {
    position: fixed;
    z-index: 1001;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.full-profile-panel {
    position: fixed;
    right: -400px;
    top: 0;
    width: 400px;
    height: 100vh;
    background: white;
    box-shadow: -2px 0 5px rgba(0,0,0,0.1);
    z-index: 1000;
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
}

.full-profile-panel.active {
    right: 0;
}

.full-profile-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
}

.back-button {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    color: #666;
}

.back-button:hover {
    color: #333;
}

.info-group {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
}

.info-group h3 {
    font-size: 1rem;
    color: #1e293b;
    margin-bottom: 16px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    padding: 8px 0;
    border-bottom: 1px solid #e5e7eb;
}

.info-label {
    color: #64748b;
    font-size: 0.9rem;
}

.info-value {
    color: #1e293b;
    font-weight: 500;
}

.floating-reaction-menu {
    display: none;
    position: fixed;
    background: white;
    border-radius: 24px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.15);
    padding: 8px;
    z-index: 1000;
    animation: scaleIn 0.2s ease;
}

.reaction-menu-content {
    display: flex;
    gap: 4px;
}

.reaction-menu-content button {
    all: unset;
    cursor: pointer;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 20px;
    transition: all 0.2s ease;
}

.reaction-menu-content button:hover {
    background: #f3f4f6;
    transform: scale(1.1);
}

.message-react-btn {
    opacity: 0;
    border: none;
    background: transparent;
    padding: 4px;
    cursor: pointer;
    color: #6b7280;
    transition: opacity 0.2s ease;
}

.message:hover .message-react-btn {
    opacity: 1;
}

.message-reactions {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 4px;
}

.reaction {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: #f3f4f6;
    border-radius: 16px;
    padding: 4px 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.reaction:hover {
    background: #e5e7eb;
    transform: scale(1.05);
}

.reaction.active {
    background: #dbeafe;
    color: #2563eb;
}

.reaction-count {
    font-size: 12px;
    color: #6b7280;
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@media (max-width: 768px) {
    .reaction-menu-content button {
        width: 32px;
        height: 32px;
        font-size: 18px;
    }
}
.files-section {
    padding: 15px;
    background: #fff;
    border-radius: 8px;
    margin-top: 15px;
}

.files-section h3 {
    margin-bottom: 15px;
    font-size: 1.1rem;
    color: #333;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 8px;
    transition: all 0.2s;
}

.file-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.file-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    border-radius: 8px;
    margin-right: 12px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.file-icon i {
    font-size: 1.2rem;
    color: #2563eb;
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 2px;
}

.file-meta {
    font-size: 0.8rem;
    color: #6b7280;
}

.file-actions {
    margin-left: 10px;
}

.download-btn {
    color: #2563eb;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s;
}

.download-btn:hover {
    background: #dbeafe;
    color: #1d4ed8;
}

.file-preview-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.85);
    z-index: 9999;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    position: relative;
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h4 {
    margin: 0;
    font-size: 1.2rem;
    color: #333;
}

.modal-body {
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    max-height: calc(90vh - 130px);
    overflow: auto;
}

.preview-content {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
}

.close-modal {
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    padding: 5px;
}

.close-modal:hover {
    color: #333;
}

.download-button {
    background: #2563eb;
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s;
}

.download-button:hover {
    background: #1d4ed8;
    color: white;
}

.file-info-preview {
    text-align: center;
    padding: 40px;
}

.file-preview-icon {
    font-size: 48px;
    color: #2563eb;
    margin-bottom: 20px;
}

.loading-spinner {
    text-align: center;
    padding: 20px;
    color: #666;
}

.no-files {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
}

.error-message {
    text-align: center;
    padding: 20px;
    color: #dc2626;
}

.preview-icon {
    color: #2563eb;
    font-size: 1.1rem;
}

.search-conversation-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.search-header {
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;
}

.search-input-wrapper {
    position: relative;
    margin-bottom: 12px;
}

.search-input {
    width: 100%;
    padding: 12px 40px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    background: #f9fafb;
    transition: all 0.2s;
}

.search-input:focus {
    background: #fff;
    border-color: #2563eb;
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
}

.clear-search {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    display: none;
}

.search-filters {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    padding-bottom: 4px;
}

.filter-btn {
    padding: 6px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 16px;
    background: #fff;
    color: #4b5563;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s;
    white-space: nowrap;
}

.filter-btn:hover {
    background: #f9fafb;
}

.filter-btn.active {
    background: #2563eb;
    color: #fff;
    border-color: #2563eb;
}

.search-results {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.initial-state,
/* Reaction Menu */
.reaction-menu {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    padding: 8px;
}

.quick-reactions {
    display: flex;
    gap: 4px;
}

.quick-reaction-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    transition: all 0.2s;
}

.quick-reaction-btn:hover {
    background: #f0f0f0;
}

.extended-emoji-picker {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    width: 320px;
    padding: 12px;
}

.emoji-picker-header {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
    margin-bottom: 8px;
}

.category-tab {
    padding: 6px 12px;
    border: none;
    background: none;
    border-radius: 16px;
    cursor: pointer;
    white-space: nowrap;
    transition: all 0.2s;
}

.category-tab.active {
    background: #e8f0fe;
    color: #1a73e8;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 4px;
    padding: 8px;
}

.emoji-item {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 20px;
    transition: all 0.2s;
}

.emoji-item:hover {
    background: #f0f0f0;
    transform: scale(1.2);
}
.searching-state,
.no-results-state,
.error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6b7280;
    text-align: center;
}

.initial-state i,
.no-results-state i,
.error-state i {
    font-size: 24px;
    margin-bottom: 12px;
}

.spinner {
    width: 24px;
    height: 24px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #2563eb;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.results-count {
    font-size: 1rem;
    color: #6b7280;
}

/* File Upload Styles */
.file-upload-container {
    position: relative;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    background: white;
    border-top: 1px solid rgba(229, 231, 235, 0.5);
}

.file-upload-options {
    display: flex;
    gap: 8px;
}

.file-upload-button {
    background: transparent;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #64748b;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-upload-button:hover {
    background: #f1f5f9;
    color: #3b82f6;
}

.file-upload-button i {
    font-size: 1.25rem;
}

/* File Preview Styles */
.file-preview {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid rgba(229, 231, 235, 0.5);
    padding: 16px;
    display: none;
    animation: slideUp 0.3s ease;
}

.file-preview.active {
    display: block;
}

.file-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.file-preview-title {
    font-weight: 500;
    color: #1e293b;
}

.clear-file {
    background: transparent;
    border: none;
    color: #ef4444;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.875rem;
}

.clear-file:hover {
    background: #fee2e2;
}

.file-preview-content {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #f8fafc;
    border-radius: 8px;
}

.file-preview-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #e2e8f0;
    border-radius: 8px;
    color: #64748b;
}

.file-preview-icon i {
    font-size: 1.5rem;
}

.file-preview-info {
    flex: 1;
}

.file-name {
    font-weight: 500;
    color: #1e293b;
    margin-bottom: 4px;
}

.file-size {
    font-size: 0.875rem;
    color: #64748b;
}

/* File Upload Progress */
.upload-progress {
    height: 4px;
    background: #e2e8f0;
    border-radius: 2px;
    margin-top: 8px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: #3b82f6;
    width: 0;
    transition: width 0.3s ease;
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* File Type Icons */
.file-type-icon {
    font-size: 1.5rem;
}

.file-type-image { color: #22c55e; }
.file-type-video { color: #ef4444; }
.file-type-document { color: #3b82f6; }
.file-type-audio { color: #f59e0b; }

.search-result-item {
    padding: 12px;
    border-radius: 8px;
    background: #fff;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid var(--border-color);
}

.search-result-item:hover {
    background: var(--secondary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.result-time {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.result-message {
    color: var(--text-primary);
    font-size: 14px;
    line-height: 1.5;
}

mark {
    background-color: #fff176;
    padding: 2px 0;
    border-radius: 2px;
    color: inherit;
}

.media-result,
.file-result {
    display: flex;
    align-items: center;
    gap: 12px;
}

.media-preview {
    width: 48px;
    height: 48px;
    border-radius: 4px;
    overflow: hidden;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
}

.media-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.media-preview i {
    font-size: 24px;
    color: #2563eb;
}

.file-icon {
    width: 48px;
    height: 48px;
    border-radius: 4px;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-icon i {
    font-size: 24px;
    color: #2563eb;
}

.result-title {
    font-size: 14px;
    color: #1f2937;
    margin-bottom: 4px;
}

.highlighted {
    animation: highlight 2s;
}

@keyframes highlight {
    0% { background-color: #fef08a; }
    100% { background-color: transparent; }
}

/* Search Highlighting */
.search-highlight {
    background-color: #fef08a;
    padding: 0 2px;
    border-radius: 3px;
}

.message.highlighted {
    animation: highlight 2s;
}

@keyframes highlight {
    0% { 
        background-color: rgba(254, 240, 138, 0.5); 
    }
    100% { 
        background-color: transparent; 
    }
}

/* Search Results Styling */
.search-results-container {
    padding: 16px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    margin: 16px;
}

.search-result-item {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.2s;
}

.search-result-item:hover {
    background-color: var(--secondary-color);
}

.search-result-item:last-child {
    border-bottom: none;
}

.result-message {
    color: var(--text-primary);
    font-size: 14px;
    line-height: 1.5;
    margin-top: 4px;
}

.result-time {
    color: var(--text-secondary);
    font-size: 12px;
    margin-top: 4px;
}

/* Search Loading State */
.search-loading {
    text-align: center;
    padding: 20px;
    color: var(--text-secondary);
}

/* No Results State */
.no-results {
    text-align: center;
    padding: 20px;
    color: var(--text-secondary);
}






.file-message {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.file-message i {
    font-size: 20px;
}

.file-message a {
    color: inherit;
    text-decoration: none;
}

.file-message a:hover {
    text-decoration: underline;
}






.file-attachment {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    margin: 4px 0;
    cursor: pointer;
    transition: all 0.2s ease;
}

.file-attachment:hover {
    background-color: rgba(0, 0, 0, 0.1);
    text-decoration: underline;
}

.file-attachment .file-name {
    color: #2196F3;  /* Link blue color */
    font-size: 14px;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.file-attachment:hover .file-name {
    color: #1976D2;  /* Darker blue on hover */
}

.file-attachment .fas {
    color: #666;
}

.file-attachment:hover .fas {
    color: #444;
}

/* For files in sent messages (right side) */
.message-sent .file-attachment {
    background-color: rgba(255, 255, 255, 0.1);
}

.message-sent .file-attachment:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.message-sent .file-attachment .file-name {
    color: #fff;  /* White text for sent messages */
}

.message-sent .file-attachment:hover .file-name {
    color: #E3F2FD;  /* Lighter color on hover */
}

.message-sent .file-attachment .fas {
    color: rgba(255, 255, 255, 0.8);
}

.message-sent .file-attachment:hover .fas {
    color: #fff;
}

.file-preview-container {
    display: inline-flex; /* Changed to inline-flex */
    align-items: center;
    gap: 4px;
    padding: 2px;
    background: #f8fafc;
    border-radius: 4px;
    margin: 2px 0;
}

.file-preview-item {
    display: flex;
    align-items: center;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 2px 4px;
    max-width: 200px;
}

.preview-content {
    display: flex;
    align-items: center;
    gap: 4px;
}

.file-icon-wrapper {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-icon-wrapper i {
    font-size: 14px;
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-size: 11px;
    color: #334155;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.file-size {
    font-size: 9px;
    color: #64748b;
}

.remove-file {
    padding: 2px;
    color: #94a3b8;
    cursor: pointer;
    border: none;
    background: none;
    display: flex;
    align-items: center;
}

.remove-file i {
    font-size: 12px;
}

.remove-file:hover {
    color: #ef4444;
}

/* For image thumbnails */
.file-thumbnail {
    width: 20px;
    height: 20px;
    object-fit: cover;
    border-radius: 2px;
}


.message-actions {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.message-sent .message-actions {
    left: -40px;
}

.message-received .message-actions {
    right: -40px;
}

.message:hover .message-actions {
    opacity: 1;
}

.message-react-btn {
    background: white;
    border: 1px solid var(--border-color);
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.message-react-btn:hover {
    background-color: var(--secondary-color);
    transform: scale(1.1);
}

.message-content-wrapper {
    position: relative;
    display: flex;
    align-items: flex-end;
}

/* Reply button styling - keep existing */
.reply-btn {
    background: white;
    border: 1px solid var(--border-color);
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-right: 4px;
}

.reply-btn:hover {
    background-color: var(--secondary-color);
    transform: scale(1.1);
}

/* Adjust message container to accommodate reply */
.message {
    position: relative;
    margin-top: 24px; /* Space for the reply above */
}

/* Reply container in messages */
.replied-message {
    position: absolute;
    top: -28px; /* Slightly increased space from message */
    left: 12px;  /* Add default indent */
    right: 12px; /* Add default indent */
    background: #f8f9fa;
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 13px;
    width: fit-content;
    max-width: 100%;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Sent message specific reply styling */
.message-sent .replied-message {
    background: #f0f7ff;  /* Light blue background */
    border-left-color: #2196F3;
    color: #1a1a1a;
    margin-left: auto;
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.1);
    padding: 6px 12px;
}

/* Reply container for the input area */
.reply-container {
    background: linear-gradient(to right, #f8f9fa, #ffffff);
    padding: 12px 16px;
    margin: 10px 0;
    border-radius: 12px;
    border-left: 4px solid #2196F3;
    position: relative;
    z-index: 2;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 
                0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.reply-container:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.08), 
                0 2px 4px rgba(0, 0, 0, 0.12);
}

.reply-preview {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 15px;
    color: #000000;
}

.reply-preview i {
    color: #0052cc;
    font-size: 16px;
    opacity: 1;
}

.reply-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 700;
    letter-spacing: 0.01em;
    color: #000000;
}

.cancel-reply {
    background: none;
    border: none;
    color: #4a5568;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.25s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cancel-reply:hover {
    color: #dc2626;
    background: rgba(220, 38, 38, 0.1);
    transform: rotate(90deg);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .reply-container {
        border-left-color: #3182ce;
    }

    .reply-preview {
        color: #ffffff;
    }

    .reply-preview i {
        color: #60a5fa;
    }

    .reply-text {
        color: #ffffff;
    }

    .cancel-reply {
        color: #e2e8f0;
    }

    .cancel-reply:hover {
        color: #fc8181;
        background: rgba(252, 129, 129, 0.2);
    }
}

/* Add hover effect for replied message */
.replied-message:hover {
    background: #f0f7ff;
    cursor: pointer;
    transform: translateY(-1px);
}

.message-sent .replied-message:hover {
    background: #e3f2fd;
}

/* Add icons styling */
.replied-message i,
.reply-preview i {
    color: #2196F3;
    font-size: 14px;
}

/* Add subtle transition effects */
.replied-message,
.reply-container,
.cancel-reply {
    transition: all 0.2s ease-in-out;
}

/* Quick reactions container */
.quick-reactions {
    display: flex;
    align-items: center;
    gap: 4px; /* Reduced gap */
    padding: 2px; /* Minimal padding */
}

/* Plus button */
.plus-reaction-button {
    background: none;
    border: none;
    color: #666;
    padding: 4px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .emoji-picker {
        background: #333;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .plus-reaction-button {
        color: #999;
    }
}

/* Mobile adjustments */
@media (max-width: 768px) {
    .emoji-picker {
        width: 180px; /* Even more compact on mobile */
        padding: 4px;
    }

    .emoji-option {
        font-size: 14px;
    }
}

/* Base emoji picker styles */
.emoji-picker {
    display: none;
    position: absolute;
    bottom: 30px;
    width: 200px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 10000;
    padding: 6px;
    overflow: hidden;
}

/* Sender message emoji picker */
.message-sent .emoji-picker {
    right: 0;
    transform: translateX(0);
}

/* Receiver message emoji picker */
.message-received .emoji-picker {
    left: 0;
    transform: translateX(0);
}

/* Quick reactions container */
.quick-reactions {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 2px;
}

/* Emoji options */
.emoji-option {
    font-size: 16px;
    padding: 4px;
    cursor: pointer;
    border-radius: 4px;
}

/* Plus button */
.plus-reaction-button {
    background: none;
    border: none;
    color: #666;
    padding: 4px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
    .emoji-picker {
        background: #333;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
}

/* Mobile adjustments */
@media (max-width: 768px) {
    .emoji-picker {
        width: 180px;
        padding: 4px;
    }
    
    .emoji-option {
        font-size: 14px;
    }
}

/* Base reply preview */
.reply-preview {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #444;  /* Darker text color */
}

/* Sender message reply preview */
.message-sent .reply-preview {
    flex-direction: row-reverse;
    text-align: right;
}

/* Reply text */
.reply-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 500;  /* Slightly bolder */
    color: #444;  /* Darker text color */
}

/* Icon positioning */
.reply-preview i {
    font-size: 14px;
    color: #555;  /* Darker icon color */
}

.message-sent .reply-preview i {
    margin-left: 0;
    margin-right: 8px;
}

.message-received .reply-preview i {
    margin-right: 8px;
    margin-left: 0;
}

/* Text alignment */
.message-sent .reply-text {
    text-align: right;
}

.message-received .reply-text {
    text-align: left;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
    .reply-preview,
    .reply-text {
        color: #ddd;  /* Lighter text in dark mode */
    }
    .reply-preview i {
        color: #ccc;  /* Lighter icon in dark mode */
    }
}
/* Base reply message - transparent */
.replied-message {
    position: absolute;
    top: -28px;
    left: 12px;
    right: 12px;
    background: rgba(255, 255, 255, 0.5);  /* Transparent white */
    padding: 4px 12px;
    font-size: 13px;
    width: fit-content;
    max-width: 100%;
}

/* Sender position */
.message-sent .replied-message {
    margin-left: auto;
    margin-right: 12px;
    text-align: right;
    padding-right: 32px;
}

/* Receiver position */
.message-received .replied-message {
    margin-right: auto;
    margin-left: 12px;
    text-align: left;
    padding-left: 32px;
}

/* Reply text - light gray */
.reply-text {
    color: #888;
}

/* Icon - minimal */
.replied-message i {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 13px;
    color: #888;
}

/* Icon position */
.message-sent .replied-message i {
    right: 12px;
}

.message-received .replied-message i {
    left: 12px;
}

/* Dark mode - transparent */
@media (prefers-color-scheme: dark) {
    .replied-message {
        background: rgba(0, 0, 0, 0.3);  /* Transparent black */
    }
    .reply-text,
    .replied-message i {
        color: #999;
    }
}

/* Style for deleted messages */
.message-text.deleted-message {
    font-style: italic;
    color: #888;
}

/* No hover effects for deleted messages */
.message .deleted-message:hover {
    background-color: inherit;
}

/* Hide all interactive elements for deleted messages */
.message .deleted-message + .file-attachment,
.message .deleted-message ~ .message-reactions {
    display: none;
}

/* Styling for deleted messages */
.message.message-deleted {
    opacity: 0.8;
}

.message-text.deleted-message {
    font-style: italic;
    color: #888 !important;
    user-select: none;
}

/* Make sure deleted messages don't show hover effects or interactive elements */
.message.message-deleted:hover {
    background-color: inherit !important;
}

.message.message-deleted .reaction-buttons,
.message.message-deleted .file-attachment,
.message.message-deleted .message-reactions {
    display: none !important;
}

/* Style for unsent message previews in contact list */
.unsent-preview-text {
    color: #888 !important;
    opacity: 0.8;
}

/* Style for deleted/unsent messages in the chat window */
.message-deleted {
    opacity: 0.8;
}

.message-text.deleted-message {
    font-style: italic !important;
    color: #888 !important;
}

/* Style for contact previews with unsent messages */
.contact-item[data-last-message-unsent="true"] .preview-text {
    font-style: italic;
    color: #888 !important;
}

/* Make the entire contact preview dimmer if it contains a deleted message */
.contact-item[data-message-deleted="true"] .preview-text {
    opacity: 0.8;
}

/* Add these styles for the unsend modal */
#unsendModal .modal-content {
    border-radius: 12px;
    border: none;
}

#unsendModal .modal-header {
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}

#unsendModal .modal-body {
    padding: 1.5rem;
}

#unsendModal .modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}

#unsendModal .btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

#unsendModal .btn-danger:hover {
    background-color: #bb2d3b;
    border-color: #b02a37;
}

#unsendModal .text-muted {
    color: #6c757d !important;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    #unsendModal .modal-content {
        background-color: #2d2d2d;
        color: #fff;
    }

    #unsendModal .modal-header,
    #unsendModal .modal-footer {
        border-color: #404040;
    }

    #unsendModal .text-muted {
        color: #a0a0a0 !important;
    }

    #unsendModal .btn-secondary {
        background-color: #4a4a4a;
        border-color: #4a4a4a;
    }
}











 
