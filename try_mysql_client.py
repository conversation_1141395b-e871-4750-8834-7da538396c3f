import subprocess
import sys
import os

def try_mysql_client():
    print("=== Trying to connect with MySQL command line client ===")
    
    # Try to find mysql client
    try:
        print("\nChecking if MySQL client is in PATH...")
        result = subprocess.run(["where", "mysql"], capture_output=True, text=True)
        
        if result.returncode == 0:
            mysql_path = result.stdout.strip().split('\n')[0]
            print(f"✅ Found MySQL client at: {mysql_path}")
        else:
            print("❌ MySQL client not found in PATH")
            
            # Check common locations
            common_paths = [
                "C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin\\mysql.exe",
                "C:\\Program Files\\MySQL\\MySQL Server 5.7\\bin\\mysql.exe",
                "C:\\xampp\\mysql\\bin\\mysql.exe",
                "C:\\wamp\\bin\\mysql\\mysql5.7.36\\bin\\mysql.exe",
                "C:\\wamp64\\bin\\mysql\\mysql5.7.36\\bin\\mysql.exe",
                "C:\\laragon\\bin\\mysql\\mysql-8.0.30-winx64\\bin\\mysql.exe"
            ]
            
            for path in common_paths:
                if os.path.exists(path):
                    mysql_path = path
                    print(f"✅ Found MySQL client at: {mysql_path}")
                    break
            else:
                print("❌ Could not find MySQL client")
                print("Please install MySQL or make sure it's in your PATH")
                return
    except Exception as e:
        print(f"Error checking for MySQL client: {e}")
        return
    
    # Try connecting with different credentials
    credentials = [
        {"user": "giguser", "password": "Happiness1524!", "description": "giguser with password"},
        {"user": "root", "password": "Happiness1524!", "description": "root with password"},
        {"user": "root", "password": "", "description": "root with empty password"},
        {"user": "root", "password": "password", "description": "root with 'password'"},
        {"user": "root", "password": "root", "description": "root with 'root'"},
        {"user": "phpmyadmin", "password": "", "description": "phpmyadmin with empty password"}
    ]
    
    for cred in credentials:
        print(f"\nTrying to connect as {cred['description']}...")
        
        cmd = [mysql_path, f"-u{cred['user']}"]
        if cred['password']:
            cmd.append(f"-p{cred['password']}")
        
        cmd.extend(["-e", "SELECT 'Connection successful!' AS Result;"])
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Connection successful!")
                print(result.stdout)
                
                # Try to show databases
                print("\nListing databases...")
                cmd = [mysql_path, f"-u{cred['user']}"]
                if cred['password']:
                    cmd.append(f"-p{cred['password']}")
                
                cmd.extend(["-e", "SHOW DATABASES;"])
                
                db_result = subprocess.run(cmd, capture_output=True, text=True)
                if db_result.returncode == 0:
                    print(db_result.stdout)
                else:
                    print(f"❌ Error listing databases: {db_result.stderr}")
                
                return True
            else:
                print(f"❌ Connection failed: {result.stderr}")
        except Exception as e:
            print(f"Error running MySQL client: {e}")
    
    print("\n❌ All connection attempts failed")
    print("\n=== Suggestions ===")
    print("1. Make sure MySQL is running")
    print("2. Check the MySQL configuration")
    print("3. Try connecting with phpMyAdmin or MySQL Workbench")
    print("4. Check the MySQL logs for authentication errors")
    return False

if __name__ == "__main__":
    success = try_mysql_client()
    sys.exit(0 if success else 1)
