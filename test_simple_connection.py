import mysql.connector
import sys

# Simple database configuration
db_config = {
    'host': 'localhost',
    'user': 'giguser',
    'password': 'password123',
    'database': 'gigdb'
}

def test_connection():
    try:
        print("Attempting to connect to MySQL database with simple credentials...")
        conn = mysql.connector.connect(**db_config)
        
        if conn.is_connected():
            print("✅ SUCCESS! Connected to MySQL database")
            db_info = conn.get_server_info()
            print(f"MySQL Server version: {db_info}")
            
            cursor = conn.cursor()
            cursor.execute("SELECT DATABASE();")
            db_name = cursor.fetchone()[0]
            print(f"Connected to database: {db_name}")
            
            cursor.close()
            conn.close()
            
            print("\nYour database connection is working correctly with these simple credentials!")
            print("You should update your app.py file with these credentials:")
            print("    'host': 'localhost',")
            print("    'user': 'giguser',")
            print("    'password': 'password123',")
            print("    'database': 'gigdb'")
            return True
            
    except mysql.connector.Error as e:
        print(f"❌ Error connecting to MySQL database: {e}")
        
        # Provide troubleshooting tips
        print("\nTroubleshooting tips:")
        print("1. Make sure you've created the user and database in phpMyAdmin as instructed")
        print("2. Check for any typos in the username, password, or database name")
        print("3. Try restarting the MySQL service")
        
        return False

if __name__ == "__main__":
    success = test_connection()
    sys.exit(0 if success else 1)
