#!/usr/bin/env python3
"""
Migration script to update existing users' PWD status to 'not applicable'
This script handles users who registered before the PWD system was implemented.
"""

import mysql.connector
import sys
from datetime import datetime

# Database configuration - using the same as app.py
db_config = {
    'host': '**************',
    'user': 'giggenius_user',
    'password': 'Happiness1524!',
    'database': 'giggenius',
    'connect_timeout': 10,
    'use_pure': True,
    'autocommit': True
}

def update_existing_users():
    """Update existing users to have 'not applicable' PWD status"""
    try:
        print("🔄 Updating existing users' PWD status...")
        print("=" * 60)
        
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor(dictionary=True)
        
        # Tables to update
        tables = ['register_genius', 'approve_genius', 'register_client', 'approve_client']
        
        for table in tables:
            print(f"\n📋 Processing table: {table}")
            
            # Check if PWD columns exist
            check_columns_query = f"""
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = 'giggenius'
                AND TABLE_NAME = '{table}'
                AND COLUMN_NAME IN ('is_pwd', 'pwd_approval_status', 'commission_rate')
            """
            cursor.execute(check_columns_query)
            existing_columns = [row['COLUMN_NAME'] for row in cursor.fetchall()]
            
            if not existing_columns:
                print(f"   ⚠️  PWD columns not found in {table}. Skipping...")
                continue
            
            # Check for users without PWD data (existing users)
            check_existing_query = f"""
                SELECT COUNT(*) as count
                FROM {table}
                WHERE is_pwd IS NULL OR is_pwd = 0
            """
            cursor.execute(check_existing_query)
            existing_count = cursor.fetchone()['count']
            
            if existing_count == 0:
                print(f"   ✅ No existing users found in {table}")
                continue
            
            print(f"   👥 Found {existing_count} existing users in {table}")
            
            # Update existing users to have proper PWD status
            if 'pwd_approval_status' in existing_columns:
                # For tables with approval status, set to 'not_applicable'
                update_query = f"""
                    UPDATE {table}
                    SET is_pwd = 0,
                        pwd_approval_status = 'not_applicable',
                        commission_rate = 10.00,
                        pwd_condition = NULL,
                        pwd_proof = NULL,
                        pwd_approved_by = NULL,
                        pwd_approved_at = NULL,
                        pwd_rejection_reason = NULL
                    WHERE (is_pwd IS NULL OR is_pwd = 0)
                    AND (pwd_approval_status IS NULL OR pwd_approval_status = 'pending')
                """
            else:
                # For tables without approval status, just set basic PWD fields
                update_query = f"""
                    UPDATE {table}
                    SET is_pwd = 0,
                        commission_rate = 10.00,
                        pwd_condition = NULL,
                        pwd_proof = NULL
                    WHERE is_pwd IS NULL OR is_pwd = 0
                """
            
            cursor.execute(update_query)
            updated_count = cursor.rowcount
            print(f"   ✅ Updated {updated_count} users in {table}")
            
            # Verify the update
            verify_query = f"""
                SELECT 
                    COUNT(*) as total_users,
                    SUM(CASE WHEN is_pwd = 0 THEN 1 ELSE 0 END) as non_pwd_users,
                    SUM(CASE WHEN is_pwd = 1 THEN 1 ELSE 0 END) as pwd_users
                FROM {table}
            """
            cursor.execute(verify_query)
            stats = cursor.fetchone()
            print(f"   📊 Table stats: {stats['total_users']} total, {stats['non_pwd_users']} non-PWD, {stats['pwd_users']} PWD")
            
            # Show approval status distribution if column exists
            if 'pwd_approval_status' in existing_columns:
                status_query = f"""
                    SELECT pwd_approval_status, COUNT(*) as count
                    FROM {table}
                    GROUP BY pwd_approval_status
                """
                cursor.execute(status_query)
                status_stats = cursor.fetchall()
                print(f"   📈 Approval status distribution:")
                for stat in status_stats:
                    status = stat['pwd_approval_status'] or 'NULL'
                    print(f"      - {status}: {stat['count']} users")
        
        print(f"\n🎉 Migration completed successfully!")
        print(f"📅 Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        cursor.close()
        conn.close()
        
    except mysql.connector.Error as err:
        print(f"❌ Database Error: {err}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        sys.exit(1)

def verify_migration():
    """Verify the migration was successful"""
    try:
        print("\n🔍 Verifying migration results...")
        print("=" * 40)
        
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor(dictionary=True)
        
        tables = ['register_genius', 'approve_genius', 'register_client', 'approve_client']
        
        for table in tables:
            # Check if table exists
            check_table_query = f"""
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_schema = 'giggenius'
                AND table_name = '{table}'
            """
            cursor.execute(check_table_query)
            table_exists = cursor.fetchone()['COUNT(*)'] > 0
            
            if not table_exists:
                print(f"   ⚠️  Table {table} does not exist")
                continue
            
            # Get comprehensive stats
            stats_query = f"""
                SELECT 
                    COUNT(*) as total_users,
                    SUM(CASE WHEN is_pwd = 0 THEN 1 ELSE 0 END) as non_pwd_users,
                    SUM(CASE WHEN is_pwd = 1 THEN 1 ELSE 0 END) as pwd_users,
                    SUM(CASE WHEN commission_rate = 10.00 THEN 1 ELSE 0 END) as rate_10_percent,
                    SUM(CASE WHEN commission_rate = 5.00 THEN 1 ELSE 0 END) as rate_5_percent
                FROM {table}
            """
            cursor.execute(stats_query)
            stats = cursor.fetchone()
            
            print(f"\n📊 {table}:")
            print(f"   Total users: {stats['total_users']}")
            print(f"   Non-PWD users: {stats['non_pwd_users']}")
            print(f"   PWD users: {stats['pwd_users']}")
            print(f"   10% commission rate: {stats['rate_10_percent']}")
            print(f"   5% commission rate: {stats['rate_5_percent']}")
        
        cursor.close()
        conn.close()
        
        print(f"\n✅ Verification completed!")
        
    except Exception as e:
        print(f"❌ Verification Error: {e}")

if __name__ == "__main__":
    print("🚀 Starting PWD Status Migration for Existing Users")
    print("=" * 60)
    
    # Ask for confirmation
    response = input("\nThis will update existing users' PWD status to 'not applicable'.\nDo you want to continue? (y/N): ")
    
    if response.lower() in ['y', 'yes']:
        update_existing_users()
        verify_migration()
    else:
        print("❌ Migration cancelled by user")
        sys.exit(0)
