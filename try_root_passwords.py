import mysql.connector
import sys

def try_connection(password, description):
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': password
        # No database specified to simplify connection
    }
    
    print(f"\n=== Trying {description} ===")
    print(f"Password: {'*****' if password else '(empty)'}")
    
    try:
        print("Connecting...")
        conn = mysql.connector.connect(**config)
        
        if conn.is_connected():
            print("✅ SUCCESS!")
            
            # Get server information
            db_info = conn.get_server_info()
            print(f"MySQL Server version: {db_info}")
            
            # List databases
            cursor = conn.cursor()
            cursor.execute("SHOW DATABASES;")
            dbs = cursor.fetchall()
            
            print("\nAvailable databases:")
            for db in dbs:
                print(f"- {db[0]}")
            
            # Check if giggenius database exists
            if any(db[0].lower() == 'giggenius' for db in dbs):
                print("\n✅ 'giggenius' database exists!")
            else:
                print("\n❌ 'giggenius' database does not exist")
            
            cursor.close()
            conn.close()
            return True
    except Exception as e:
        print(f"❌ FAILED: {e}")
    
    return False

# Try different root password options
passwords = [
    {"password": "", "description": "empty password (XAMPP default)"},
    {"password": "Happiness1524!", "description": "Happiness1524!"},
    {"password": "root", "description": "root"},
    {"password": "password", "description": "password"},
    {"password": "admin", "description": "admin"},
    {"password": "mysql", "description": "mysql"}
]

success = False
for option in passwords:
    if try_connection(option["password"], option["description"]):
        print(f"\n✅ SUCCESS with password: {option['description']}")
        print("Use this password in your application configuration.")
        success = True
        break

if not success:
    print("\n❌ All password attempts failed.")
    print("\nSuggestions:")
    print("1. Check if MySQL is running (XAMPP Control Panel)")
    print("2. Try resetting the root password")
    print("3. Check MySQL error logs for authentication issues")
    print("4. Try connecting through phpMyAdmin")

sys.exit(0 if success else 1)
