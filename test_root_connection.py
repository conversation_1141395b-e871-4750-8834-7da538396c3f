import mysql.connector
import sys

# Database configuration using root user
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': 'Happiness1524!',
    'database': 'giggenius'  # Using lowercase database name
}

def test_connection():
    try:
        # Attempt to establish a connection
        print("Attempting to connect to MySQL database as root...")
        conn = mysql.connector.connect(**db_config)
        
        # If connection is successful
        if conn.is_connected():
            print("✅ Successfully connected to MySQL database as root!")
            
            # Get server information
            db_info = conn.get_server_info()
            print(f"MySQL Server version: {db_info}")
            
            # Get cursor and execute a simple query
            cursor = conn.cursor()
            cursor.execute("SELECT DATABASE();")
            db_name = cursor.fetchone()[0]
            print(f"Connected to database: {db_name}")
            
            # Check if GigGenius user exists
            print("\nChecking if GigGenius user exists:")
            cursor.execute("SELECT User, Host FROM mysql.user WHERE User = 'GigGenius';")
            users = cursor.fetchall()
            
            if users:
                print("GigGenius user exists:")
                for user in users:
                    print(f"- User: {user[0]}, Host: {user[1]}")
                
                # Check GigGenius user privileges
                print("\nChecking GigGenius user privileges:")
                cursor.execute("SHOW GRANTS FOR 'GigGenius'@'localhost';")
                grants = cursor.fetchall()
                
                for grant in grants:
                    print(f"- {grant[0]}")
            else:
                print("GigGenius user does not exist!")
                
            cursor.close()
            conn.close()
            return True
            
    except mysql.connector.Error as e:
        print(f"❌ Error connecting to MySQL database: {e}")
        return False

if __name__ == "__main__":
    success = test_connection()
    sys.exit(0 if success else 1)
