<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Submit Job - GigGenius</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --accent-color: #3b82f6;
            --light-color: #f3f4f6;
            --dark-color: #1f2937;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
        }

        body {
            font-family: 'Poppins', sans-serif;
            color: var(--dark-color);
            background-color: #f9fafb;
        }

        .navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 15px 0;
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color);
            font-size: 1.5rem;
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background-color: white;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
            z-index: 1000;
            padding-top: 80px;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--dark-color);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .sidebar-menu a:hover, .sidebar-menu a.active {
            background-color: var(--light-color);
            color: var(--primary-color);
            border-left-color: var(--primary-color);
        }

        .sidebar-menu i {
            margin-right: 10px;
            font-size: 1.2rem;
            width: 20px;
            text-align: center;
        }

        .main-content {
            margin-left: 250px;
            padding: 80px 20px 20px;
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 15px 20px;
            font-weight: 600;
        }

        .card-body {
            padding: 20px;
        }

        .form-label {
            font-weight: 500;
            margin-bottom: 8px;
        }

        .form-control, .form-select {
            padding: 10px 15px;
            border-radius: 8px;
            border: 1px solid #d1d5db;
            font-size: 0.95rem;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            padding: 10px 20px;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
        }

        .btn-outline-secondary {
            color: var(--dark-color);
            border-color: #d1d5db;
            padding: 10px 20px;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-outline-secondary:hover {
            background-color: var(--light-color);
            color: var(--dark-color);
            border-color: #d1d5db;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand ms-4" href="#">
                <i class="fas fa-bolt me-2"></i>
                GigGenius
            </a>
            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <a href="#" class="d-flex align-items-center text-dark text-decoration-none dropdown-toggle" id="userDropdown" data-bs-toggle="dropdown">
                        <img src="https://ui-avatars.com/api/?name={{ session.get('first_name', ' ')[0] }}{{ session.get('last_name', ' ')[0] }}&background=2563eb&color=fff" alt="User" class="rounded-circle me-2" style="width: 40px; height: 40px;">
                        <span>{{ session.get('first_name', '') }}</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i> My Profile</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i> Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="sidebar">
        <ul class="sidebar-menu">
            <li>
                <a href="{{ url_for('client_page') }}">
                    <i class="fas fa-home"></i> Dashboard
                </a>
            </li>
            <li>
                <a href="#" class="active">
                    <i class="fas fa-briefcase"></i> My Jobs
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-user-tie"></i> Find Talent
                </a>
            </li>
            <li>
                <a href="{{ url_for('chat') }}">
                    <i class="fas fa-comments"></i> Messages
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-credit-card"></i> Billing
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-user"></i> Profile
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-cog"></i> Settings
                </a>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Post a New Job</h1>
                <a href="{{ url_for('client_page') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Dashboard
                </a>
            </div>

            <div class="card">
                <div class="card-header">
                    Job Details
                </div>
                <div class="card-body">
                    <form id="jobSubmissionForm">
                        <div class="mb-3">
                            <label for="title" class="form-label">Job Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" required>
                            <div class="form-text">A clear, concise title that describes the job</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Job Description <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="6" required></textarea>
                            <div class="form-text">Detailed description of the job, requirements, and expectations</div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="">Select a category</option>
                                    <option value="Web Development">Web Development</option>
                                    <option value="Mobile Development">Mobile Development</option>
                                    <option value="Design">Design</option>
                                    <option value="Writing">Writing</option>
                                    <option value="Marketing">Marketing</option>
                                    <option value="Data Science">Data Science</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="specialty" class="form-label">Specialty</label>
                                <input type="text" class="form-control" id="specialty" name="specialty">
                                <div class="form-text">E.g., React, Python, Logo Design, etc.</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="project_size" class="form-label">Project Size</label>
                                <select class="form-select" id="project_size" name="project_size">
                                    <option value="">Select project size</option>
                                    <option value="Small">Small</option>
                                    <option value="Medium">Medium</option>
                                    <option value="Large">Large</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="duration" class="form-label">Expected Duration</label>
                                <select class="form-select" id="duration" name="duration">
                                    <option value="">Select duration</option>
                                    <option value="Less than 1 week">Less than 1 week</option>
                                    <option value="1-2 weeks">1-2 weeks</option>
                                    <option value="2-4 weeks">2-4 weeks</option>
                                    <option value="1-3 months">1-3 months</option>
                                    <option value="3-6 months">3-6 months</option>
                                    <option value="6+ months">6+ months</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="budget_type" class="form-label">Budget Type</label>
                                <select class="form-select" id="budget_type" name="budget_type">
                                    <option value="">Select budget type</option>
                                    <option value="Fixed">Fixed</option>
                                    <option value="Hourly">Hourly</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="budget_amount" class="form-label">Budget Amount</label>
                                <input type="text" class="form-control" id="budget_amount" name="budget_amount">
                                <div class="form-text">Enter amount in USD (e.g., 500 or 25/hr)</div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="job_type" class="form-label">Job Type</label>
                            <select class="form-select" id="job_type" name="job_type">
                                <option value="">Select job type</option>
                                <option value="One-time project">One-time project</option>
                                <option value="Ongoing work">Ongoing work</option>
                                <option value="Contract to hire">Contract to hire</option>
                            </select>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-outline-secondary me-2" onclick="window.location.href='{{ url_for('client_page') }}'">Cancel</button>
                            <button type="submit" class="btn btn-primary">Post Job</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const jobForm = document.getElementById('jobSubmissionForm');

            jobForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Get form data
                const formData = new FormData(jobForm);
                const submitButton = jobForm.querySelector('button[type="submit"]');

                // Disable button and show loading state
                submitButton.disabled = true;
                submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> Submitting...';

                // Convert FormData to JSON
                const jsonData = {};
                formData.forEach((value, key) => {
                    jsonData[key] = value;
                });

                // Submit the form
                fetch('/submit_job', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(jsonData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        alert('Job posted successfully!');
                        // Redirect to client dashboard
                        window.location.href = '{{ url_for('client_page') }}';
                    } else {
                        // Show error message
                        alert('Error: ' + (data.error || 'An error occurred'));
                        // Reset button
                        submitButton.disabled = false;
                        submitButton.innerHTML = 'Post Job';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                    // Reset button
                    submitButton.disabled = false;
                    submitButton.innerHTML = 'Post Job';
                });
            });
        });
    </script>
</body>
</html>
